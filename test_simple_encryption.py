#!/usr/bin/env python3
"""
Simple test for encryption functionality
"""
import json
import hashlib
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes


class SimpleEncryptionManager:
    def __init__(self, kms_master_key: bytes):
        self._kms_master_key = kms_master_key

    def encrypt(self, data: bytes) -> bytes:
        nonce = Fernet.generate_key()[:12]
        cipher = Cipher(algorithms.AES(self._kms_master_key), modes.GCM(nonce))
        encryptor = cipher.encryptor()
        ciphertext = encryptor.update(data) + encryptor.finalize()
        return nonce + encryptor.tag + ciphertext

    def decrypt(self, encrypted_data: bytes) -> bytes:
        nonce = encrypted_data[:12]
        tag = encrypted_data[12:28]
        ciphertext = encrypted_data[28:]
        cipher = Cipher(algorithms.AES(self._kms_master_key), modes.GCM(nonce, tag))
        decryptor = cipher.decryptor()
        return decryptor.update(ciphertext) + decryptor.finalize()

    def encrypt_conversation(self, conversation_data: list) -> bytes:
        conversation_json = json.dumps(conversation_data, ensure_ascii=False)
        conversation_bytes = conversation_json.encode('utf-8')
        return self.encrypt(conversation_bytes)

    def decrypt_conversation(self, encrypted_data: bytes) -> list:
        decrypted_bytes = self.decrypt(encrypted_data)
        conversation_json = decrypted_bytes.decode('utf-8')
        return json.loads(conversation_json)

    def generate_conversation_hash(self, conversation_data: list) -> str:
        conversation_json = json.dumps(conversation_data, ensure_ascii=False, sort_keys=True)
        conversation_bytes = conversation_json.encode('utf-8')
        return hashlib.sha256(conversation_bytes).hexdigest()


def test_encryption():
    print("=== Simple Encryption Test ===\n")
    
    # Initialize encryption manager
    kms_master_key = b'12345678901234567890123456789012'  # Exactly 32 bytes
    print(f"Key length: {len(kms_master_key)} bytes")
    encryption_manager = SimpleEncryptionManager(kms_master_key)
    
    # Test conversation data
    conversation_data = [
        {
            "role": "user",
            "content": "What is the law regarding contract disputes?",
            "timestamp": "2024-01-01T10:00:00",
            "index": 1
        },
        {
            "role": "assistant", 
            "content": "Contract disputes are governed by several legal principles...",
            "timestamp": "2024-01-01T10:00:30",
            "index": 1
        }
    ]
    
    print("1. Testing conversation encryption...")
    encrypted_data = encryption_manager.encrypt_conversation(conversation_data)
    print(f"   ✓ Encrypted data length: {len(encrypted_data)} bytes")
    
    print("2. Testing conversation decryption...")
    decrypted_data = encryption_manager.decrypt_conversation(encrypted_data)
    print(f"   ✓ Decrypted {len(decrypted_data)} conversation items")
    
    print("3. Testing data integrity...")
    if conversation_data == decrypted_data:
        print("   ✓ Data integrity verified - content matches")
    else:
        print("   ✗ Data integrity failed - content mismatch")
        return False
    
    print("4. Testing hash generation...")
    hash1 = encryption_manager.generate_conversation_hash(conversation_data)
    hash2 = encryption_manager.generate_conversation_hash(decrypted_data)
    print(f"   Original hash: {hash1[:16]}...")
    print(f"   Decrypted hash: {hash2[:16]}...")
    
    if hash1 == hash2:
        print("   ✓ Hash verification passed")
    else:
        print("   ✗ Hash verification failed")
        return False
    
    print("\n🎉 All encryption tests passed successfully!")
    print("\nEncryption functionality is working correctly:")
    print("✓ Conversations can be encrypted and decrypted")
    print("✓ Data integrity is maintained through encryption/decryption cycle")
    print("✓ Hash generation works for integrity verification")
    
    return True


if __name__ == "__main__":
    test_encryption()
