import re

from api.dependencies.mongo_db import get_mongo_client
from configs.app_config import MON<PERSON>O_DB_AUTOCOMPLETE, MONGO_PROCEDURE_COLLECTION, MONGO_INDEX_AUTOCOMPLETE
from utils.dto import <PERSON><PERSON><PERSON>


def extract_last_two_parts(query):
    words = query.split()  # Split the sentence by spaces
    last_two = words[-2:] if len(
        words) >= 2 else words  # Select the last two elements (or the whole sentence if less than 2)

    # Cleaning - keep only Hebrew letters
    pt_clean = ["".join(re.findall(r"[א-ת]", word)) for word in last_two]
    pt_clean = [word for word in pt_clean if word]  # Filter out empty results

    # Cleaning - keep only numbers
    pn_clean = ["".join(re.findall(r"\d", word)) for word in last_two]
    pn_clean = [word for word in pn_clean if word]  # Filter out empty results

    if len(pt_clean) == 2 and not pn_clean:  # Both are words only
        pt_clean = [pt_clean[-1]]
    if len(pn_clean) == 2 and not pt_clean:  # Both are numbers only
        pn_clean = [pn_clean[-1]]

    # Return only the element, not a list
    return (pt_clean[0] if pt_clean else '', pn_clean[0] if pn_clean else '')


def get_search_case_number(query, source):
    tribunal = 1 if source == SourceEnum.ethics else None
    # pt_clean, pn_clean = extract_last_two_parts(query)
    pn_clean = re.sub(r'[^0-9]', '', query)
    pt_clean = re.sub(r'[^א-ת]', '', query)
    minimumShouldMatch = 0
    if not pn_clean and not pt_clean:
        return []

    template_pipe = [
        {
            "$search": {
                "index": MONGO_INDEX_AUTOCOMPLETE,  # The index name
                "compound": {
                    "should": [],
                    "minimumShouldMatch": minimumShouldMatch
                }
            }
        },
        {
            "$limit": 10  # Limit the number of results
        },
        {
            "$project": {
                "_id": 1,  # Exclude _id from results
                "title": 1,  # Include title
                "procedures_objects": {
                    "$filter": {
                        "input": "$procedures_objects",  # Field containing the array to filter
                        "as": "procedure",  # Temporary variable for each item in the array
                        "cond": {
                            "$and": [
                                {
                                    "$regexMatch": {
                                        "input": "$$procedure.pn_normalized",
                                        # Accessing the pn_normalized of the procedure
                                        "regex": f"^{pn_clean}",  # Check if pn_normalized starts with pn_clean
                                        "options": "i"  # Case-insensitive match (if required)
                                    }
                                },
                                {
                                    "$regexMatch": {
                                        "input": "$$procedure.pt",  # Accessing the pt of the procedure
                                        "regex": f"^{pt_clean}",  # Check if pt starts with pt_clean
                                        "options": "i"  # Case-insensitive match (if required)
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        }
    ]

    if pn_clean:
        template_pipe[0]["$search"]["compound"]["should"].append({
            "autocomplete": {
                "query": pn_clean,  # The search term
                "path": "procedures_objects.pn_normalized"  # Autocomplete on pn_normalized field
            }
        })
        minimumShouldMatch += 1
    if pt_clean:
        template_pipe[0]["$search"]["compound"]["should"].append({
            "autocomplete": {
                "query": pt_clean,  # The search term
                "path": "procedures_objects.pt"  # Autocomplete on pt field
            }
        })
        minimumShouldMatch += 1

    template_pipe[0]["$search"]["compound"]["minimumShouldMatch"] = minimumShouldMatch

    if tribunal:
        template_pipe.insert(1, {
            "$match": {
                "tribunal_id": 1
            }
        })
    return template_pipe


##create function that get the pipeline and return the results
async def get_search_results(mongo_client, pipeline):
    suggestions = []
    if not pipeline:
        return []
    collection = mongo_client[MONGO_DB_AUTOCOMPLETE][MONGO_PROCEDURE_COLLECTION]
    search_results = await collection.aggregate(pipeline).to_list(10)  # Already returns a list

    for result in search_results:
        procedures = result.get("procedures_objects", [])
        suggestions.extend([{
            "txtId": result.get("_id"),
            "procedure_type": procedure.get("pt", ""),
            "procedure_number": procedure.get("pn", ""),
            "decision_name": procedure.get("dn", ""),
            "title": result.get("title", ""),
        } for procedure in procedures])

    return suggestions


async def main():
    mongo_connection = await get_mongo_client()
    try:
        db = mongo_connection[MONGO_DB_AUTOCOMPLETE]
        pipeline = get_search_case_number("גל בגצ", SourceEnum.ethics)
        if not pipeline:
            return []
        result = await db[MONGO_PROCEDURE_COLLECTION].aggregate(pipeline).to_list(None)  # Get all results
        import json
        print(json.dumps(result, indent=4, ensure_ascii=False))
        return result
    finally:
        mongo_connection.close()


if __name__ == '__main__':
    import asyncio

    asyncio.run(main())
