from utils.dto import ListItem
from typing import Any

from datetime import datetime
from pydantic import BaseModel, Field
from typing import List, Optional

from utils.pre_chat_dto import ListChunkContext
from utils.datetime_utils import timestamp_now_il


class ReturnRelatedChunksRequest(BaseModel):
    search_analytics_post_chat: Optional[Any] = None
    user_id: str
    chat_id: str
    last_q_and_a: str
    cohere_client: Any
    law_index: Any
    verdict_index: Any
    books_summaries_index: Any
    max_results: int
    embedding: Any
    ai_provider: Any


class PostChatRequest(BaseModel):
    chatId: str
    userId: str
    domain: str


class PostChatResponse(BaseModel):
    related: List[ListItem]
    statusCode: int
    message: Optional[str] = None
    statistics: Optional[str] = None


class PostChatRelatedRequest(BaseModel):
    chat_id: str
    user_id: str
    index: int
    redis_pool: Any
    cohere_client: Any
    law_index: Any
    verdict_index: Any
    books_summaries_index: Any
    query_statistics_results: Any
    domain: str
    ai_provider: Any





def convert_list_items_to_chunks_context(list_items: List[Any]):
    results = []

    for item in list_items:
        try:
            results.append(
                ListChunkContext(
                    docId=item.get("cId") or item.get("txtId"),
                    type=item.get("source"),
                    score=item.get("doc_score",0)))

        except Exception as e:
            print(f"Error converting list item to ChatChunkContext: {e}")
    sorted(results, key=lambda x: x.score, reverse=True)
    return results


class PostChatAnalytics(BaseModel):
    user_id: str
    chat_id: str
    timestamp: datetime = Field(default_factory=timestamp_now_il)
    chunks_context: List[ListChunkContext] = []
    query_index: int
    search_alpha: float
