import concurrent.futures
import json
import re
from time import time

from configs import app_config as conf
from data_ops.pinecone_func import search_query_in_multiple_indices, \
    get_combined_results_by_filter_dicts_and_by_percents
from db_utils.sql_helper import get_procedures_txt_ids_metadata_by_procedure_number
from middlewares.logging_utils import app_logger as logger, log_format
from utils.constants_util import MIN_QUERY_LEN_WITH_VALID_AND_NOT_VALID_PN_IN_SEARCH
from utils.dto import ExtractTextEmbeddingReuquest, ActionsEnum, SearchMultipleIndicesRequest, TypeEnum
from services.search.search_dto import SearchPineconeByQueryRequest
from utils.pinecone_utli import get_type, normalize_score


async def res_no_procedure(procedure_number, user_id, search_analytics, search_id, search_statistics_results,
                           cohere_client, query, law_index, verdict_index, books_summaries_index, max_results, filters,
                           has_ner, procedure, user_query_b_pers_entities, query_len, ai_provider, domain=None):
    is_res = False
    search_in_pinecone_list = []
    if procedure_number:
        start = time()
        res = get_procedures_txt_ids_metadata_by_procedure_number(procedure_number)
        if domain==TypeEnum.ethics:
            res= list(filter(lambda x: x['metadata']['tribunal_id'] == 1, res))
            if not res:
                raise Exception('PROCEDURE_NUMBER_IS_NOT_ETHICS')

        if res:  # valid pn
            if query_len < MIN_QUERY_LEN_WITH_VALID_AND_NOT_VALID_PN_IN_SEARCH:
                sorted(res, key=lambda x: x['metadata']['decision_name'] != 'פסק דין')
                logger.info(log_format({'userId': user_id, 'Type': ActionsEnum.search,
                                        'Action': 'get_procedures_txt_ids_metadata_by_procedure_number', 'id': '',
                                        'Time': time() - start, 'Data': json.dumps([f.dict() for f in filters]),
                                        'text': 'query', 'numbering': 6}))
            else:
                search_in_pinecone_list.append(
                    {"type": "procedure_numbers", "value": procedure_number, "operation": "$in",
                     "percents": int(round(max_results / 2))})
                search_in_pinecone_list.append(
                    {"type": "procedure_numbers", "value": procedure_number, "operation": "$nin",
                     "percents": int(round(max_results / 2))})
                res = await get_pinecone_results_by_filter_dicts_and_by_percents(search_analytics, user_id, search_id,
                                                                                 cohere_client,
                                                                                 search_statistics_results, query,
                                                                                 max_results, search_in_pinecone_list,
                                                                                 verdict_index)
        else:  # not valid pn
            if query_len < conf.MIN_QUERY_LEN:
                raise Exception('PROCEDURE_NOT_EXIST')
            is_res = True

    if not procedure_number or is_res:
        search_pinecone_by_query_request = SearchPineconeByQueryRequest(search_analytics=search_analytics,
                                                                        user_id=user_id,
                                                                        search_id=search_id,
                                                                        search_statistics_results=search_statistics_results,
                                                                        cohere_client=cohere_client,
                                                                        query=query,
                                                                        law_index=law_index,
                                                                        verdict_index=verdict_index,
                                                                        books_summaries_index=books_summaries_index,
                                                                        max_results=max_results,
                                                                        filters=filters,
                                                                        has_ner=has_ner,
                                                                        procedure=procedure,
                                                                        ai_provider=ai_provider, domain=domain)
        res = await search_pinecone_by_query(search_pinecone_by_query_request)
        # TODO DANIEL process_search_result search on empty text field since the update of the text happens only after in line 651
        if has_ner:
            res = process_results_if_ner_in_query(user_id, filters, user_query_b_pers_entities, res)

    return res


async def get_pinecone_results_by_filter_dicts_and_by_percents(search_analytics, user_id, search_id, cohere_client,
                                                               search_statistics_results, query, max_results,
                                                               search_in_pinecone_list, verdict_index):
    from data_ops.embedding_helper import extract_text_embedding

    extract_text_embedding_request = ExtractTextEmbeddingReuquest(search_analytics_pre_chat=search_analytics,
                                                                  user_id=user_id,
                                                                  chat_id=search_id,
                                                                  cohere_client=cohere_client,
                                                                  query_statistics_results=search_statistics_results,
                                                                  text_to_embed=query)
    start = time()
    embedding = await extract_text_embedding(extract_text_embedding_request)
    embed_time = time() - start
    search_analytics.embedTime = embed_time
    combined_results_by_filter_dicts_and_by_percents = get_combined_results_by_filter_dicts_and_by_percents(
        search_in_pinecone_list, verdict_index, embedding, max_results, True, search_statistics_results)
    rerank_time = time()
    first_item = combined_results_by_filter_dicts_and_by_percents.pop(0)
    sorted_combined = sorted(combined_results_by_filter_dicts_and_by_percents, key=lambda x: x['score'], reverse=True)
    sorted_combined.insert(0, first_item)
    search_analytics.rerankTime = time() - rerank_time
    search_analytics.rerankResults = [{"docId": re.get('metadata').get('cid') or re.get('id'), "docType": get_type(re)}
                                      for re in sorted_combined]

    return sorted_combined


async def search_pinecone_by_query(request: SearchPineconeByQueryRequest):
    search_analytics = request.search_analytics
    user_id = request.user_id
    search_id = request.search_id
    search_statistics_results = request.search_statistics_results
    cohere_client = request.cohere_client
    query = request.query
    law_index = request.law_index
    verdict_index = request.verdict_index
    books_summaries_index = request.books_summaries_index
    max_results = int(request.max_results)
    filters = request.filters
    has_ner = request.has_ner
    procedure = request.procedure or None
    ai_provider = request.ai_provider
    domain = request.domain

    start = time()

    search_request: SearchMultipleIndicesRequest = {"query": query,
                                                    "cohere_client": cohere_client,
                                                    "law_index": law_index,
                                                    "verdict_index": verdict_index,
                                                    "books_summaries_index": books_summaries_index,
                                                    "max_results": max_results,
                                                    "filters": filters,
                                                    "consolidate_source_docs": True,
                                                    "search_analytics": search_analytics,
                                                    "query_statistics_results": search_statistics_results,
                                                    "procedure": procedure,
                                                    "has_ner": has_ner,
                                                    "is_search": True,
                                                    "user_id": user_id,
                                                    "chat_id": search_id,
                                                    "ai_provider": ai_provider,
                                                    "domain": domain
                                                    }

    res, ner_time = await search_query_in_multiple_indices(search_request)
    logger.info(log_format(
        {'userId': user_id, 'Type': ActionsEnum.search, 'Action': 'search_query_in_multiple_indices', 'id': '',
         'Time': time() - start, 'Data': json.dumps([f.dict() for f in filters]), 'text': 'query', 'numbering': 6}))
    return res


def process_results_if_ner_in_query(user_id, filters, user_query_b_pers_entities, res):
    start = time()
    with concurrent.futures.ThreadPoolExecutor() as executor:
        futures = [executor.submit(process_search_result, ser_res, user_query_b_pers_entities) for ser_res in res]
        res = [future.result() for future in concurrent.futures.as_completed(futures)]
    logger.info(log_format(
        {'userId': user_id, 'Type': ActionsEnum.search, 'Action': 'process_search_result - ner', 'id': '',
         'Time': time() - start, 'Data': json.dumps([f.dict() for f in filters]), 'text': 'query', 'numbering': 7}))
    return res


def normalize_text(text):
    # Normalize text by replacing commas, periods, and quotes with a space, and collapsing multiple spaces into one
    text = re.sub(r"[,.'\"“”]", " ", text)
    text = re.sub(r"\s+", " ", text)
    return text.strip()


def match_abbreviation(entity, text):
    # Generate an abbreviation pattern from the entity
    entity_parts = entity.split()
    if len(entity_parts) >= 2:
        abbrev_pattern = entity_parts[0][0] + ".*" + entity_parts[-1]
    else:
        return False  # If there's no abbreviation possible, return False

    # Search for a match in text using the generated pattern
    match = re.search(abbrev_pattern, normalize_text(text))
    return match is not None


def find_matches(user_query_b_pers_entities, text, combined_names):
    matched_entities = set()
    normalized_text = normalize_text(text)

    for entity in user_query_b_pers_entities:
        # Ensure entity is longer than one character
        if len(entity) > 1:
            # Check direct match in text for the entity
            if re.search(re.escape(entity) + r'\b', normalized_text):
                matched_entities.add(entity)

            if match_abbreviation(entity, text):
                matched_entities.add(entity)

            # Additionally, check direct and abbreviation matches with combined names
            for name in combined_names:
                normalized_name = normalize_text(name)
                if re.search(re.escape(entity) + r'\b', normalized_name) or match_abbreviation(entity, name):
                    matched_entities.add(entity)

    return matched_entities


def process_search_result(ser_res, user_query_b_pers_entities):
    metadata = ser_res.get('metadata', {})
    text = metadata.get('text', metadata.get('summary_text', '') or metadata.get('provision_text', ''))

    matched_ner_names = []
    combined_names = set()
    # Lists to hold the extracted names
    judges = metadata.get('judges', '').split(", ") if isinstance(metadata.get('judges', ''), str) else metadata.get(
        'judges', [])
    prosecutors = metadata.get('prosecutors', '').split(", ") if isinstance(metadata.get('prosecutors', ''),
                                                                            str) else metadata.get('prosecutors', [])
    defenders = metadata.get('defenders', '').split(", ") if isinstance(metadata.get('defenders', ''),
                                                                        str) else metadata.get('defenders', [])
    representatives = metadata.get('representatives', '').split(", ") if isinstance(metadata.get('representatives', ''),
                                                                                    str) else metadata.get(
        'representatives', [])

    # Combine all the names into a single set, filtering out any that don't meet the criteria
    if user_query_b_pers_entities:
        for sublist in [judges, prosecutors, defenders, representatives]:
            for name in sublist:
                if len(name) > 2 and not re.search(r'\d', name) and name != "||":
                    combined_names.add(name)

        matched_ner_names = find_matches(user_query_b_pers_entities, text, combined_names)
        metadata['matched_ner_names'] = list(matched_ner_names)

    return {
        "metadata": {
            'txtId': int(metadata.get('txt_id') or metadata.get('txtId') or metadata.get('docId', -1)),
            'cId': str(metadata.get('cid') or metadata.get('cId') or metadata.get('id') or (
                metadata.get('txt_id') + '-0' if metadata.get('txt_id') else '0')),
            'source': metadata.get('source', ''),
            'doc_score': ser_res.get('score', 0.0),
            'chunk_score': ser_res.get('chunk_score', 0.0),
            'normalized_score': normalize_score(ser_res.get('chunk_score', 0.0)),
            'title': metadata.get('title', ''),
            'sectionNumbers': metadata.get('section_numbers', []),
            'subject': metadata.get('subject', ''),
            'text': text,
            'legislation_id': metadata.get('legislation_id', ''),
            'chapter_title': metadata.get('chapter_title', ''),
            'provision_title': metadata.get('provision_title', ''),
            'amendment_information': metadata.get('amendment_information', ''),
            'prosecutors': metadata.get('prosecutors', []),
            'defenders': metadata.get('defenders', []),
            'judges': judges,
            'representatives': metadata.get('representatives', []),
            'court_name': metadata.get('court_name', ''),
            'procedures': metadata.get('procedures', []),
            'show_date': float(metadata.get('show_date', 0)),
            'location': metadata.get('location', ''),
            'procedure_type': metadata.get('procedure_type', []),
            'pages': int(metadata.get('pages', 0)),
            'decision_name': metadata.get('decision_name', ''),
            'ref_count': int(metadata.get('ref_count', 0)),
            'procedure_numbers': metadata.get('procedure_numbers', []),
            'create_date': str(metadata.get('create_date', '')),
            'legal_field_book': metadata.get('legal_field_book', ''),
            'combined_names': list(combined_names),
            'matched_ner_names': matched_ner_names,
            'tribunal_id': metadata.get('tribunal_id', 0),
        }
    }
