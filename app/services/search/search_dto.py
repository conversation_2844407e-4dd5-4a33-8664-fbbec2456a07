from datetime import datetime
from enum import Enum
from typing import Any, Optional, List

from pydantic import BaseModel, Field

from utils.dto import ListItem, Filter, SearchStatistics
from utils.pre_chat_dto import ChatChunkContext, ListChunkContext
from utils.datetime_utils import timestamp_now_il


class SearchRequestDTO(BaseModel):
    search_analytics: Any
    user_id: str
    search_id: str
    search_statistics_results: Optional[SearchStatistics]
    cohere_client: Any
    query: str
    law_index: Any
    verdict_index: Any
    books_summaries_index: Any
    max_results: int
    filters: List[Filter]
    filters_formula: Optional[str]
    ai_provider: Any
    index:int


class SearchPineconeByQueryRequest(BaseModel):
    search_analytics: Any
    user_id: str
    search_id: str
    search_statistics_results: Any
    cohere_client: Any
    query: str
    law_index: Any
    verdict_index: Any
    books_summaries_index: Any
    max_results: int
    filters: List[Filter]
    has_ner: bool
    procedure: Optional[str]
    ai_provider: Any
    domain: Optional[str]


class SearchRequest(BaseModel):
    query: str
    searchId: str
    filters: List[Filter] = []
    filtersFormula: Optional[str] = None
    userId: str
    index: Optional[int]
    root: Optional[str]


class SearchResponse(BaseModel):
    statusCode: Optional[int] = 200
    message: str = ""
    list: List[ListItem]
    searchId: str





class SearchAnalyticsDto(BaseModel):
    user_id: str
    chat_id: str
    search_type: Any
    timestamp: datetime = Field(default_factory=timestamp_now_il)
    chunks_context: List[ListChunkContext] = []
    query_index: int
    query_length: int
