import traceback

from configs import app_config as conf
from services.pre_chat.data_source import PineConeQuery, DataSourceFilter
from services.pre_chat.data_source.data_source_analyzer import DataSourceAnalyzer
from services.pre_chat.helpers.regex_functions import Cleaners
from utils.pre_chat_dto import Filters, SearchTypeEnum, PreChatManager
from services.pre_chat.request_stratgey import BasePreRequestStrategy
from middlewares.logging_utils import app_logger as logger


def filter_builder(filters: Filters) -> tuple[bool, list[PineConeQuery]]:
    # TODO remove the split the long chapter_id and the exam book_id
    try:
        logger.info(f"filter_builder: Building filters for Machshavot")

        def exclude_docs():
            issues_filter = []
            for chapter_filter in conf.Book_Chapters_Ids_Filters:
                issues_filter.append(DataSourceFilter(type="chapter_id", value=chapter_filter, operation="ne"))
            if conf.Book_Exam_Id:
                issues_filter.append(DataSourceFilter(type="book_type_id", value=conf.Book_Exam_Id, operation="ne"))

            return issues_filter

        issue_filter, is_book = exclude_docs(), False
        arr = []

        if not filters or not filters.docs:
            logger.warning(f"filter_builder: No filters provided for Machshavot")
            return is_book, arr

        for Filter in filters.docs:
            if Filter.type == "legal_field_id":
                Filter.value = int(Filter.value)
            logger.info(f"filter_builder: Adding filter type={Filter.type}, value={Filter.value}")
            arr.append(
                PineConeQuery(
                    filter=[DataSourceFilter(type=Filter.type, value=Filter.value, operation="eq"), *issue_filter]))
            is_book = True if Filter.type == "book_id" else False

        logger.info(f"filter_builder: Created {len(arr)} filters, is_book={is_book}")
        return is_book, arr
    except Exception as e:
        logger.error(f"filter_builder: Error building filters: {e}, {traceback.format_exc()}")
        raise ValueError("PREPARE_CHAT_ERROR")


class MachshavotStrategy(BasePreRequestStrategy):
    def __init__(self, pre_chat_object: PreChatManager):
        super().__init__(pre_chat_object)

    def choose_prompt_template(self, is_book: bool = False):
        """Choose the appropriate prompt template based on the situation"""
        # For machshavot, we might have different templates based on whether it's a book or not
        if is_book:
            self.pre_chat_object.model_settings.prompt_template = "MACHSHAVOT_SPECIFIC_BOOK_PROMPT_TEMPLATE_V1"
            self.pre_chat_object.chat_settings.search_type = SearchTypeEnum.specific_document
            self.pre_chat_object.chat_settings.chat_type_client=ChatTypeClient.lawDocumentStrategy

        else:
            self.pre_chat_object.model_settings.prompt_template = "MACHSHAVOT_CONVERSATION_TOPIC_PROMPT_TEMPLATE_V1"

    async def handle(self):
        try:
            logger.info(f"MachshavotStrategy.handle: Processing request for chat_id={self.pre_chat_object.chatId}")

            self.pre_chat_object.query.clean_query = Cleaners.NormalizeCommonAbbreviations(
                self.pre_chat_object.query.raw_query).clean()

            # Embed query
            self.pre_chat_object.query.embed_query = await self.embed_query()

            # Build filters - logging happens inside the function
            is_book, query_list = filter_builder(self.pre_chat_object.filters)

            # Search for chunks
            machshavot_chunks = await DataSourceAnalyzer(self.pre_chat_object).search(query_list, len(query_list))

            if not machshavot_chunks:
                logger.warning(
                    f"MachshavotStrategy.handle: No machshavot chunks found for chat_id={self.pre_chat_object.chatId}")
                raise ValueError("RESULTS_NONE_MESSAGE")

            # Store the results in the pre_chat_object's data context
            self.pre_chat_object.data.data_raw = machshavot_chunks
            self.pre_chat_object.data.type = "chunks_document"

            # Choose prompt template
            self.choose_prompt_template(is_book)
            logger.info(f"MachshavotStrategy.handle: Successfully completed for chat_id={self.pre_chat_object.chatId}")

        except Exception as e:
            logger.error(
                f"MachshavotStrategy.handle: Error processing request for chat_id={self.pre_chat_object.chatId}: {e}, {traceback.format_exc()}")
            # If it's already a ValueError with a known error key, pass it through
            if isinstance(e, ValueError):
                raise
            # Otherwise, wrap it in a generic error
            raise ValueError("PREPARE_CHAT_ERROR")
