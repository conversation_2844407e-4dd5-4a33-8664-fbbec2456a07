import re
import traceback

from ai_models.citation_prompts import MACHSHAVOT_CONVERSATION_ASSISTANT_MESSAGE_TEMPLATE
from middlewares.logging_utils import app_logger as logger
from services.pre_chat.helpers.sql_helper import <PERSON><PERSON><PERSON><PERSON>per
from services.pre_chat.request_stratgey import BasePreRequestStrategy
from utils.conversation_manager import ConversationI<PERSON>, RoleEnum, ConversationEnum
from utils.pre_chat_dto import SearchType<PERSON><PERSON>, ChatTypeClient


class TrainerStrategy(BasePreRequestStrategy):
    """
    This class is used to handle trainer-specific chat interactions.
    It supports the pre_chat_assistant function and tracks the number of queries in the conversation.
    """

    def __init__(self, pre_chat_object):
        super().__init__(pre_chat_object)
        self.sql_helper = SQLHelper()

    def choose_prompt_template(self):
        """
        Choose the appropriate prompt template for trainer interactions
        """
        self.pre_chat_object.model_settings.prompt_template = "MACHSHAVOT_CONVERSATION_ASSISTANT_MESSAGE_TEMPLATE"

    def exract_filters(self):
        """
        Extracts filters from the pre_chat_object
        """
        try:
            logger.info(f"TrainerStrategy.exract_filters: Extracting filters for chat_id={self.pre_chat_object.chatId}")

            if not self.pre_chat_object.filters or not self.pre_chat_object.filters.docs:
                logger.error(f"TrainerStrategy.exract_filters: No filters provided for chat_id={self.pre_chat_object.chatId}")
                raise ValueError("PREPARE_CHAT_ERROR")

            filters = self.pre_chat_object.filters.docs
            question_id = None
            book_id = None

            for filter in filters:
                if filter.type == "question_id":
                    question_id = filter.value
                    logger.info(f"TrainerStrategy.exract_filters: Found question_id={question_id} for chat_id={self.pre_chat_object.chatId}")
                elif filter.type == "book_id":
                    book_id = filter.value
                    logger.info(f"TrainerStrategy.exract_filters: Found book_id={book_id} for chat_id={self.pre_chat_object.chatId}")

            if not question_id or not book_id:
                logger.error(f"TrainerStrategy.exract_filters: Missing required filters for chat_id={self.pre_chat_object.chatId}")
                raise ValueError("PREPARE_CHAT_ERROR")

            return question_id, book_id

        except Exception as e:
            logger.error(f"TrainerStrategy.exract_filters: Error extracting filters for chat_id={self.pre_chat_object.chatId}: {e}, {traceback.format_exc()}")
            if isinstance(e, ValueError):
                raise
            raise ValueError("PREPARE_CHAT_ERROR")

    async def handle(self):
        """
        Main handler for trainer strategy
        """
        try:
            logger.info(f"TrainerStrategy.handle: Processing request for chat_id={self.pre_chat_object.chatId}")

            if len(self.pre_chat_object.conversation) == 0:
                # New conversation
                question_id, book_id = self.exract_filters()

                answers, question_text = self.sql_helper.extract_entity_relations(question_id)
                if not answers or not question_text:
                    logger.error(f"TrainerStrategy.handle: Failed to extract entity relations for question_id={question_id}")
                    raise ValueError("PREPARE_CHAT_ERROR")

                prompt_injection = MACHSHAVOT_CONVERSATION_ASSISTANT_MESSAGE_TEMPLATE.format(
                    book_answer=answers,
                    student_answer=self.pre_chat_object.query.raw_query
                )

                # Add conversation items
                self.pre_chat_object.add_to_conversation(ConversationItem(
                    role=RoleEnum.assistant,
                    content=question_text,
                    action=ConversationEnum.PRINTABLE.value,
                    prompt=None,
                    entry_type="None",
                    index=0
                ))

                self.pre_chat_object.add_to_conversation(ConversationItem(
                    role=RoleEnum.user,
                    content=prompt_injection,
                    action=ConversationEnum.MODEL_CONVERSATION.value,
                    prompt=None,
                    entry_type="None",
                    index=0
                ))

                self.pre_chat_object.add_to_conversation(ConversationItem(
                    role=RoleEnum.user,
                    content="Provide all in Hebrew",
                    prompt=None,
                    entry_type="None",
                    action=ConversationEnum.MODEL_CONVERSATION.value
                ))

                self.pre_chat_object.add_to_conversation(ConversationItem(
                    role=RoleEnum.user,
                    content=self.pre_chat_object.query.raw_query,
                    prompt=None,
                    entry_type="answer",
                    action=ConversationEnum.PRINTABLE.value
                ))
                self.choose_prompt_template()

            else:
                # Existing conversation
                pattern = r"^(ערעור|עירעור)[:]?[\s\S]*"
                prompt=None

                if re.match(pattern, self.pre_chat_object.query.raw_query):
                    prompt = "Machshavot_APPEAL_ASSISTANT_MESSAGE_TEMPLATE"
                    self.pre_chat_object.model_settings.prompt_template=prompt

                self.pre_chat_object.add_to_conversation(ConversationItem(
                    role=RoleEnum.user,
                    content=self.pre_chat_object.query.raw_query,
                    prompt=prompt,
                    entry_type="query",
                    action=ConversationEnum.BOTH.value
                ))

            self.pre_chat_object.chat_settings.chat_type_client=ChatTypeClient.trainerStrategy

            self.pre_chat_object.chat_settings.search_type = SearchTypeEnum.trainer
            logger.info(f"TrainerStrategy.handle: Successfully completed for chat_id={self.pre_chat_object.chatId}")

        except Exception as e:
            logger.error(f"TrainerStrategy.handle: Error processing request for chat_id={self.pre_chat_object.chatId}: {e}, {traceback.format_exc()}")
            # If it's already a ValueError with a known error key, pass it through
            if isinstance(e, ValueError):
                raise
            # Otherwise, wrap it in a generic error
            raise ValueError("PREPARE_CHAT_ERROR")
