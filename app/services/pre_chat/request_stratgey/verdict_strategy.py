import asyncio
import traceback

from services.pre_chat.data_source import DataSourceFilter, PineConeQuery, VerdictChunkModel
from services.pre_chat.data_source.data_source_analyzer import DataSourceAnalyzer
from services.pre_chat.helpers.agent_ner import agent_ner
from services.pre_chat.helpers.regex_functions import Cleaners
from services.pre_chat.helpers.verdict_helper import multi_procedure_extraction, get_txt_id_by_procedure
from utils.pre_chat_dto import PreChat<PERSON>anager, ChatEnum
from services.pre_chat.request_stratgey import BasePreRequestStrategy, SpecificDocumentStrategy
from middlewares.logging_utils import app_logger as logger


def filter_builder(document_ids: list) -> tuple[bool, list[PineConeQuery]]:
    """
    Build Pinecone filters for verdict searches
    Returns: (is_specific_type, list of PineConeQuery)
    """
    query_list = []
    the_rami_query = []
    filter_not = False
    for doc_id in document_ids:
        query_list.append(PineConeQuery(
            filter=[DataSourceFilter(type="txt_id", value=doc_id, operation="eq")]))
        the_rami_query.append(DataSourceFilter(type="txt_id", value=doc_id, operation="ne"))

    if not query_list:
        query_list.append(PineConeQuery(
            filter=[DataSourceFilter(type="court_name", value="עליון", operation="eq")]))
        query_list.append(PineConeQuery(
            filter=[DataSourceFilter(type="court_name", value="עליון", operation="ne")]))
    else:
        query_list.append(PineConeQuery(
            filter=the_rami_query))
        filter_not = True

    return filter_not, query_list


class VerdictStrategy(BasePreRequestStrategy):

    def __init__(self, pre_chat_object: PreChatManager):
        super().__init__(pre_chat_object)

    async def handle(self):
        try:
            logger.info(f"VerdictStrategy.handle: Processing request for chat_id={self.pre_chat_object.chatId}")

            self.pre_chat_object.query.clean_query = Cleaners.NormalizeCommonAbbreviations(
                self.pre_chat_object.query.raw_query).clean()

            # Extract procedures from query - logging happens inside the function
            suspicious_documents = multi_procedure_extraction(self.pre_chat_object.query.raw_query)

            # Run embedding, NER, and get verdict IDs - logging happens inside the functions
            results = await asyncio.gather(
                self.embed_query(),
                agent_ner(self.pre_chat_object.query.raw_query, self.pre_chat_object.chatId),
                get_txt_id_by_procedure(suspicious_documents, self.pre_chat_object.connection_source.mongo_client,
                                        self.pre_chat_object.chatId),
                return_exceptions=False
            )

            if isinstance(results[0], Exception):
                raise ValueError("EMBED_TEXTS_ERROR")
            self.pre_chat_object.query.embed_query = results[0]
            self.pre_chat_object.query.ner = results[1]
            self.pre_chat_object.chat_settings.documents = results[2]

            # Build filters
            filter_not, query_list = filter_builder(self.pre_chat_object.chat_settings.documents)

            # Get search results as VerdictChunkModel objects with text from SQL
            verdict_chunks = await DataSourceAnalyzer(self.pre_chat_object).search(query_list, len(query_list),
                                                                                   filter_not)


            all_txt_ids = set([result.txt_id for result in verdict_chunks])

            self.pre_chat_object.data.master_chunks = self.sql_helper.get_masters_chunk_row_text(list(all_txt_ids))
            self.pre_chat_object.data.data_raw = verdict_chunks
            self.pre_chat_object.data.type = "chunks_document"
            self.pre_chat_object.model_settings.prompt_template = "VERDICT_CONVERSATION_PROMPT_TEMPLATE_V1"

            logger.info(f"VerdictStrategy.handle: Successfully completed for chat_id={self.pre_chat_object.chatId}")

        except Exception as e:
            logger.error(
                f"VerdictStrategy.handle: Error processing request for chat_id={self.pre_chat_object.chatId}: {e}, {traceback.format_exc()}")
            # If it's already a ValueError with a known error key, pass it through
            if isinstance(e, ValueError):
                raise
            # Otherwise, wrap it in a generic error
            raise ValueError("PREPARE_CHAT_ERROR")


class VerdictDocumentStrategy(SpecificDocumentStrategy):

    async def handle(self):
        try:
            logger.info(
                f"VerdictDocumentStrategy.handle: Processing specific document request for chat_id={self.pre_chat_object.chatId}, doc_id={self.doc_id}")

            # Get metadata
            meta_data = self.sql_helper.get_maagar_id_and_total_tokens_for_txt_id(self.doc_id)
            if not meta_data:
                logger.error(f"VerdictDocumentStrategy.handle: No metadata found for doc_id={self.doc_id}")
                raise ValueError("GET_FULL_TEXT_ERROR")

            token_count = meta_data.total_tokens

            if token_count < self.max_tokens:
                # Get full document
                full_document = self.get_full_document()
                if not full_document:
                    logger.error(
                        f"VerdictDocumentStrategy.handle: Failed to get full document for doc_id={self.doc_id}")
                    raise ValueError("GET_FULL_TEXT_ERROR")

                self.pre_chat_object.data.full_text_content = full_document
                verdict_meta_data = VerdictChunkModel(
                    cid=str(self.doc_id),
                    id=str(self.doc_id),
                    pages=meta_data.pages,
                    txt_id=self.doc_id,
                    title=meta_data.title,
                    show_date=meta_data.show_date,
                    location=meta_data.location,
                    judges=meta_data.judges,
                    prosecutors=meta_data.prosecutors,
                    defenders=meta_data.defenders,
                    representatives=meta_data.representatives,
                    court_name=meta_data.court_name,
                    ref_count=meta_data.referrer_count,
                    decision_name=meta_data.decision_name,
                    procedures=meta_data.procedures,
                    procedure_type=meta_data.procedure_type,
                    tribunal_id=meta_data.tribunal_id
                )
                self.pre_chat_object.data.data_raw = [verdict_meta_data]
                self.pre_chat_object.data.type = "full_document"
            else:
                # Document too large, get chunks
                self.pre_chat_object.query.clean_query = Cleaners.NormalizeCommonAbbreviations(
                    self.pre_chat_object.query.raw_query).clean()

                # Run embedding and NER - logging happens inside the functions
                results = await asyncio.gather(
                    self.embed_query(),
                    agent_ner(self.pre_chat_object.query.raw_query, self.pre_chat_object.chatId))
                self.pre_chat_object.query.embed_query = results[0]
                self.pre_chat_object.query.ner = results[1]

                query_list = self.get_query_for_spesfic_document()
                self.pre_chat_object.data.data_raw = await DataSourceAnalyzer(self.pre_chat_object).search(query_list,
                                                                                                           len(query_list))

                if not self.pre_chat_object.data.data_raw:
                    logger.error(f"VerdictDocumentStrategy.handle: No chunks found for doc_id={self.doc_id}")
                    raise ValueError("RESULTS_NONE_MESSAGE")

                self.pre_chat_object.data.master_chunks = self.sql_helper.get_masters_chunk_row_text([self.doc_id])

                ###need to import 3 first and last cids

                self.pre_chat_object.data.type = "chunks_document"

            self.pre_chat_object.chat_settings.return_nearest_and_related = False
            self.pre_chat_object.chat_settings.chat_type = ChatEnum.specific_document
            self.pre_chat_object.model_settings.prompt_template = "VERDICT_FULL_TEXT_PROMPT_TEMPLATE_V1"


            logger.info(
                f"VerdictDocumentStrategy.handle: Successfully completed for chat_id={self.pre_chat_object.chatId}")

        except Exception as e:
            logger.error(
                f"VerdictDocumentStrategy.handle: Error processing request for chat_id={self.pre_chat_object.chatId}, doc_id={self.doc_id}: {e}, {traceback.format_exc()}")
            # If it's already a ValueError with a known error key, pass it through
            if isinstance(e, ValueError):
                raise
            # Otherwise, wrap it in a generic error
            raise ValueError("PREPARE_CHAT_ERROR")
