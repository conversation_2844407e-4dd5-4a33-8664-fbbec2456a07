import asyncio
import traceback
from typing import List

from middlewares.logging_utils import app_logger as logger
from services.pre_chat.data_source import PineConeQuery, DataSourceFilter, LawChunkModel
from services.pre_chat.data_source.data_source_analyzer import DataSourceAnalyzer
from services.pre_chat.helpers.law_helper import  clean_data
from services.pre_chat.helpers.regex_functions import Cleaners
from utils.pre_chat_dto import SearchTypeEnum, PreChatManager
from services.pre_chat.request_stratgey import BasePreRequestStrategy, SpecificDocumentStrategy


def filter_builder(law_ids: list) -> tuple[bool, list[PineConeQuery]]:
    """
    Build Pinecone filters for law searches
    Returns: (is_specific_type, list of PineConeQuery)
    """
    try:
        if not law_ids:
            logger.debug("filter_builder: No law_ids provided, returning default query")
            return False, [PineConeQuery(filter=None)]

        logger.debug(f"filter_builder: Building filters for Law with {len(law_ids)} law_ids")
        query_list = []
        exclude_query = []
        type_filter = False

        for law_id in law_ids:
            logger.debug(f"filter_builder: Adding filter for law_id={law_id}")
            query_list.append(PineConeQuery(
                filter=DataSourceFilter(type="txt_id", value=law_id, operation="eq")))
            exclude_query.append(DataSourceFilter(type="txt_id", value=law_id, operation="ne"))

        if exclude_query:
            # Default query with no filters
            # Add a query that excludes the specific laws
            logger.debug(f"filter_builder: Adding exclusion query for {len(exclude_query)} law_ids")
            query_list.append(PineConeQuery(
                filter=exclude_query))
            type_filter = True

        logger.debug(f"filter_builder: Created {len(query_list)} filters, type_filter={type_filter}")
        return type_filter, query_list
    except Exception as e:
        logger.error(f"filter_builder: Error building filters: {e}, {traceback.format_exc()}")
        raise ValueError("PREPARE_CHAT_ERROR")


class LawStrategy(BasePreRequestStrategy):

    def __init__(self, pre_chat_object: PreChatManager):
        super().__init__(pre_chat_object)

    async def handle(self):
        # Clean and normalize the query
        try:
            logger.info(f"LawStrategy.handle: Processing request for chat_id={self.pre_chat_object.chatId}")

            self.pre_chat_object.query.clean_query = Cleaners.NormalizeCommonAbbreviations(
                self.pre_chat_object.query.raw_query).clean()

            # Embed the query for vector search
            self.pre_chat_object.query.embed_query = await self.embed_query()

            # Build filters - logging happens inside the function
            is_specific_type, query_list = filter_builder([])

            # Get search results as LawChunkModel objects with text from SQL

            self.pre_chat_object.data.data_raw = await DataSourceAnalyzer(self.pre_chat_object).search(query_list,
                                                                                                       len(query_list))
            self.pre_chat_object.data.type = "chunks_document"

            # Choose and set the appropriate prompt template
            self.pre_chat_object.model_settings.prompt_template = "LAW_CONVERSATION_PROMPT_TEMPLATE_V1"
            logger.debug(f"LawStrategy.handle: Successfully completed for chat_id={self.pre_chat_object.chatId}")

        except Exception as e:
            logger.error(
                f"LawStrategy.handle: Error processing request for chat_id={self.pre_chat_object.chatId}: {e}, {traceback.format_exc()}")
            # If it's already a ValueError with a known error key, pass it through
            if isinstance(e, ValueError):
                raise
            raise ValueError("PREPARE_CHAT_ERROR")


class LawDocumentStrategy(SpecificDocumentStrategy):
    """Implementation for law documents"""

    async def get_full_chunks(self) -> List[LawChunkModel]:
        try:
            logger.debug(
                f"LawDocumentStrategy.get_full_chunks: Getting chunks for doc_id={self.doc_id}, chat_id={self.pre_chat_object.chatId}")

            results = await asyncio.gather(
                self.sql_helper.get_law_metadata_by_txt_id(self.doc_id),
                self.sql_helper.get_chunks_by_txt_id(self.doc_id),
                return_exceptions=False
            )

            if isinstance(results[0], Exception):
                logger.error(
                    f"LawDocumentStrategy.get_full_chunks: Error getting metadata for doc_id={self.doc_id}: {results[0]}, {traceback.format_exc()}")
                raise ValueError("GET_FULL_TEXT_ERROR")

            if isinstance(results[1], Exception):
                logger.error(
                    f"LawDocumentStrategy.get_full_chunks: Error getting chunks for doc_id={self.doc_id}: {results[1]}, {traceback.format_exc()}")
                raise ValueError("GET_FULL_TEXT_ERROR")

            meta_datas, chunks_data = results

            if not chunks_data:
                logger.warning(f"LawDocumentStrategy.get_full_chunks: No chunks found for doc_id={self.doc_id}")
                raise ValueError("GET_FULL_TEXT_ERROR")

            # first step: combine chunks with metadata
            combined_chunks = {}
            for cid, metadata in meta_datas.items():
                # Normalize titles
                chunk_data = chunks_data.get(cid, {})
                ctext = chunk_data.get("cText")

                if not ctext:
                    continue  # Skip if ctext is empty

                # Merge metadata and chunk data
                full_data = {**chunk_data, **metadata}
                full_data = clean_data(full_data)
                full_data["content"] = ctext
                full_data["id"] = cid

                try:
                    combined_chunks[cid] = LawChunkModel(**full_data)
                except Exception as e:
                    logger.error(
                        f"LawDocumentStrategy.get_full_chunks: Failed to create LawChunkModel for cid={cid}  {e} {self.doc_id}")
            logger.info(
                f"LawDocumentStrategy.get_full_chunks: Successfully combined {len(combined_chunks)} chunks for doc_id={self.doc_id}")
            return list(combined_chunks.values())

        except Exception as e:
            logger.error(
                f"LawDocumentStrategy.get_full_chunks: Error getting chunks for doc_id={self.doc_id}, chat_id={self.pre_chat_object.chatId}: {e}, {traceback.format_exc()}")
            # If it's already a ValueError with a known error key, pass it through
            if isinstance(e, ValueError):
                raise
            # Otherwise, wrap it in a generic error
            raise ValueError("GET_FULL_TEXT_ERROR")

    async def handle(self):
        try:
            logger.info(
                f"LawDocumentStrategy.handle: Processing specific document request for chat_id={self.pre_chat_object.chatId}, doc_id={self.doc_id}")

            self.pre_chat_object.query.clean_query = Cleaners.NormalizeCommonAbbreviations(
                self.pre_chat_object.query.raw_query).clean()

            # Get metadata
            meta_data = self.sql_helper.get_maagar_id_and_total_tokens_for_txt_id(self.doc_id)
            if not meta_data:
                logger.error(f"LawDocumentStrategy.handle: No metadata found for doc_id={self.doc_id}")
                raise ValueError("GET_FULL_TEXT_ERROR")

            token_count = meta_data.total_tokens

            if token_count < self.max_tokens:
                # Get full chunks - logging happens inside the function
                self.pre_chat_object.data.data_raw = await self.get_full_chunks()

            else:
                # Document too large, get chunks

                self.pre_chat_object.query.embed_query = await  self.embed_query()

                query_list = self.get_query_for_spesfic_document()
                self.pre_chat_object.data.data_raw = await DataSourceAnalyzer(self.pre_chat_object).search(query_list,
                                                                                                           len(query_list))

            if not self.pre_chat_object.data.data_raw:
                logger.error(
                    f"LawDocumentStrategy.handle: No chunks found for doc_id={self.doc_id} ,token_count={token_count} and max_tokens={self.max_tokens}")
                raise ValueError("RESULTS_NONE_MESSAGE")

            self.pre_chat_object.data.type = "chunks_document"
            self.pre_chat_object.chat_settings.search_type = SearchTypeEnum.specific_document
            self.pre_chat_object.model_settings.prompt_template = "LAW_FULL_TEXT_PROMPT_TEMPLATE_V1"

            logger.info(f"LawDocumentStrategy.handle: Successfully completed for chat_id={self.pre_chat_object.chatId}")

        except Exception as e:
            logger.error(
                f"LawDocumentStrategy.handle: Error processing request for chat_id={self.pre_chat_object.chatId}, doc_id={self.doc_id}: {e}, {traceback.format_exc()}")
            # If it's already a ValueError with a known error key, pass it through
            if isinstance(e, ValueError):
                raise
            # Otherwise, wrap it in a generic error
            raise ValueError("PREPARE_CHAT_ERROR")
