from abc import ABC, abstractmethod

from configs.app_config import MAX_TOTAL_TOKENS
from data_ops.embedding_helper import embed_text

from services.pre_chat.helpers.regex_functions import Cleaners
from utils.pre_chat_dto import PreChatManager

class BasePreRequestStrategy(ABC):
    def __init__(self, pre_chat_object: PreChatManager):
        self.pre_chat_object = pre_chat_object
        self.sql_helper = SQLHelper()

    def choose_prompt_template(self):
        pass

    @abstractmethod
    async def handle(self):
        raise NotImplementedError("handle method must be implemented")

    # TODO - add prometheus metrics
    async def embed_query(self) -> list[float]:
        # raise ValueError("EMBED_TEXTS_ERROR")

        if not self.pre_chat_object.connection_source.embed_client_instance or not self.pre_chat_object.query.clean_query:
            logger.error("No embedding client instance or clean query provided.")
            raise ValueError("No embedding client instance or clean query provided.")
        try:
            text = self.pre_chat_object.query.clean_query+"?"
            if self.pre_chat_object.summaries:
                text += " " + self.pre_chat_object.summaries[-1]

            return await embed_text(
                cohere_client=self.pre_chat_object.connection_source.embed_client_instance,
                text=text, input_type="search_query")
        except Exception as e:
            logger.error(f"Failed to embed query: {self.pre_chat_object.query.clean_query}, error: {e}")
            raise ValueError("EMBED_TEXTS_ERROR")


from typing import List

from utils.pre_chat_dto import PreChatManager
from services.pre_chat.helpers.sql_helper import SQLHelper
from services.pre_chat.data_source import PineConeQuery, DataSourceFilter
from middlewares.logging_utils import app_logger as logger


class SpecificDocumentStrategy(BasePreRequestStrategy):
    """
    Generic strategy for handling specific document requests
    Can be used for verdicts, laws, machshavot books, etc.
    """

    def __init__(self, pre_chat_object: PreChatManager, doc_id=None):
        super().__init__(pre_chat_object)
        self.doc_id = doc_id
        self.max_tokens = MAX_TOTAL_TOKENS
        self.pre_chat_object = pre_chat_object
        self.domain = self.pre_chat_object.domain
        self.data = {}

    def get_full_document(self) -> str:
        """Get full law text"""
        full_text = self.sql_helper.get_html_and_text_only_content(self.doc_id)
        return Cleaners.RemoveUnwantedChars(full_text).clean()

    def get_query_for_spesfic_document(self) -> List[PineConeQuery]:
        """Get chunked law content"""
        # Create filter for this specific law
        query_list = [PineConeQuery(
            filter=[DataSourceFilter(
                type="txt_id",
                value=self.doc_id,
                operation="eq"
            )]
        )]
        return query_list

    async def handle(self):
        raise NotImplementedError("handle method must be implemented")


