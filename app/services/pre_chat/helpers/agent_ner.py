import traceback

from dotenv import load_dotenv
from pydantic import BaseModel
from pydantic_ai import Agent

from ai_models import get_ai_provider_factory
from middlewares.logging_utils import app_logger as logger


class NerAgent(BaseModel):
    has_ner: bool= False
    entities: list= []


async def agent_ner(query:str, chat_id: str = None) -> NerAgent:
    try:
        logger.info(f"agent_ner: Processing NER for query, chat_id={chat_id}")

        agent = Agent(
            model='anthropic:claude-3-5-haiku-latest',
            deps_type=str,
            system_prompt=("You are an AI assistant trained to perform named entity recognition (NER) on "
                        "Hebrew text. Your task is to identify and extract named entities of type PERSON, "
                        "such as the names of people. For this task, you should ignore placeholders and "
                        "generic references to individuals."),
            result_type=NerAgent
        )

        logger.info(f"agent_ner: Running NER agent for chat_id={chat_id}")
        dice_result = await agent.run(query)

        entities=[]
        for entity in dice_result.data.entities:
            if isinstance(entity, dict):
                name = entity.get("full_name") or entity.get("name")
                if name:
                    entities.append(name)
            else:
                entities.append(entity)

        result = NerAgent(
            has_ner=dice_result.data.has_ner,
            entities=entities
        )

        if result.has_ner:
            logger.info(f"agent_ner: Found {len(entities)} entities for chat_id={chat_id}: {entities}")
        else:
            logger.info(f"agent_ner: No entities found for chat_id={chat_id}")

        return result

    except Exception as e:
        logger.error(f"agent_ner: Error processing NER for chat_id={chat_id}: {e}, {traceback.format_exc()}")
        return NerAgent(
            has_ner=False,
            entities=[]
        )

async def main():
    await agent_ner(" ")

if __name__ == '__main__':
    load_dotenv()

    # agent_ner()
    import asyncio
    asyncio.run(main())
