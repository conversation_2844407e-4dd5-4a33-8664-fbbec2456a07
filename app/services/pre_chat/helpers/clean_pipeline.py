from abc import ABC, abstractmethod

from services.pre_chat.helpers.regex_functions import Cleaners


class BasePipeline(ABC):
    def __init__(self, text: str, cleaners):
        self.text = text
        self.cleaners = cleaners

    def get_cleaners(self) -> list:
        return self.cleaners

    def run(self) -> str:
        "Runs all cleaning functions in the defined order"
        cleaners = self.get_cleaners()
        for cleaner_class in cleaners:
            cleaner = cleaner_class(self.text)
            self.text = cleaner.clean()
        return self.text


class BaseCleanerPipeline(BasePipeline):
    def __init__(self, text: str):
        cleaners_list = [Cleaners.RemoveUnwantedChars, Cleaners.EraseBeforeWord, Cleaners.TrimEndOfVerdict,
                         Cleaners.FixDatesForRTLText]
        super().__init__(text, cleaners_list)


class AggressiveCleanerPipeline(BasePipeline):
    def __init__(self, text: str):
        cleaners_list = [Cleaners.CleanVerdictText]
        super().__init__(text, cleaners_list)
