import re
import datetime
import asyncio
import traceback

from api.dependencies.mongo_db import get_mongo_client
from configs.app_config import MONGO_DB_AUTOCOMPLETE, MONGO_PROCEDURE_COLLECTION
from middlewares.logging_utils import app_logger as logger

decision_name_priority = ['פסק דין', 'גזר דין', 'הכרעת דין', 'החלטה']
decision_priority_map = {name: i for i, name in enumerate(decision_name_priority)}


# def multi_procedure_extraction(query: str):
#     try:
#         logger.info(f"multi_procedure_extraction: Extracting procedures from query")
#         pattern = r'([א-ת״׳"]+)?\s*(?:\(([^)]+)\))?\s*(\d{1,8}[-/]\d{1,8}[-/]?\d{0,2})'
#         matches = re.findall(pattern, query)
#         court_cases = []
#
#         for match in matches:
#             pt = match[0] or match[1]
#             pn = match[2]
#             if '-' in pn and not re.match(r'^\d{1,8}[-/]\d{1,8}[-/]?\d{0,2}$', pn):
#                 continue
#             pn_clean = re.sub(r'[^0-9]', '', pn)
#             pt_clean = re.sub(r'[^א-ת]', '', pt)
#             if pt_clean:
#                 prefix_match = re.match(r'^([בכלמש])?(.*)$', pt_clean)
#                 prefix, pt_stripped = prefix_match.groups()
#
#                 if prefix:
#                     court_cases.append((pt_clean, pn_clean, ""))  # עם התחילית
#                     court_cases.append((pt_stripped, pn_clean, ""))  # בלי התחילית
#                 else:
#                     court_cases.append((pt_clean, pn_clean, ""))
#         if court_cases:
#             logger.info(f"multi_procedure_extraction: Found {len(court_cases)} court cases: {court_cases}")
#         else:
#             logger.info(f"multi_procedure_extraction: No court cases found in query")
#
#         return court_cases
#
#     except Exception as e:
#         logger.error(f"multi_procedure_extraction: Error extracting procedures: {e}, {traceback.format_exc()}")
#         return []
#


async def fetch_ids_for_pair(pt, pn, collection):
    try:
        logger.info(f"fetch_ids_for_pair: Fetching IDs for procedure type={pt}, number={pn}")
        return collection.find({
            "procedures_objects.pt": pt,
            "procedures_objects.pn_normalized": pn,
        })
    except Exception as e:
        logger.error(f"fetch_ids_for_pair: Error fetching IDs for pt={pt}, pn={pn}: {e}, {traceback.format_exc()}")
        raise


async def get_txt_id_by_procedure2(court_cases: list[tuple[str, str]], mongo_client, chat_id: str = None):
    try:
        logger.info(f"get_all_verdict_ids: Processing {len(court_cases)} court cases for chat_id={chat_id}")

        if not court_cases:
            logger.info(f"get_all_verdict_ids: No court cases to process for chat_id={chat_id}")
            return []

        collection = mongo_client[MONGO_DB_AUTOCOMPLETE][MONGO_PROCEDURE_COLLECTION]
        start = datetime.datetime.now()

        tasks = [fetch_ids_for_pair(pt, pn, collection) for pt, pn, location in court_cases]
        logger.info(f"get_all_verdict_ids: Created {len(tasks)} tasks for chat_id={chat_id}")

        expected_keys = {f"{pt}_{pn}" for pt, pn, location, in court_cases}

        all_results = await asyncio.gather(*tasks, return_exceptions=True)

        verdict_ids_raw = {}

        for i, result in enumerate(all_results):
            if isinstance(result, Exception):
                print(result)
                continue

            async for doc in result:
                txt_id = doc["_id"]
                procedures = doc["procedures_objects"]
                title = doc.get("title", "")
                for proc in procedures:
                    pt = proc.get('pt')
                    pn_normalized = proc.get('pn_normalized')

                    dn = proc.get('dn')
                    key = f"{pt}_{pn_normalized}"

                    if key not in expected_keys:
                        continue

                    new_priority = decision_priority_map.get(dn, float('inf'))
                    current_priority = decision_priority_map.get(verdict_ids_raw.get(key, {}).get("dn"), float('inf'))

                    if key not in verdict_ids_raw or new_priority < current_priority:
                        verdict_ids_raw[key] = {"_id": txt_id, "dn": dn}

        final_ids = [int(entry["_id"]) for entry in verdict_ids_raw.values()]
        duration = datetime.datetime.now() - start
        logger.info(
            f"get_all_verdict_ids: Found {len(final_ids)} verdict IDs in {duration} for chat_id={chat_id}, {verdict_ids_raw}")
        return final_ids

    except Exception as e:

        logger.error(
            f"get_all_verdict_ids: Error processing court cases for chat_id={chat_id}: {e}, {traceback.format_exc()}")
        return []


async def get_txt_id_by_procedure(court_cases: list[tuple[str, str, str]], mongo_client, chat_id: str = None):
    try:
        logger.info(f"get_all_verdict_ids: Processing {len(court_cases)} court cases for chat_id={chat_id}")

        if not court_cases:
            logger.info(f"get_all_verdict_ids: No court cases to process for chat_id={chat_id}")
            return []

        collection = mongo_client[MONGO_DB_AUTOCOMPLETE][MONGO_PROCEDURE_COLLECTION]
        start = datetime.datetime.now()

        tasks = [fetch_ids_for_pair(pt, pn, collection) for pt, pn, _ in court_cases]
        logger.info(f"get_all_verdict_ids: Created {len(tasks)} tasks for chat_id={chat_id}")

        # Map expected_keys to their locations
        expected_keys_to_location = {f"{pt}_{pn}": location for pt, pn, location in court_cases}
        expected_keys = set(expected_keys_to_location.keys())

        all_results = await asyncio.gather(*tasks, return_exceptions=True)

        verdict_ids_raw = {}

        for i, result in enumerate(all_results):
            if isinstance(result, Exception):
                logger.warning(f"get_all_verdict_ids: Task {i} raised exception: {result}")
                continue

            async for doc in result:
                txt_id = doc["_id"]
                procedures = doc.get("procedures_objects", [])
                title = doc.get("title", "")

                for proc in procedures:
                    pt = proc.get('pt')
                    pn_normalized = proc.get('pn_normalized')
                    dn = proc.get('dn')

                    key = f"{pt}_{pn_normalized}"
                    if key not in expected_keys:
                        continue

                    location = expected_keys_to_location.get(key, "")
                    location_in_title = location and location in title

                    new_priority = decision_priority_map.get(dn, float('inf'))

                    existing = verdict_ids_raw.get(key)
                    if existing:
                        current_priority = decision_priority_map.get(existing.get("dn"), float('inf'))
                        current_location_match = existing.get("location_match", False)

                        # Priority comparison with location preference
                        if (
                            new_priority < current_priority or
                            (new_priority == current_priority and location_in_title and not current_location_match)
                        ):
                            verdict_ids_raw[key] = {
                                "_id": txt_id,
                                "dn": dn,
                                "location_match": location_in_title
                            }
                    else:
                        verdict_ids_raw[key] = {
                            "_id": txt_id,
                            "dn": dn,
                            "location_match": location_in_title
                        }

        final_ids = [int(entry["_id"]) for entry in verdict_ids_raw.values()]
        duration = datetime.datetime.now() - start

        logger.info(
            f"get_all_verdict_ids: Found {len(final_ids)} verdict IDs in {duration} for chat_id={chat_id}, {verdict_ids_raw}"
        )

        return final_ids

    except Exception as e:
        logger.error(
            f"get_all_verdict_ids: Error processing court cases for chat_id={chat_id}: {e}, {traceback.format_exc()}"
        )
        return []


def multi_procedure_extraction(query: str):
    try:
        court_cases = []
        pattern = re.compile(
            r'([א-ת״׳"\s]+?)\s*'
            r'(?:[\(\[]([^\)\]]+)[\)\]])?\s*'
            r'(\d{1,8}[-/]\d{1,8}[-/]?\d{0,2})'
        )

        matches = pattern.findall(query)

        for match in matches:
            pt_raw = match[0] or match[1] or ""
            pt_split = pt_raw.strip().split()
            pt = pt_split[-1] if pt_split else ""
            location_clean = match[1] or ""
            pn = match[2]

            pn_clean = re.sub(r'[^0-9]', '', pn)

            pt_words = [w for w in re.findall(r'[א-ת״׳"]+', pt)]

            # ניקוי location - אותיות עבריות בלבד
            # location_clean = re.sub(r'[^א-ת]', '', location)

            for pt_word in pt_words:
                pt_word = re.sub(r'[^א-ת]', '', pt_word)

                prefix_match = re.match(r'^([בכלמש])?(.*)$', pt_word)

                prefix, pt_stripped = prefix_match.groups()
                if prefix:
                    court_cases.append((pt_word, pn_clean, location_clean))
                    court_cases.append((pt_stripped, pn_clean, location_clean))
                else:
                    court_cases.append((pt_word, pn_clean, location_clean))

            if not pt_words:
                court_cases.append(("", pn_clean, location_clean))

        return court_cases

    except Exception as e:
        print(f"Error: {e}")
        return []


if __name__ == '__main__':
    pn = "345171116"
    pt = "תפ"
    query = """
        #  על״ע 7892/04,  1234-56, ע״א 9876/12 עלי 3456/78, בש״א   1111/22
        # משמעות זיקה למידע בייצוג עו"ד [כלל 16[א]]
        # בעמ 7272/10
         בבג״צ 5658/23
        # על"ע 5/85 - מרים שמש נגד הועד המחוזי של לשכת עורכי הדין ת"ל אביב
        # 4982/02/09
        # נכתב על"ע 3467/00 הוועד המחוזי של לשכת עורכי-הדין בתל-אביב נ' מיכאל צלטנר, עו"ד, פ"ד נו (2) 895, בעמ' 900-901, כי:
        # עמ״ת (מרכז) 4982-02-09
        """


    court_cases = multi_procedure_extraction(query)

    print(court_cases)
    import asyncio

    mongo_client = asyncio.run(get_mongo_client())
    asyncio.run(get_txt_id_by_procedure(court_cases, mongo_client))
