import traceback
from typing import Dict, Type

from middlewares.logging_utils import app_logger as logger

from services.pre_chat.redis_pre_chat import save_pre_chat_to_redis, handle_pre_chat_flow
from redis_db.redis_helper import convert_to_pre_chat_manager
from services.pre_chat.request_stratgey import BasePreRequestStrategy
from services.pre_chat.request_stratgey.law_strategy import LawStrategy, LawDocumentStrategy
from services.pre_chat.request_stratgey.machsvaot_strategy import MachshavotStrategy
from services.pre_chat.request_stratgey.trainer_strategy import TrainerStrategy
from services.pre_chat.request_stratgey.verdict_strategy import VerdictStrategy, VerdictDocumentStrategy
from utils.analytics import create_document
from utils.conversation_manager import ConversationItem, RoleEnum, ConversationEnum
from utils.pre_chat_dto import PreChatRequest, PreChatAnalytics, \
    ChatTypeAnalytics, ConnectionSource, SearchTypeEnum, Query, PreChatManager, ChatTypeClient


class PreRequestDomainStrategyFactory:
    strategies: Dict[str, Type[BasePreRequestStrategy]] = {
        "verdict": VerdictStrategy,
        "machshavot": MachshavotStrategy,
        "law": LawStrategy,
        "ethics": VerdictStrategy
    }

    @classmethod
    def get_strategy(cls, domain: str, pre_chat_object: PreChatManager) -> BasePreRequestStrategy:
        try:
            logger.info(
                f"PreRequestDomainStrategyFactory.get_strategy: Getting strategy for domain={domain}, chat_id={pre_chat_object.chatId}")

            if pre_chat_object.filters and pre_chat_object.filters.docs:
                pre_chat_object.model_settings.model="standard_model"
                filters = pre_chat_object.filters.docs
                filter_type = filters[0].type
                logger.info(
                    f"PreRequestDomainStrategyFactory.get_strategy: Found filter_type={filter_type} for chat_id={pre_chat_object.chatId}")

                if (domain == "verdict" or domain == "ethics") and filter_type == "txt_id":
                    doc_id = int(filters[0].value)
                    # pre_chat_object.pre_chat_analytics.chat_type_analytics = ChatTypeAnalytics.VERDICT_DOC_QUERY
                    return VerdictDocumentStrategy(pre_chat_object, doc_id=doc_id)
                elif domain == "law" and filter_type == "txt_id":
                    doc_id = int(filters[0].value)
                    # pre_chat_object.pre_chat_analytics.chat_type_analytics = ChatTypeAnalytics.LAW_DOC_QUERY
                    return LawDocumentStrategy(pre_chat_object, doc_id=doc_id)
                elif domain == "machshavot" and filter_type == "book_id" and len(filters) > 1 and filters[
                    1].type == "question_id":
                    # pre_chat_object.pre_chat_analytics.chat_type_analytics = ChatTypeAnalytics.THOUGHTS_TRAINING_SUBJECT_QUERY
                    return TrainerStrategy(pre_chat_object)

            pre_chat_object.model_settings.model = "premium_model"
            strategy_class = cls.strategies.get(domain)

            if not strategy_class:
                logger.error(
                    f"PreRequestDomainStrategyFactory.get_strategy: No strategy found for domain={domain}, chat_id={pre_chat_object.chatId}")
                raise ValueError("PREPARE_CHAT_ERROR")

            logger.info(
                f"PreRequestDomainStrategyFactory.get_strategy: Creating {strategy_class.__name__} for chat_id={pre_chat_object.chatId}")
            strategy_instance = strategy_class(pre_chat_object)

            return strategy_instance

        except Exception as e:
            logger.error(
                f"PreRequestDomainStrategyFactory.get_strategy: Error getting strategy for domain={domain}, chat_id={pre_chat_object.chatId}: {e}, {traceback.format_exc()}")
            if isinstance(e, ValueError):
                raise
            raise ValueError("PREPARE_CHAT_ERROR")


class PreChatProcess:
    def __init__(self, request: PreChatRequest, connection_source: ConnectionSource):
        self.pre_chat_object = None
        self.request = request
        self.connection_source = connection_source

    async def process_request(self) -> PreChatManager:

        try:
            logger.info(
                f"PreChatProcess.process_request: Processing request for chat_id={self.request.chatId}, user_id={self.request.userId}, domain={self.request.domain}")
            await self.load_pre_chat()

            # First try to load existing data from Redis
            logger.info(
                f"PreChatProcess.process_request: Getting strategy for chat_id={self.pre_chat_object.chatId}, domain={self.pre_chat_object.domain}")
            strategy = PreRequestDomainStrategyFactory.get_strategy(self.pre_chat_object.domain, self.pre_chat_object)

            logger.info(f"PreChatProcess.process_request: Handling strategy for chat_id={self.pre_chat_object.chatId}")
            await strategy.handle()
            self.pre_chat_object.chat_settings.chat_type_client = ChatTypeClient.from_strategy(strategy)
            if self.pre_chat_object.chat_settings.search_type != SearchTypeEnum.trainer:
                logger.info(
                    f"PreChatProcess.process_request: Adding user query to conversation for chat_id={self.pre_chat_object.chatId}")
                self.pre_chat_object.add_to_conversation(
                    ConversationItem(role=RoleEnum.user, content=self.request.query,
                                     action=ConversationEnum.BOTH.value,
                                     prompt=None,
                                     entry_type="query"))

            # Save the updated data to Redis
            await save_pre_chat_to_redis(pre_chat_object=self.pre_chat_object)
            logger.info(
                f"PreChatProcess.process_request: Successfully processed request for chat_id={self.pre_chat_object.chatId}")

            ###send analytics to firestore
            try:
                self.process_analytics()
            except Exception as e:
                logger.error(
                    f"PreChatProcess.process_request: Error processing analytics for chat_id={self.pre_chat_object.chatId}: {e}, {traceback.format_exc()}")

            return self.pre_chat_object

        except Exception as e:
            logger.error(
                f"PreChatProcess.process_request: Error processing pre-chat request for chat_id={self.request.chatId}, user_id={self.request.userId}: {e}, {traceback.format_exc()}")

            # If it's already a ValueError with a known error key, pass it through
            if isinstance(e, ValueError):
                raise
            # Otherwise, wrap it in a generic error
            raise ValueError("PREPARE_CHAT_ERROR")


    async def load_pre_chat(self):
        try:
            logger.info(
                f"PreChatProcess.load_pre_chat: Loading pre-chat data for chat_id={self.request.chatId}, user_id={self.request.userId}")
            chat_data = await handle_pre_chat_flow(self.request, self.connection_source)

            if chat_data:
                # Convert RedisSchema to PreChatManager
                logger.info(
                    f"PreChatProcess.load_pre_chat: Converting existing Redis data for chat_id={self.request.chatId}")
                self.pre_chat_object = await convert_to_pre_chat_manager(chat_data, self.request,
                                                                         self.connection_source)

            else:
                logger.info(
                    f"PreChatProcess.load_pre_chat: Creating new PreChatManager for chat_id={self.request.chatId}")
                self.pre_chat_object = PreChatManager(
                    connection_source=self.connection_source,
                    chatId=self.request.chatId,
                    userId=self.request.userId,
                    query=Query(raw_query=self.request.query, clean_query=None, embed_query=[]),
                    domain=self.request.domain,
                    filters=self.request.filters,
                )

            logger.info(
                f"PreChatProcess.load_pre_chat: Successfully loaded pre-chat data for chat_id={self.request.chatId}")

        except Exception as e:
            logger.error(
                f"PreChatProcess.load_pre_chat: Error loading pre-chat data for chat_id={self.request.chatId}, user_id={self.request.userId}: {e}, {traceback.format_exc()}")
            if isinstance(e, ValueError):
                raise
            raise ValueError("PREPARE_CHAT_ERROR")

    def process_analytics(self):
        def choose_search_type(request: PreChatRequest) -> str:
            domain = request.domain
            search_type_specific = True if self.pre_chat_object.chat_settings.search_type == SearchTypeEnum.specific_document else False

            if domain == "verdict" or domain == "ethics":
                return (
                    ChatTypeAnalytics.VERDICT_DOC_QUERY.value
                    if search_type_specific
                    else ChatTypeAnalytics.VERDICT_QUERY.value
                )

            elif domain == "law":
                return (
                    ChatTypeAnalytics.LAW_DOC_QUERY.value
                    if search_type_specific
                    else ChatTypeAnalytics.LAW_QUERY.value
                )

            elif domain == "machshavot":
                if search_type_specific:
                    doc_type = request.filters.docs[0].type if request.filters and request.filters.docs else None
                    if doc_type == "book_id":
                        return ChatTypeAnalytics.THOUGHTS_BOOK_QUERY.value
                    elif doc_type == "question_id":
                        return ChatTypeAnalytics.THOUGHTS_TRAINING_SUBJECT_QUERY.value
                return ChatTypeAnalytics.THOUGHTS_LEGAL_FIELD_QUERY.value
            return ChatTypeAnalytics.UNKNOWN.value

        logger.info(f"PreChatProcess.process_analytics: Processing analytics for chat_id={self.pre_chat_object.chatId}")

        query_length = len(self.pre_chat_object.query.raw_query)

        prompt_version = self.pre_chat_object.model_settings.prompt_template
        ner = self.pre_chat_object.query.ner.entities if self.pre_chat_object.query.ner.has_ner else []
        filters = []
        search_type = choose_search_type(self.request)
        try:

            if len(self.request.filters.docs) > 0:
                for filter in self.request.filters.docs:
                    filters.append({"type": filter.type.value, "id": filter.value,"extract":False})
            else:
                if self.pre_chat_object.chat_settings.documents:
                    for doc in self.pre_chat_object.chat_settings.documents:
                        filters.append({"type": "txt_id", "id": doc,"extract":True})
            # Create the PreChatAnalytics object
            pre_chat_analytics = PreChatAnalytics(
                user_id=self.pre_chat_object.userId,
                chat_id=self.pre_chat_object.chatId,
                query_index=self.pre_chat_object.chat_settings.session_index,
                query_length=query_length,
                chat_type_analytics=ChatTypeAnalytics(search_type),
                prompt_version=prompt_version,
                ner=ner,
                docs_id=filters,
                search_alpha=self.pre_chat_object.query.search_alpha
            )
            create_document("pre_chat_analytics", pre_chat_analytics.model_dump())
        except Exception as e:
            logger.warning(
                f"PreChatProcess.process_analytics: Error logging analytics for chat_id={self.pre_chat_object.chatId}: {e}, {traceback.format_exc()}")


