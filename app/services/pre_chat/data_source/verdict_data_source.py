import traceback
from typing import List, Dict, Any

from services.pre_chat.data_source import DataSource, PineConeQuery, ChunkExtractor, VerdictChunkModel, \
    TextExtractor
from services.pre_chat.data_source.pinecone_helpers import PinconeHelper
from services.pre_chat.helpers.sql_helper import SQLHelper
from utils.pre_chat_dto import PreChatManager
from middlewares.logging_utils import app_logger as logger
from pinecone.core.openapi.db_data.model.scored_vector import ScoredVector


def build_title(chunk):

    try:
        prosecutors = chunk.prosecutors or []
        defenders = chunk.defenders or []
        title = f"{' ו'.join(defenders)} נגד {' ו'.join(prosecutors)}"
    except Exception as e:
        logger.warning(f"Error building title: {e}")
        title = "כותרת פסק דין"
    return title


class VerdictTextExtractor(TextExtractor):
    """Text extractor for verdict sources"""

    def __init__(self):
        self.sql_helper = SQLHelper()

    async def parse_chunk(self, search_results: List[Dict[str, Any]]) -> List[VerdictChunkModel]:
        """Extract text using get_chunk_texts and convert to VerdictChunkModel"""
        # Extract all cids to fetch text in a single query
        all_cids = [result.get('id') for result in search_results]

        # Fetch text from SQL for all cids in a single query
        chunk_texts = self.sql_helper.get_chunk_texts(all_cids) if all_cids else {}

        # Convert to VerdictChunkModel objects
        verdict_chunks = []
        for result in search_results:
            try:
                # Convert to VerdictChunkModel
                metadata = result.get('metadata', {})
                cid = metadata.get('cid')
                chunk_index = cid.split('-')[1]

                # Add text if available
                text = chunk_texts[cid]
                chunk = ChunkExtractor.extract(result, "verdict", text, chunk_index)
                if chunk.title == "":
                    chunk.title = build_title(chunk)
                if isinstance(chunk, VerdictChunkModel):
                    verdict_chunks.append(chunk)
            except Exception as e:
                logger.warning(f"Failed to extract verdict chunk: {str(e)}, {traceback.print_exc()}")
                continue

        return verdict_chunks


class VerdictDataSource(DataSource):

    def __init__(self, request: PreChatManager, query: PineConeQuery,
                 text_extractor: TextExtractor = VerdictTextExtractor()):
        super().__init__(query, text_extractor)
        self.request = request
        self.search_alpha = self.query.search_alpha
        self.sparse_vector = None
        self.embed_query = self.request.query.embed_query

    async def get_data(self, sort: bool = True, top_k: int = 100) -> List[ScoredVector]:
        # Get raw search results from Pinecone
        if self.using_sparse_embedding and self.request.query.ner.has_ner:
            self.sparse_vector = PinconeHelper.get_sparse_embeddings(
                domain_name=self.request.domain, query=self.request.query.raw_query,
                ner_entities=self.request.query.ner.entities)

            if self.sparse_vector:
                self.embed_query, self.sparse_vector = PinconeHelper.combine_text_and_sparse_embeddings(
                    text_embeddings=self.embed_query,
                    sparse_embeddings=self.sparse_vector, search_alpha=self.search_alpha)

        pinecone_results = await PinconeHelper.pinecone_search_query(
            pinecone_index=self.request.connection_source.vector_db_instance.Verdict,
            embeddings=self.embed_query,
            top_k=top_k, filter_dict=self.query.to_pinecone_filter,
            sparse_vector=self.sparse_vector)

        if sort:
            pinecone_results = sorted(pinecone_results, key=lambda x: x['score'], reverse=True)

        return pinecone_results
