from collections import OrderedDict
from typing import List, Dict, Any

from pinecone.core.openapi.db_data.model.scored_vector import ScoredVector

from middlewares.logging_utils import app_logger as logger
from services.pre_chat.data_source import DataSource, PineConeQuery, MachshavotChunkModel, TextExtractor
from services.pre_chat.data_source.pinecone_helpers import PinconeHelper
from services.pre_chat.helpers.sql_helper import SQLHelper
from utils.pre_chat_dto import PreChatManager
from app.models import AiBookSubChapter


class MachshavotTextExtractor(TextExtractor):
    def __init__(self):
        self.sql_helper = SQLHelper()

    """Text extractor for machshavot sources"""

    async def parse_chunk(self, search_results: List[Dict[str, Any]]) -> List[MachshavotChunkModel]:
        """Parse search results into sub-chapters with all metadata

        This method aggregates search results by sub_chapter_id, re-ranks them by score,
        and builds a list of sub-chapters. It limits the total tokens to 65000.

        Args:
            search_results: List of search results from Pinecone

        Returns:
            List of MachshavotChunkModel objects representing sub-chapters
        """
        # Group search results by sub_chapter_id and collect metadata
        sub_chapters_data = {}
        sub_chapter_ids = set()

        # First, process search results to get scores and metadata
        for result in search_results:
            metadata = result.get('metadata', {})
            sub_chapter_id = metadata.get('sub_chapter_id')
            score = result.get('score', 0.0)

            if not sub_chapter_id:
                continue

            sub_chapter_ids.add(sub_chapter_id)

            if sub_chapter_id not in sub_chapters_data:
                sub_chapters_data[sub_chapter_id] = {
                    'max_score': score,
                    'count': 1,
                    'meta_data': metadata
                }
            else:
                sub_chapters_data[sub_chapter_id]['count'] += 1
                sub_chapters_data[sub_chapter_id]['max_score'] = max(
                    sub_chapters_data[sub_chapter_id]['max_score'], score
                )

        if not sub_chapter_ids:
            logger.warning("No sub_chapter_ids found in search results")
            return []

        try:
            # Re-rank sub_chapters by score
            sub_chapters_by_score = OrderedDict(
                sorted(sub_chapters_data.items(), key=lambda x: x[1]['max_score'], reverse=True)
            )

            # Build the list of sub_chapters
            machshavot_chunks = []
            total_tokens = 0
            MAX_TOKENS = 65000
            sub_chapter_all_data = self.sql_helper.import_sub_chapter(sub_chapter_ids)
            # Process sub_chapters by score
            for sub_chapter_id, sub_chapter_data in sub_chapters_by_score.items():
                # Create a MachshavotChunkModel for the sub_chapter
                sub_chapter_model = self._create_sub_chapter_model(
                    sub_chapter_id,
                    sub_chapter_all_data[sub_chapter_id],
                    sub_chapter_data['meta_data'],
                    sub_chapter_data['max_score']
                )

                if sub_chapter_model and total_tokens + sub_chapter_model.token_count <= MAX_TOKENS:
                    machshavot_chunks.append(sub_chapter_model)
                    total_tokens += sub_chapter_model.token_count
                elif total_tokens == 0:
                    # If we don't have any chunks yet and this one is too big, add it anyway
                    # (this is to ensure we always return at least one result)
                    machshavot_chunks.append(sub_chapter_model)
                    break

            logger.info(f"Processed {len(machshavot_chunks)} sub-chapters with {total_tokens} tokens")
            return machshavot_chunks

        except Exception as e:
            logger.warning(f"Error in parse_chunk: {str(e)}")
            return []

    def _create_sub_chapter_model(self, sub_chapter_id: str, sub_chapter_data: AiBookSubChapter,
                                  metadata: Dict[str, Any], score: float) -> MachshavotChunkModel | None:
        """Create a MachshavotChunkModel for a sub-chapter

        This method creates a MachshavotChunkModel for a sub-chapter using the metadata
        from the search results. It uses the SQLHelper to get the sub-chapter data.

        Args:
            sub_chapter_id: The ID of the sub-chapter
            metadata: Metadata from the search results
            score: The score from the search results

        Returns:
            A MachshavotChunkModel for the sub-chapter
        """
        try:
            # Create a unique ID for the sub-chapter model

            # Combine data with metadata
            book_name = metadata.get('legal_field', '') + ' - ' + metadata.get('book_type', '')
            combined_data = {
                'id': sub_chapter_id,
                'cid': sub_chapter_id,
                'content': sub_chapter_data.subChapterText,
                'text': sub_chapter_data.subChapterText,
                'score': score,  # Use the score from the search results
                'source': 'machshavot',
                'book_id': metadata.get('book_id', '0'),
                'chapter_id': metadata.get('chapter_id', '0'),
                'sub_chapter_id': sub_chapter_id,
                'chapter_title': metadata.get('chapter_title', ''),
                'sub_chapter_title': sub_chapter_data.subChapterTitle,
                'chapter_number': metadata.get('chapter_number', ''),
                'sub_chapter_number': sub_chapter_data.subChapterNumber,
                'legal_field': metadata.get('legal_field', ''),
                'book_type': metadata.get('book_type', ''),
                'legal_field_id': 4,
                'book_type_id': 2,
                'book_name': book_name,
                'page_number': sub_chapter_data.startPageNumber,
                'token_count': sub_chapter_data.nTokens
            }

            # Create and return the model
            return MachshavotChunkModel(**combined_data)

        except Exception as e:
            logger.error(f"Failed to create sub-chapter MachshavotChunkModel: {str(e)}")
            return None


class MachshavotDataSource(DataSource):

    def __init__(self, request: PreChatManager, query: PineConeQuery,
                 text_extractor: TextExtractor = MachshavotTextExtractor()):
        super().__init__(query, text_extractor)
        self.request = request
        self.sparse_vector = None
        self.embed_query = self.request.query.embed_query
        self.top_k = 100
        self.text_extractor = MachshavotTextExtractor()

    async def get_data(self, sort: bool = True, top_k: int = 100) -> List[ScoredVector]:
        # Get raw search results from Pinecone
        pinecone_results = await PinconeHelper.pinecone_search_query(
            pinecone_index=self.request.connection_source.vector_db_instance.Machshavot,
            embeddings=self.embed_query,
            top_k=self.top_k, filter_dict=self.query.to_pinecone_filter,
            sparse_vector=self.sparse_vector)

        if sort:
            pinecone_results = sorted(pinecone_results, key=lambda x: x['score'], reverse=True)

        return pinecone_results
