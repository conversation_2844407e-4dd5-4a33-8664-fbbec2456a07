import json
from enum import Enum
from fastapi import APIRouter, Depends, Request

from ai_models import get_ai_provider_factory
from utils.dto import ActionsEnum, SearchTypeEnum, CollectionNameEnum, ListItem
from services.search.search_dto import SearchRequestDTO, SearchRequest, SearchResponse
from utils.analytics import create_document, get_search_analytics
from app.utils.statistics_utils import get_search_statistics_results, update_statistics_results_total_time
from middlewares.authorization import handle_request_product
from services.search.search_core import search
from middlewares.logging_utils import app_logger as logger
from api.dependencies.third_party_apis import get_cohere_client
from api.dependencies.vector_db import get_law_index, get_verdicts_index, get_books_summaries_index
from configs.app_config import SEARCH_TOP_K, VERSION,SHOULD_PREFORMED_JWT_VALIDATION_ON_SEARCH_REQUESTS
from middlewares.logging_utils import log_format
import copy


router = APIRouter()


class ListItemEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, ListItem):
            return obj.dict()
        return super().default(obj)




#

@router.post("/search", response_model=SearchResponse)
async def search_data(request: SearchRequest,  request_fastapi: Request,cohere_client = Depends(get_cohere_client), law_index = Depends(get_law_index), verdicts_index = Depends(get_verdicts_index),books_summaries_index = Depends(get_books_summaries_index),
                      ai_provider=Depends(get_ai_provider_factory)):
    # smart search on a given query
    from time import time
    from datetime import datetime
    if SHOULD_PREFORMED_JWT_VALIDATION_ON_SEARCH_REQUESTS:
        allow, message, statusCode = handle_request_product(request, request_fastapi)
        if not allow:
            return SearchResponse(statusCode=statusCode, message= message ,list=[],searchId=request.searchId)

    try:
        # get from req

        # Convert filters to a serializable format
        filters_list = []
        for f in copy.copy(request.filters):
            filter_dict = {
                'column': f.column.value if isinstance(f.column, Enum) else f.column,
                'condition': f.condition.value if isinstance(f.condition, Enum) else f.condition,
                'value': f.value
            }
            filters_list.append(filter_dict)

        search_analytics = get_search_analytics(threadId=request.searchId, type=SearchTypeEnum.smartSearch._value_, query=request.query, filters=json.dumps(filters_list), index=request.index, userId=request.userId)
        logger.info(log_format({'userId':request.userId,'Type':ActionsEnum.search,'Action':'START','id':'','Time':0,'Data':json.dumps(filters_list),'text':request.query, 'numbering':1}))

        search_statistics_results = get_search_statistics_results()
        time_start = time()
        search_request = SearchRequestDTO(search_analytics = search_analytics,
                                        user_id = request.userId,
                                        search_id = request.searchId,
                                        search_statistics_results = search_statistics_results,
                                        cohere_client = cohere_client,
                                        query = request.query,
                                        law_index = law_index,
                                        verdict_index = verdicts_index,
                                        books_summaries_index = books_summaries_index,
                                        max_results = SEARCH_TOP_K,
                                        filters = request.filters,
                                        filters_formula = request.filtersFormula,
                                          ai_provider=ai_provider,
                                          index = request.index)

        items = await search(search_request)
        time_search_api_end = time() - time_start
        update_statistics_results_total_time(search_statistics_results, time_search_api_end)
        logger.info(f"total_time_search_python_took_>> {round(time_search_api_end, 3)}")
        response = SearchResponse(
            statusCode=200,
            list=items,
            # statistics=search_statistics_results,
            searchId=request.searchId
        )

        return response

    except Exception as e:
        response = SearchResponse(
            statusCode=500,
            list=[],
            message=str(e),
            searchId=request.searchId
        )
        return response




