import traceback

from fastapi import APIRouter
from starlette.responses import JSONResponse

from configs.app_config import SHOULD_PREFORMED_JWT_VALIDATION_ON_CHAT_REQUESTS
from middlewares.authorization import handle_request_product
from middlewares.exceptions import raise_exception
from utils.pre_chat_dto import PreChatRequest, VectorDbInstance, ConnectionSource
from services.pre_chat import PreChatProcess
from redis_db.redis_chat_manager import RedisChatManager
from io import BytesIO
from typing import List, Dict, Any

from fastapi import Query, Depends, HTTPException
from fastapi.responses import Response
from motor.motor_asyncio import AsyncIOMotorClient
from pydantic import BaseModel
from pymongo.errors import PyMongoError
from starlette.requests import Request

from ai_models import get_ai_provider_factory
from api.dependencies.mongo_db import get_mongo_client
from api.dependencies.third_party_apis import get_cohere_client
from api.dependencies.vector_db import get_law_index, get_verdicts_index, get_books_summaries_index
from app.utils.statistics_utils import get_query_statistics_results
from middlewares.logging_utils import app_logger as logger
from services.autocomplete_utils import get_search_case_number, get_search_results
from services.chat_session_download import ChatReport
from services.post_chat.post_chat_core import post_chat_related
from utils.cache_db import get_redis
from utils.dto import KilFromRedisRequest, SourceEnum
from services.post_chat.post_chat_dto import PostChatRequest, PostChatResponse, PostChatRelatedRequest

ChatRouter = APIRouter()


async def get_connection_source(
        embed_client_instance=Depends(get_cohere_client),
        redis_instance=Depends(get_redis),
        ai_provider=Depends(get_ai_provider_factory),
        machshavot=Depends(get_books_summaries_index),
        verdict=Depends(get_verdicts_index),
        law=Depends(get_law_index),
        mongo_client=Depends(get_mongo_client),
):
    return ConnectionSource(
        embed_client_instance=embed_client_instance,
        redis_instance=redis_instance,
        ai_provider=ai_provider,
        vector_db_instance=VectorDbInstance(
            Machshavot=machshavot,
            Verdict=verdict,
            Law=law,
        ),
        mongo_client=mongo_client,
    )


@ChatRouter.post("/pre-chat")
async def pre_chat(request: PreChatRequest, request_fastapi: Request,
                   connection_source=Depends(get_connection_source)):
    try:
        if SHOULD_PREFORMED_JWT_VALIDATION_ON_CHAT_REQUESTS:
            allow, message, statusCode = handle_request_product(request, request_fastapi)

            if not allow:
                return JSONResponse(status_code=statusCode, content={
                    "message": message,
                    "chat_id": request.chatId,
                    "user_id": request.userId
                })

        pre_chat_process = PreChatProcess(request, connection_source)

        response= await pre_chat_process.process_request()
        return JSONResponse(status_code=200, content={
            "chat_id": response.chatId,
            "return_nearest_and_related": response.chat_settings.return_nearest_and_related,
            "suspect_docs": response.chat_settings.documents,
            "ner_entities": response.query.ner.entities,

        }
                            )

    except Exception as e:
        # Log the error with traceback
        logger.error(f"Error processing pre-chat request: {e},{traceback.print_exc()}")


        error_message = raise_exception(e)

        # Return a custom response with the error message and additional context
        return JSONResponse(
            status_code=500,
            content={
                "message": error_message,
                "chat_id": request.chatId,
                "user_id": request.userId
            },
        )


@ChatRouter.post("/post-chat", response_model=PostChatResponse)
async def post_chat(request: PostChatRequest, request_fastapi: Request, redis_pool=Depends(get_redis),
                    cohere_client=Depends(get_cohere_client),
                    law_index=Depends(get_law_index),
                    verdict_index=Depends(get_verdicts_index),
                    books_summaries_index=Depends(get_books_summaries_index),
                    ai_provider=Depends(get_ai_provider_factory)):
    try:
        if SHOULD_PREFORMED_JWT_VALIDATION_ON_CHAT_REQUESTS:
            allow, message, statusCode = handle_request_product(request, request_fastapi)

            if not allow:
                return JSONResponse(status_code=statusCode, content={
                    "message": message,
                    "chat_id": request.chatId,
                    "user_id": request.userId
                })

        query_statistics_results = get_query_statistics_results()

        post_chat_related_request = PostChatRelatedRequest(chat_id=request.chatId,
                                                           user_id=request.userId,
                                                           index=0,
                                                           redis_pool=redis_pool,
                                                           cohere_client=cohere_client,
                                                           law_index=law_index,
                                                           verdict_index=verdict_index,
                                                           books_summaries_index=books_summaries_index,
                                                           query_statistics_results=query_statistics_results,
                                                           domain=request.domain,
                                                           ai_provider=ai_provider)

        chat_relateds = await post_chat_related(post_chat_related_request)
        if not chat_relateds:
            message = raise_exception('NOT_EXIST_IN_REDIS')
            return JSONResponse(
                status_code=500,
                content={
                    "message": message,
                    "chat_id": request.chatId
                },
            )

        return JSONResponse(status_code=200, content={"related": chat_relateds})

    except Exception as e:
        print(str(traceback.print_exc()))
        logger.error(f'Error in post-chat with chat_id {request.chatId}. The error details - {str(traceback.print_exc())}')
        message = raise_exception('POST_CHAT_ERROR', info=str(e))
        return JSONResponse(
            status_code=500,
            content={
                "message": message,
                "chat_id": request.chatId
            },
        )


@ChatRouter.post("/end-chat")
async def kill_chat_id_in_redis(request: KilFromRedisRequest, redis_pool=Depends(get_redis)):
    try:
        pass
        # await delete_keys_with_pattern(redis_pool, request.chatId)
    except Exception as e:
        logger.error(f'Error in end-chat with chat_id {request.chatId}. The error details - {e}')
        return {'statusCode': 500}

    return {'statusCode': 200}


class ChatDownloadRequest(BaseModel):
    chat_id: str
    # user_name: str = Field(min_length=3, max_length=50, regex="^[a-zA-Z0-9_]+$")  # Example: Alphanumeric with underscores
    user_name: str
    user_id: str


# @ChatRouter.post('/chat-session-download')
# async def chat_session_download(chat_download: ChatDownloadRequest, redis_pool=Depends(get_redis)):
#     try:
#         chat_id = chat_download.chat_id
#
#         chat_session = await RedisChatManager(redis_pool).load_from_redis(chat_id)
#         if not chat_session:
#             # If no chat session found, return custom 404 status code with a message
#             return Response(
#                 content="Chat session not found",
#                 status_code=404
#             )
#
#         doc_bytes = ChatReport(chat_session, chat_download.user_name).run()
#
#         # Return the bytes as a response
#         return Response(
#             content=doc_bytes.read(),
#             media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
#             headers={"Content-Disposition": "attachment; filename=generated.docx"}
#         )
#     except Exception as e:
#         logger.error(f'Error in chat-session-download with chat_id {chat_id}. The error details - {e}')
#         return JSONResponse(
#             status_code=500,
#             content={
#                 "status": "error",
#                 "message": "Internal Server Error",
#                 "chat_id": chat_download.chat_id
#             },
#         )


@ChatRouter.get('/get-chat-session-download')
async def get_chat_session_download(chat_id: str, user_name: str, redis_pool=Depends(get_redis)):
    try:
        from datetime import datetime
        import pytz

        # Define the Israel timezone
        formatted_time = datetime.now(pytz.timezone("Asia/Jerusalem")).strftime("%Y_%m_%d_%H_%M")

        file_name = f"TechdinChatSession_{formatted_time}.docx"
        chat_id = chat_id
        try:
            _,chat_session = await RedisChatManager(redis_pool).load_chat_from_redis(chat_id, "", "")

        except:
            chat_session = None

        if not chat_session:
            # If no chat session found, return custom 404 status code with a message
            return Response(
                content="Chat session not found",
                status_code=404
            )

        doc = ChatReport(chat_session, user_name).run()
        byte_stream = BytesIO()
        doc.save(byte_stream)
        byte_stream.seek(0)

        # Return the bytes as a response
        return Response(
            content=byte_stream.read(),
            media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            headers={"Content-Disposition": f"attachment; filename={file_name}"}
        )
    except Exception as e:
        logger.error(f'Error in chat-session-download with chat_id {chat_id}. The error details - {e}')
        raise HTTPException(status_code=500, detail="Internal Server Error")


@ChatRouter.get('/autocomplete/case-number')
async def autocomplete(
        q: str = Query(..., min_length=1, max_length=100),
        source: SourceEnum = SourceEnum.verdicts,
        mongo_client: AsyncIOMotorClient = Depends(get_mongo_client)
) -> Dict[str, List[Dict[str, Any]]]:
    """
    Autocomplete search for case numbers.
    """
    suggestions = []
    try:

        suggestions = await get_search_results(mongo_client, get_search_case_number(q, source))

        return {"suggestions": suggestions}

    except PyMongoError as e:
        return {"message": f"An error occurred: {str(e)}", "suggestions": suggestions}
    except Exception as e:
        return {"message": f"An unexpected error occurred: {str(e)}", "suggestions": suggestions}


@ChatRouter.get("/health")
def read_health():
    return {"message": "OK"}
