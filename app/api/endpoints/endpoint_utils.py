from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse

from app.db_utils.data_helper import ChunkData, SourceEnum, get_chunk_data as process_chunk_data
from middlewares.logging_utils import app_logger as logger

utils_routers = APIRouter(tags=["utils"])


@utils_routers.post("/chunk_data")
async def get_chunk_data(chunk_data: ChunkData):
    """
    Endpoint to retrieve chunk data based on source type.
    Routes to appropriate function based on source:
    - law/verdict: uses get_chunk_verdict_law
    - machshavot: uses import_text_by_sub_chapter
    """
    try:
        # Call the data helper function
        result_data = process_chunk_data(chunk_data)
        if not result_data:
            return JSONResponse(status_code=404, content={"detail": "No data found for the provided chunk data."})

        return JSONResponse(
            status_code=200,
            content={
                "data": result_data
            }
        )

    except ValueError as e:
        logger.error(f"Validation error in get_chunk_data: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error in get_chunk_data: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal error retrieving chunk data")
