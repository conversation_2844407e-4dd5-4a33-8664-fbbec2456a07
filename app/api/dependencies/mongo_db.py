
from configs.app_config import M<PERSON><PERSON><PERSON>_USER_NAME, <PERSON><PERSON><PERSON><PERSON>_PASSWORD, MONGO_URL
from middlewares.logging_utils import app_logger as logger
from bson.codec_options import CodecOptions
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorCollection
from pytz import timezone
mongo_client = None

user_timezone = timezone("Asia/Jerusalem")
_CODEC_OPTIONS = CodecOptions(tz_aware=True, tzinfo=user_timezone)

async def create_connection_mongo():
    try:
        uri = MONGO_URL.format(MONGO_USER_NAME,MONGO_PASSWORD)

        client = AsyncIOMotorClient(uri)
    except Exception as e:
        logger.error("Failed to connect to MongoDB")
        print(f"Error occurred: {e}")
        raise e
        # return None

    return client


async def get_mongo_client():
    global mongo_client
    if mongo_client is None:
        mongo_client = await create_connection_mongo()
        logger.info('MongoDB connection has been created')
    return mongo_client







async def get_collection_with_tz(
    mongo_client: AsyncIOMotorClient, db_name: str, collection_name: str
) -> AsyncIOMotorCollection:
    return mongo_client[db_name].get_collection(collection_name, codec_options=_CODEC_OPTIONS)
