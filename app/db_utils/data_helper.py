from typing import Dict, Any
from enum import Enum
import re
import uuid

from pydantic import BaseModel, model_validator

import app.models
import configs.app_config as config
from api.dependencies.relational_db import get_sql_session
from middlewares.logging_utils import app_logger as logger


class SourceEnum(str, Enum):
    """
    Enum for valid chunk data sources.
    """
    LAW = "law"
    VERDICT = "verdict"
    MACHSHAVOT = "machshavot"


class ChunkData(BaseModel):
    """
    Model for chunk data with routing information.
    Validates cid format based on source type:
    - machshavot: must be UUID4 format (36 characters)
    - law/verdict: must be format "number-number" (e.g., "123-0")
    """
    cid: str
    type: str
    source: SourceEnum

    @model_validator(mode='after')
    def validate_cid_format(self) -> 'ChunkData':
        """
        Validate cid format based on source type after all fields are set.
        """
        if not self.cid:
            raise ValueError("cid cannot be empty")

        if self.source == SourceEnum.MACHSHAVOT:
            # Validate UUID4 format for machshavot
            try:
                # Check if it's a valid UUID4 format
                uuid_obj = uuid.UUID(self.cid, version=4)
                # Ensure it's exactly 36 characters (standard UUID format with hyphens)
                if len(self.cid) != 36:
                    raise ValueError(f"Machshavot cid must be exactly 36 characters (UUID4 format), got {len(self.cid)} characters")
            except ValueError as e:
                if "36 characters" in str(e):
                    raise e
                raise ValueError(f"Machshavot cid must be a valid UUID4 format, got: {self.cid}")

        elif self.source in [SourceEnum.LAW, SourceEnum.VERDICT]:
            # Validate "number-number" format for law/verdict
            pattern = r'^\d+-\d+$'
            if not re.match(pattern, self.cid):
                raise ValueError(f"Law/Verdict cid must be in format 'number-number' (e.g., '123-0'), got: {self.cid}")

        else:
            # This shouldn't happen due to enum validation, but keeping for safety
            raise ValueError(f"Unknown source type for cid validation: {self.source}")

        return self


def get_chunk_verdict_law(chunk_id: str, strict=False) -> Dict[str, str]:
    """
    Get chunk text for law/verdict sources by chunk ID.
    """
    try:
        table = app.models.AiEmbed
        with get_sql_session() as sql_session:
            results = (sql_session.query(table.cId, table.cText)
                      .filter(table.cId == chunk_id)
                      .all())

        results_dict = {str(cid): text for cid, text in results}
        if strict and not results_dict:
            raise ValueError(f"No text found for chunk_id: {chunk_id}")
        return results_dict

    except Exception as e:
        logger.error(f'Error in get_chunk_verdict_law function with chunk id {chunk_id}. error - {e}')
        raise Exception(str(e) if str(e) in config.ERROR_KEYS else 'GET_CHUNK_TEXT_ERROR')


def import_text_by_sub_chapter(sub_chapter_id: str) -> Dict[str, str]:
    """
    Get chunk text for machshavot sources by sub-chapter ID.
    """
    try:
        table = app.models.AiBookEmbed
        with get_sql_session() as sql_session:
            results = (
                sql_session.query(
                    table.subChapterId,
                    table.cText,
                )
                .filter(table.subChapterId == sub_chapter_id)
                .all()
            )

        results_dict = {str(cid): text for cid, text in results}
        return results_dict

    except Exception as e:
        logger.error(f"Error importing text by sub-chapter: {e}", exc_info=True)
        raise Exception(f"Error importing text by sub-chapter: {str(e)}")


def get_chunk_data(chunk: ChunkData) -> Dict[str, Any]:
    """
    Function to process and return chunk data based on source type.
    Routes to appropriate function based on source:
    - law/verdict: uses get_chunk_verdict_law
    - machshavot: uses import_text_by_sub_chapter

    Args:
        chunk (ChunkData): Single chunk data to process.

    Returns:
        Dict[str, Any]: Dictionary containing the retrieved chunk data.

    Raises:
        ValueError: If chunk is None or invalid source type
        Exception: If database operations fail
    """
    if not chunk:
        raise ValueError("No chunk provided")

    # Route based on source type using enum
    if chunk.source in [SourceEnum.LAW, SourceEnum.VERDICT]:
        try:
            result = get_chunk_verdict_law(chunk.cid, strict=False)
            logger.info(f"Retrieved {chunk.source.value} chunk: {chunk.cid}")
            return result
        except Exception as e:
            logger.error(f"Error retrieving {chunk.source.value} chunk {chunk.cid}: {e}", exc_info=True)
            raise Exception(f"Error retrieving {chunk.source.value} chunk: {str(e)}")

    elif chunk.source == SourceEnum.MACHSHAVOT:
        try:
            result = import_text_by_sub_chapter(chunk.cid)
            logger.info(f"Retrieved machshavot sub-chapter: {chunk.cid}")
            return result
        except Exception as e:
            logger.error(f"Error retrieving machshavot chunk {chunk.cid}: {e}", exc_info=True)
            raise Exception(f"Error retrieving machshavot chunk: {str(e)}")

    else:
        # This should never happen due to enum validation, but keeping for safety
        logger.warning(f"Unknown source type: {chunk.source} for chunk {chunk.cid}")
        raise ValueError(f"Unknown source type: {chunk.source}")


if __name__ == '__main__':
    # Example usage with enum and validation
    try:
        # Valid examples:
        # law_chunk = ChunkData(cid="100-0", type="chunk", source=SourceEnum.LAW)
        # verdict_chunk = ChunkData(cid="1000143-0", type="chunk", source=SourceEnum.VERDICT)
        machshavot_chunk = ChunkData(cid="60ea0567-850b-4770-959c-0002a8a2c113", type="sub_chapter", source=SourceEnum.MACHSHAVOT)

        print("All validations passed!")
        # print(f"Law chunk: {law_chunk}")
        # print(f"Verdict chunk: {verdict_chunk}")
        print(f"Machshavot chunk: {machshavot_chunk}")

        # Get chunk data
        # law_data = get_chunk_data(law_chunk)
        # print(f"Law data: {law_data}")
        # verdict_data = get_chunk_data(verdict_chunk)
        # print(f"Verdict data: {verdict_data}")
        machshavot_data = get_chunk_data(machshavot_chunk)
        print(f"Machshavot data: {machshavot_data}")

    except Exception as e:
        print(f"Error: {e}")

    # Examples of invalid formats (will raise validation errors):
    # try:
    #     # This will fail - invalid UUID for machshavot
    #     invalid_machshavot = ChunkData(cid="invalid-uuid", type="sub_chapter", source=SourceEnum.MACHSHAVOT)
    # except Exception as e:
    #     print(f"Expected validation error for invalid UUID: {e}")
    #
    # try:
    #     # This will fail - invalid format for law
    #     invalid_law = ChunkData(cid="invalid_format", type="chunk", source=SourceEnum.LAW)
    # except Exception as e:
    #     print(f"Expected validation error for invalid law format: {e}")
    #
    #
