from collections import defaultdict
from time import time
from typing import List, Dict, Any

from sqlalchemy.orm import aliased
from sqlalchemy import delete, desc, text
from sqlalchemy.orm.exc import NoResultFound

from api.dependencies.relational_db import get_sql_session
from utils.dto import ActionsEnum, TypeEnum, ETLRequestStatusEnum
from configs import app_config as config
from middlewares.logging_utils import app_logger as logger, log_format
import app.models
import datetime
import os
import json


def is_running_in_docker():
    return os.environ.get('RUNNING_IN_DOCKER') == 'True'


def insert_to_bm25_with_fifo(maagarId, new_row_data, max_rows=config.MAX_BM25_ROWS):
    # Check the current number of rows
    table = app.models.LawsBM25ParamsJson if maagarId == 1 else app.models.VerdictsBM25ParamsJson
    with get_sql_session() as sql_session:
        current_row_count = sql_session.query(table).count()

        # If the number of rows exceeds max_rows, delete the oldest row
        if current_row_count >= max_rows:
            oldest_row = sql_session.query(table).order_by(table.Id).first()
            sql_session.delete(oldest_row)

        # Insert the new row
        new_row = table(**new_row_data)
        sql_session.add(new_row)
        sql_session.commit()


def get_last_bm25_entry_and_save_in_local_file(maagarId):
    # Query the table and order by Id in descending order
    table = app.models.LawsBM25ParamsJson if maagarId == 1 else app.models.VerdictsBM25ParamsJson
    with get_sql_session() as sql_session:
        last_entry = sql_session.query(table).order_by(desc(table.Id)).first()

    bm25_name = config.BM25_LAW_PARAMS if maagarId == 1 else config.BM25_VERDICT_PARAMS

    with open(bm25_name, 'w') as json_file:
        json.dump(json.loads(last_entry.bm25), json_file)

    return bm25_name  # json.loads(last_entry.bm25)


def get_last_bm25_from_sql(maagarId):
    table = app.models.LawsBM25ParamsJson if maagarId == 1 else app.models.VerdictsBM25ParamsJson

    with get_sql_session() as sql_session:
        last_entry = sql_session.query(table).order_by(desc(table.Id)).first()

    return json.loads(last_entry.bm25)


def get_ratio_by_txt_ids(txt_ids: List[int], strict=False):
    table = app.models.AiRatio
    with get_sql_session() as sql_session:
        results = (sql_session.query(table.txtId, table)
                   .filter(table.txtId.in_(txt_ids))
                   .all())
    if results:
        for row in results:
            results_dict = {row[0]: row[1].summary}
        return results_dict
    return None


def import_chunks_by_sub_chapter(sub_chapter_ids: List[str], strict=False) -> defaultdict[Any, list]:
    try:

        table = app.models.AiBookEmbed
        ai_books = aliased(app.models.AiBook)
        with get_sql_session() as sql_session:
            results = (
                sql_session.query(
                    table.subChapterId,
                    table.cText,
                    table.cId,
                    table.nTokens,
                    table.pageNumber,
                    ai_books.totalPages,
                    ai_books.bookName,
                    ai_books.year,
                    ai_books.legalFieldId,
                    ai_books.bookTypeId,

                )
                .join(ai_books, table.bookId == ai_books.bookId)
                .filter(table.subChapterId.in_(sub_chapter_ids))
                .all()
            )

        results_dict = defaultdict(list)
        for row in results:
            results_dict[str(row[0])].append({
                'cText': row[1],
                'cId': row[2],
                'nTokens': row[3],
                'pageNumber': row[4],
                'subChapterId': row[0],
                'totalPages': row[5],
                'bookName': row[6],
                'year': row[7],
                'legal_field_id': row[8],
                'book_type_id': row[9],

            })

        # Identify and log missing sub_chapter_ids
        # missing_sub_chapter_ids = set(sub_chapter_ids) - set(results_dict.keys())
        # if missing_sub_chapter_ids:
        #     msg = f"get_sub_chapter_text: Missing sub_chapter_ids: {missing_sub_chapter_ids}"
        #     if strict:
        #         raise RuntimeError(msg)
        #     logger.warning(
        #         f"In get_sub_chapter_text function, Missing sub_chapter_ids: {missing_sub_chapter_ids}"
        #     )
        #     for key in missing_sub_chapter_ids:
        #         if key in results_dict:
        #             del results_dict[str(key)]

        return results_dict
    except Exception as e:
        raise Exception(str(e) if str(e) in config.ERROR_KEYS else "GET_SUB_CHAPTER_TEXT_ERROR")


def get_chunk_texts(chunk_ids: List[str], strict=False) -> Dict[str, str]:
    try:
        table = app.models.AiEmbed
        with get_sql_session() as sql_session:
            results = (sql_session.query(table.cId, table.cText)
                       .filter(table.cId.in_(chunk_ids))
                       .all())

        results_dict = {row[0]: row[1] for row in results}

        # Identify and log missing txt_ids
        missing_chunk_ids = list(set(chunk_ids) - set(results_dict.keys()))
        if missing_chunk_ids:
            msg = f"get_chunk_texts: Missing chunk_ids: {missing_chunk_ids}"
            if strict:
                raise RuntimeError(msg)
            logger.warning(f'In get_chunk_texts function, Missing chunk ids: {missing_chunk_ids}')
            for key in missing_chunk_ids:
                if key in results_dict:
                    del results_dict[str(key)]

        return results_dict
    except Exception as e:
        missing = ', '.join(map(str, chunk_ids))
        logger.error(f'Error in get_chunk_texts function with chunk ids {missing}. error - {e}')
        raise Exception(str(e) if str(e) in config.ERROR_KEYS else 'GET_CHUNK_TEXT_ERROR')


def get_chunk_metadata_rows(txt_ids: List[str], strict=False) -> Dict[str, app.models.AiMetadata]:
    try:
        table = app.models.AiMetadata
        with get_sql_session() as sql_session:
            results = (sql_session.query(table.txtId, table)
                       .filter(table.txtId.in_(txt_ids))
                       .all())

        results_dict = {row[0]: row[1] for row in results}

        # Identify and log missing txt_ids
        missing_txt_ids = set(txt_ids) - set(results_dict.keys())
        if missing_txt_ids:
            msg = f" get_chunk_metadata_rows: Missing chunk_ids get_chunk_metadata_rows: {', '.join(map(str, missing_txt_ids))}"
            if strict:
                raise RuntimeError(msg)
            # logger.warning(msg)

        return results_dict
    except Exception as e:
        logger.error(f'Error in get_chunk_metadata_rows function with txt_ids {txt_ids}. error - {e}')
        raise Exception(str(e) if str(e) in config.ERROR_KEYS else 'GET_CHUNK_METADATA_ROWS_ERROR')


def get_chunk_law_metadata_rows(txt_ids: List[str], strict=False) -> Dict[str, str]:
    table = app.models.AiLawMetadata
    with get_sql_session() as sql_session:
        results = (sql_session.query(table.txtId, table)
                   .filter(table.txtId.in_(txt_ids))
                   .all())
    results_dict = {row[0]: row[1] for row in results}

    # Identify and log missing txt_ids
    missing_txt_ids = set(txt_ids) - set(results_dict.keys())
    if missing_txt_ids:
        msg = f" get_chunk_law_metadata_rows: Missing chunk_ids get_chunk_law_metadata_rows: {', '.join(map(str, missing_txt_ids))}"
        if strict:
            raise RuntimeError(msg)
        logger.warning(msg)

    return results_dict


def get_chunk_vaerdict_metadata_rows(txt_ids: List[str], strict=False) -> Dict[str, str]:
    table = app.models.VerdictsAiMetadata
    with get_sql_session() as sql_session:
        results = (sql_session.query(table.txtId, table)
                   .filter(table.txtId.in_(txt_ids))
                   .all())
    results_dict = {row[0]: row[1] for row in results}

    # Identify and log missing txt_ids
    missing_txt_ids = set(txt_ids) - set(results_dict.keys())
    if missing_txt_ids:
        msg = f" get_chunk_vaerdict_metadata_rows: Missing chunk_ids get_chunk_vaerdict_metadata_rows: {', '.join(map(str, missing_txt_ids))}"
        if strict:
            raise RuntimeError(msg)
        logger.warning(msg)

    return results_dict


def get_related_referenced_documents(txt_id):
    procedure_name = "[cmsHashavim].[dbo].[ai_sp_GetRelatedReferencedDocuments]"
    sql_expression = text(f"EXEC {procedure_name} @documentId  = :documentId")

    with get_sql_session() as sql_session:
        results = sql_session.execute(sql_expression, {"documentId": txt_id}).fetchall()
    procedure_results_list = [row._asdict() for row in results]

    return procedure_results_list


def get_referenced_and_related_ids(txt_id):
    procedure_related_name = "[cmsHashavim].[dbo].[ai_sp_GetRelatedDocuments]"
    procedure_referenced_name = "[cmsHashavim].[dbo].[ai_sp_GetReferencedDocuments]"
    sql_expression_related = text(f"EXEC {procedure_related_name} @documentId = :documentId, @count = :count")
    sql_expression_referenced = text(f"EXEC {procedure_referenced_name} @documentIds = :documentId, @count = :count")
    with get_sql_session() as sql_session:
        related_results = sql_session.execute(sql_expression_related, {"documentId": txt_id,
                                                                       "count": config.REFERENCED_AND_RELATED_COUNT}).fetchall()
        referenced_results = sql_session.execute(sql_expression_referenced, {"documentId": txt_id,
                                                                             "count": config.REFERENCED_AND_RELATED_COUNT}).fetchall()
    related_results_list = [row._asdict() for row in related_results]
    referenced_results_list = [row._asdict() for row in referenced_results]

    referenced_and_related_ids = {
        "related_ids": {row.get("related_id"): row.get("related_title") for row in related_results_list},
        "referenced_ids": [row.get("referenced_id") for row in referenced_results_list]
    }

    return referenced_and_related_ids


def get_resources_referrers_count(txt_ids: List[int]) -> Dict[int, int]:
    """returns a dict from the txt_id to the refers count"""
    raise DeprecationWarning(
        "There is a bug in the referrer count in cms_main. you should use get_resources_referrers_ids and use len() on the list of referrers directly")
    cms_main = app.models.ResourcesMain
    with get_sql_session() as sql_session:
        results = sql_session.query(
            cms_main.txtId.label('txt_id'),
            cms_main.referrerCount.label('referrer_count')
        ).filter(
            # cms_main.maagarId == 2,  # only verdicts
            # cms_main.isPublish == 1,  # only published
            cms_main.txtId.in_(txt_ids)
        ).all()

    return {row.txt_id: row.referrer_count for row in results}


def get_resources_referrers_ids(txt_ids: List[int]) -> Dict[int, List[int]]:
    """ returns a dict from txt_id to all ids that refer to it"""
    cms_main = app.models.ResourcesMain
    cms_DocumentRelated = app.models.DocumentsRelated

    with get_sql_session() as sql_session:
        filtered_main = sql_session.query(cms_main.txtId).filter(
            # cms_main.maagarId == 2,  # only verdicts
            # cms_main.isPublish == 1, # only published - TODO should we really filter on this? If so, should we also filter on this elsewhere?
            cms_main.txtId.in_(txt_ids)
        ).subquery()

        filtered_related = sql_session.query(cms_DocumentRelated.txtId, cms_DocumentRelated.relId).filter(
            cms_DocumentRelated.relId.in_(txt_ids)
        ).subquery()

        main_alias = aliased(filtered_main)
        related_alias = aliased(filtered_related)

        results = sql_session.query(
            main_alias.c.txtId.label('original_id'),
            related_alias.c.txtId.label('related_id')
        ).join(
            related_alias, related_alias.c.relId == main_alias.c.txtId
        ).all()

    referrers_dict = defaultdict(list)
    for row in results:
        referrers_dict[row.original_id].append(row.related_id)

    for k in referrers_dict:
        referrers_dict[k] = sorted(set(referrers_dict[k]))

    return referrers_dict


def store_in_chunk_db(txt_ids, rows: List[Dict], status):
    to_remove = status == ETLRequestStatusEnum.remove
    [dict.update({'updatedAt': datetime.date.today()}) for dict in rows]
    with get_sql_session() as sql_session:
        stmt = (
            delete(app.models.AiEmbed).
            where(app.models.AiEmbed.txtId.in_(txt_ids))  # Match multiple values for deletion
        )
        sql_session.execute(stmt)
        if not to_remove:
            sql_session.bulk_insert_mappings(app.models.AiEmbed, rows)
        sql_session.commit()


def _store_simple_ai_gen_resource(txt_id, summary, table):
    # Check the version number
    with get_sql_session() as sql_session:
        existing_summaries_count = sql_session.query(table).filter_by(txtId=txt_id).count()
        next_version = existing_summaries_count + 1

        # Create an instance of AiSummary and add it to the session
        new_summary = table(
            txtId=txt_id,
            summary=summary,
            updatedAt=datetime.date.today(),
            version=next_version
        )

        sql_session.add(new_summary)

        # Commit the session to save changes to the database
        sql_session.commit()


def store_summary(txt_id, summary):
    _store_simple_ai_gen_resource(txt_id, summary, app.models.AiSummary)


def store_ratio(txt_id, ratio):
    _store_simple_ai_gen_resource(txt_id, ratio, app.models.AiRatio)


def get_all_cids(maagar_id, sql_session=None, batch_size=1000):
    close_session_at_end = sql_session is None
    if sql_session is None:
        sql_session = get_sql_session()

    # Query to get the cIds based on maagarId = 1
    query = sql_session.query(app.models.AiEmbed.cId).join(
        app.models.ResourcesMain, app.models.AiEmbed.txtId == app.models.ResourcesMain.txtId
    ).filter(app.models.ResourcesMain.maagarId == maagar_id).order_by(app.models.AiEmbed.cId)

    # Using a for loop to fetch 1000 cId at a time
    offset = 0

    while True:
        cids = query.offset(offset).limit(batch_size).all()
        if not cids:
            break
        yield [cid_tuple[0] for cid_tuple in cids]

        offset += batch_size

    if close_session_at_end:
        sql_session.close()


def get_maagar_id_and_total_tokens_for_txt_id(txt_id: int) -> tuple:
    """
    Retrieve the maagarId and total_tokens for a given txt_id from the AiMetadata table.
    
    Args:
    - sql_session (session): The database session.
    - txt_id (int): The ID for which to retrieve the maagarId and total_tokens.
    
    Returns:
    - tuple: (maagarId, total_tokens)
    """
    try:
        with get_sql_session() as sql_session:
            result = sql_session.query(app.models.AiMetadata.maagarId, app.models.AiMetadata.total_tokens).filter_by(
                txtId=txt_id).one()
        return result.maagarId, result.total_tokens
    except NoResultFound:
        logger.warning(
            f'In get_maagar_id_and_total_tokens_for_txt_id function, txt_id: {txt_id} not found in AiMetadata table.')
        return None, None
    except Exception as e:
        logger.error(f'Error in get_maagar_id_and_total_tokens_for_txt_id function. error - {e}')
        raise Exception(str(e) if str(e) in config.ERROR_KEYS else 'GET_MAAGAR_ID_AND_TOTAL_TOKENS_ERROR')

        # raise RuntimeError(f"txt_id: {txt_id} not found in AiMetadata table.")


def get_ai_metadata_by_txt_ids(txt_ids: List[int], columns: List[str]) -> Dict[int, Dict[str, Any]]:
    try:
        with get_sql_session() as sql_session:
            selected_columns = [getattr(app.models.AiMetadata, col) for col in columns]

            results = sql_session.query(
                app.models.AiMetadata.txtId,
                *selected_columns
            ).filter(app.models.AiMetadata.txtId.in_(txt_ids)).all()
        return {row.txtId: {col: getattr(row, col) for col in columns} for row in results}
    except NoResultFound:
        logger.warning(
            f'In get_maagar_id_and_total_tokens_for_txt_id function, txt_ids: {txt_ids} not found in AiMetadata table.')
    except Exception as e:
        logger.error(f'Error in get_maagar_id_and_total_tokens_for_txt_id function. error - {e}')
        raise Exception(str(e) if str(e) in config.ERROR_KEYS else 'GET_MAAGAR_ID_AND_TOTAL_TOKENS_ERROR')


def convert_procedure_to_txt_id(pt, pn, user_id):
    try:
        start = time()
        more_then_one_result = False

        procedure_name = f"[{config.AI_DATABASE_NAME}].[dbo].[GetVerdictMetadataByProcedureTypeAndProcedureNumber]"

        sql_expression = text(f"EXEC {procedure_name} @Pt = :Pt, @Pn = :Pn")

        with get_sql_session() as sql_session:
            results = sql_session.execute(sql_expression, {"Pt": pt, "Pn": pn}).fetchall()

        if results:
            more_then_one_result = True if len(results) > 1 else False
            results_list = [row._asdict() for row in results]
            # TODO  - if more than 1 result we should ask the user which case - tie each result to its txtId for easy next iteration - (same as pn with multiple pt ) - usually Location difrentiation
            # Define the order of decision_name preferences
            decision_name_priority = ['פסק דין', 'גזר דין', 'הכרעת דין']

            # Filter the results based on the priority of decision_name
            filtered_results = [result for result in results_list if result['decision_name'] in decision_name_priority]

            if filtered_results:
                # Sort the filtered results by the priority and then by referrer_count and pages
                filtered_results.sort(key=lambda x: (
                    decision_name_priority.index(x['decision_name']), -x.get('referrer_count', 0), -x.get('pages', 0)))
                selected_result = filtered_results[0]
            else:
                if more_then_one_result:
                    # If no preferred decision_name is found, select based on max referrer_count and max pages
                    sorted_results = sorted(results_list,
                                            key=lambda x: (-x.get('referrer_count', 0), -x.get('pages', 0)))
                    selected_result = sorted_results[0]
                else:
                    selected_result = results_list[0]

            logger.info(log_format({
                'userId': user_id,
                'Type': ActionsEnum.search,
                'Action': 'get_procedures_txt_id',
                'id': selected_result.get('txtId'),
                'Time': time() - start,
                'Domain': TypeEnum.verdict,
                'text': 'question',
                'numbering': 7
            }))

            return selected_result.get('txtId'), more_then_one_result

        return 0, False

    except Exception as e:
        logger.info(f'Error in convert_procedure_to_txt_id: {e}')
        return 0,False


def check_ethics_procedure(txt_id):
    AiMetadata = app.models.AiMetadata
    with get_sql_session() as sql_session:
        try:
            result = sql_session.query(AiMetadata).filter_by(txtId=txt_id).one()
            if result.court_name in ["בית הדין המשמעתי הארצי של לשכת עורכי הדין",
                                     "בית הדין המשמעתי המחוזי של לשכת עורכי הדין"]:
                return True
            if result.tribunal_id == 1:
                return True
            return False
        except NoResultFound:
            return False


if __name__ == '__main__':
    print(check_ethics_procedure(6001)
          )

