# Chat History Encryption Implementation

## Overview

This implementation adds end-to-end encryption for chat conversations stored in MongoDB, along with comprehensive audit logging and data integrity verification using SHA-256 checksums.

## Features

### 🔐 Encryption
- **AES-GCM Encryption**: Conversations are encrypted using AES-GCM with 256-bit keys
- **User-Specific Keys**: Each user has their own encryption key managed by the UserKeyManager
- **Backward Compatibility**: Plain conversation field is maintained for migration purposes

### 🔍 Data Integrity
- **SHA-256 Checksums**: Each conversation has a hash for integrity verification
- **Hash Verification**: Automatic verification during decryption to detect tampering
- **Consistent Ordering**: Conversations are sorted by timestamp and index for consistent hashing

### 📊 Audit Logging
- **Comprehensive Logging**: All encryption/decryption operations are logged
- **Firestore Integration**: Audit logs are sent to the existing analytics system
- **Detailed Tracking**: Includes user_id, user_key_id, chat_id, timestamps, and operation status

## Architecture

### Core Components

1. **EncryptionManager** (`app/chat_history/security/encrypt_manger.py`)
   - Handles AES-GCM encryption/decryption
   - Generates and verifies conversation hashes
   - Singleton pattern for consistent key management

2. **AuditService** (`app/chat_history/services/audit_service.py`)
   - Centralized audit logging for all encryption operations
   - Integration with existing analytics system
   - Structured logging with enums for consistency

3. **Enhanced MongoDB Schema** (`app/chat_history/schema/mongo_models.py`)
   - `encrypted_conversation`: Encrypted conversation data (bytes)
   - `conversation_hash`: SHA-256 hash for integrity verification
   - `conversation`: Plain conversation (kept for backward compatibility)

4. **Updated ChatHistoryManager** (`app/chat_history/services/encrypt_data_manager.py`)
   - Automatic encryption during storage
   - Automatic decryption during retrieval
   - Comprehensive error handling and audit logging

## Data Flow

### Storage Flow
1. **Redis → Mongo Conversion**: `convert_redis_to_mongo()`
   - Generate conversation hash using SHA-256
   - Encrypt conversation using user's encryption key
   - Log hash generation and encryption operations
   - Store encrypted data, hash, and plain conversation (for compatibility)

2. **Store Operation**: `ChatHistoryManager.store_chat()`
   - Get user key ID for audit logging
   - Convert with encryption enabled
   - Log successful/failed storage operations

### Retrieval Flow
1. **Mongo → Redis Conversion**: `convert_mongo_to_redis()`
   - Decrypt conversation using user's encryption key
   - Verify conversation hash for integrity
   - Log decryption and hash verification operations
   - Fallback to plain conversation if decryption fails

2. **Extract Operation**: `ChatHistoryManager.extract_chat_history()`
   - Get user key ID for audit logging
   - Convert with decryption enabled
   - Log successful/failed retrieval operations

## Security Features

### Encryption
- **AES-GCM**: Authenticated encryption providing both confidentiality and integrity
- **256-bit Keys**: Strong encryption keys generated using Fernet
- **Unique Nonces**: Each encryption operation uses a unique nonce
- **Master Key**: KMS master key protects user-specific keys

### Data Integrity
- **SHA-256 Hashing**: Cryptographically secure hash function
- **Consistent Serialization**: Deterministic JSON serialization for consistent hashes
- **Automatic Verification**: Hash verification on every decryption operation
- **Tamper Detection**: Immediate detection of data corruption or tampering

### Audit Trail
- **Complete Logging**: Every encryption/decryption operation is logged
- **Structured Data**: Consistent audit log format with enums
- **Error Tracking**: Failed operations are logged with detailed error messages
- **User Tracking**: All operations are tied to specific users and user keys

## Usage Examples

### Basic Usage (Automatic)
```python
# The encryption is automatic when using ChatHistoryManager
chat_manager = ChatHistoryManager(
    encryption_manager=encryption_manager,
    mongo_client=mongo_client,
    user_key_manager=user_key_manager
)

# Store chat (automatically encrypts)
await chat_manager.store_chat(redis_chat_data)

# Retrieve chat (automatically decrypts)
redis_chat = await chat_manager.extract_chat_history(chat_id)
```

### Manual Encryption/Decryption
```python
# Initialize encryption manager
EncryptionManager.initialize(kms_master_key)
encryption_manager = EncryptionManager.get_instance()

# Encrypt conversation
encrypted_data = encryption_manager.encrypt_conversation(conversation)

# Generate hash
conversation_hash = encryption_manager.generate_conversation_hash(conversation)

# Decrypt conversation
decrypted_conversation = encryption_manager.decrypt_conversation(encrypted_data)

# Verify hash
is_valid = encryption_manager.verify_conversation_hash(decrypted_conversation, conversation_hash)
```

## Configuration

### Environment Variables
- `KMS_MASTER_KEY`: Master encryption key (32 bytes)
- `MONGO_DB_HISTORY`: MongoDB database name for chat history
- `MONGO_CHAT_HISTORY_COLLECTION`: MongoDB collection name

### Dependencies
- `cryptography`: For AES-GCM encryption
- `pymongo`: For MongoDB operations
- `pydantic`: For data validation
- Existing analytics system for audit logging

## Testing

Run the test suite to verify the implementation:

```bash
python app/chat_history/test_encryption.py
```

The test suite verifies:
- Encryption/decryption functionality
- Hash generation and verification
- Data integrity through full encryption/decryption cycles
- Conversion function correctness

## Migration Strategy

### Phase 1: Deployment
- Deploy with encryption enabled
- New conversations are encrypted
- Existing conversations remain accessible via plain conversation field

### Phase 2: Migration (Optional)
- Background job to encrypt existing plain conversations
- Verify hash generation for existing data
- Update audit logs for migrated data

### Phase 3: Cleanup (Future)
- Remove plain conversation field after full migration
- Update schema to make encrypted fields required

## Monitoring and Alerts

### Audit Log Monitoring
- Monitor audit logs in Firestore for failed operations
- Set up alerts for hash verification failures
- Track encryption/decryption success rates

### Performance Monitoring
- Monitor encryption/decryption latency
- Track storage size increases due to encryption
- Monitor memory usage during bulk operations

## Security Considerations

### Key Management
- Master key should be stored securely (AWS KMS, Azure Key Vault, etc.)
- User keys are encrypted with master key
- Regular key rotation should be implemented

### Access Control
- Audit logs should have restricted access
- Encryption manager should be initialized once per application instance
- User key access should be logged and monitored

### Data Protection
- Encrypted data provides protection at rest
- Hash verification ensures data integrity
- Audit logs provide complete operation traceability

## Troubleshooting

### Common Issues

1. **Decryption Failures**
   - Check user key availability
   - Verify master key configuration
   - Review audit logs for specific error messages

2. **Hash Verification Failures**
   - Indicates potential data corruption
   - Check MongoDB data integrity
   - Review recent system changes

3. **Performance Issues**
   - Monitor encryption/decryption times
   - Consider batch operations for large datasets
   - Optimize conversation serialization

### Debug Mode
Enable detailed logging by setting log level to DEBUG:
```python
import logging
logging.getLogger('app').setLevel(logging.DEBUG)
```
