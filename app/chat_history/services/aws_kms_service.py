import boto3
import traceback
from typing import Optional
from botocore.exceptions import Client<PERSON><PERSON><PERSON>, BotoCoreError

from chat_history.decorators import audit_encrypt, audit_decrypt
from middlewares.logging_utils import app_logger as logger
from configs.app_config import S3_Region


class AWSKMSService:
    """
    AWS KMS service for encrypting and decrypting user keys
    """
    
    def __init__(self, kms_key_id: str, region: str = S3_Region):
        """
        Initialize AWS KMS service
        
        Args:
            kms_key_id: AWS KMS key ID or ARN
            region: AWS region
        """
        self.kms_key_id = kms_key_id
        self.region = region
        self._kms_client = None
    
    @property
    def kms_client(self):
        """Lazy initialization of KMS client"""
        if self._kms_client is None:
            try:
                self._kms_client = boto3.client('kms', region_name=self.region)
                logger.info(f"AWS KMS client initialized for region {self.region}")
            except Exception as e:
                logger.error(f"Failed to initialize AWS KMS client: {e}")
                raise RuntimeError(f"Failed to initialize AWS KMS client: {e}")
        return self._kms_client
    
    @audit_encrypt("AWSKMSService", user_id_param="user_id")
    def encrypt_user_key(self, user_key: bytes, user_id: str) -> bytes:
        """
        Encrypt user key using AWS KMS
        
        Args:
            user_key: User's encryption key to encrypt
            user_id: User ID for audit logging
            
        Returns:
            bytes: KMS-encrypted user key
            
        Raises:
            RuntimeError: If KMS encryption fails
        """
        try:
            # Encrypt the user key using KMS
            response = self.kms_client.encrypt(
                KeyId=self.kms_key_id,
                Plaintext=user_key,
                EncryptionContext={
                    'user_id': user_id,
                    'purpose': 'chat_encryption',
                    'service': 'chat_history'
                }
            )
            
            encrypted_key = response['CiphertextBlob']
            logger.info(f"Successfully encrypted user key for user {user_id} using KMS")
            return encrypted_key
            
        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_message = f"KMS ClientError {error_code}: {e.response['Error']['Message']}"
            logger.error(f"KMS encryption failed for user {user_id}: {error_message}")
            raise RuntimeError(f"KMS encryption failed: {error_message}")
            
        except BotoCoreError as e:
            error_message = f"KMS BotoCoreError: {str(e)}"
            logger.error(f"KMS encryption failed for user {user_id}: {error_message}")
            raise RuntimeError(f"KMS encryption failed: {error_message}")
            
        except Exception as e:
            error_message = f"Unexpected error during KMS encryption: {str(e)}"
            logger.error(f"KMS encryption failed for user {user_id}: {error_message}")
            logger.error(traceback.format_exc())
            raise RuntimeError(f"KMS encryption failed: {error_message}")
    
    @audit_decrypt("AWSKMSService", user_id_param="user_id")
    def decrypt_user_key(self, encrypted_user_key: bytes, user_id: str) -> bytes:
        """
        Decrypt user key using AWS KMS
        
        Args:
            encrypted_user_key: KMS-encrypted user key
            user_id: User ID for audit logging
            
        Returns:
            bytes: Decrypted user key
            
        Raises:
            RuntimeError: If KMS decryption fails
        """
        try:
            # Decrypt the user key using KMS
            response = self.kms_client.decrypt(
                CiphertextBlob=encrypted_user_key,
                EncryptionContext={
                    'user_id': user_id,
                    'purpose': 'chat_encryption',
                    'service': 'chat_history'
                }
            )
            
            decrypted_key = response['Plaintext']
            logger.info(f"Successfully decrypted user key for user {user_id} using KMS")
            return decrypted_key
            
        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_message = f"KMS ClientError {error_code}: {e.response['Error']['Message']}"
            logger.error(f"KMS decryption failed for user {user_id}: {error_message}")
            raise RuntimeError(f"KMS decryption failed: {error_message}")
            
        except BotoCoreError as e:
            error_message = f"KMS BotoCoreError: {str(e)}"
            logger.error(f"KMS decryption failed for user {user_id}: {error_message}")
            raise RuntimeError(f"KMS decryption failed: {error_message}")
            
        except Exception as e:
            error_message = f"Unexpected error during KMS decryption: {str(e)}"
            logger.error(f"KMS decryption failed for user {user_id}: {error_message}")
            logger.error(traceback.format_exc())
            raise RuntimeError(f"KMS decryption failed: {error_message}")
    
    def test_kms_connection(self) -> bool:
        """
        Test KMS connection and permissions
        
        Returns:
            bool: True if KMS is accessible, False otherwise
        """
        try:
            # Try to describe the KMS key to test connection
            response = self.kms_client.describe_key(KeyId=self.kms_key_id)
            logger.info(f"KMS connection test successful. Key state: {response['KeyMetadata']['KeyState']}")
            return True
        except Exception as e:
            logger.error(f"KMS connection test failed: {e}")
            return False
