import json

from pymongo.errors import PyMongoError

from chat_history.utils.history_utils import adjust_chat_history, OrderByEnum, get_mongo_field
from utils.datetime_utils import timestamp_now_il

from api.dependencies.mongo_db import get_mongo_client, get_collection_with_tz
from configs.app_config import MONGO_DB_HISTORY, MONGO_CHAT_HISTORY_COLLECTION
from middlewares.logging_utils import app_logger as logger
from pydantic.json import pydantic_encoder

from typing import Optional, Dict, Any
from motor.motor_asyncio import AsyncIOMotorClient


async def get_user_chats(
        mongo_client: AsyncIOMotorClient,
        user_id: str,
        offset: int = 0,
        limit: int = 20,
        order_by: OrderByEnum = OrderByEnum.UPDATED
) -> Dict[str, Any]:
    """Get user chats with pagination."""
    try:
        history_chat_collection = await get_collection_with_tz(mongo_client, db_name=MON<PERSON>O_DB_HISTORY,
                                                               collection_name=MONGO_CHAT_HISTORY_COLLECTION)
        query = {"user_id": user_id, "chat_settings.status": "active"}
        mongo_field = get_mongo_field(order_by)

        total_count = await history_chat_collection.count_documents(query)

        cursor = history_chat_collection.find(
            query,
            {"chat_id": 1,
             "filters": 1, "chat_settings": 1
             }
        ).sort(mongo_field, -1).skip(offset).limit(limit)

        documents = await cursor.to_list(length=limit)

        chats = []
        for doc in documents:
            chat_settings = doc.get('chat_settings', {})
            title = chat_settings.get('title', 'צאט חדש')
            filters = doc.get('filters', {})
            chat_settings = doc.get('chat_settings', {})

            if chat_settings["status"] == "active":
                chats.append({
                    "chat_id": doc.get('chat_id'),
                    "filters": filters,
                    "chat_settings": json.loads(
                        json.dumps(chat_settings, default=pydantic_encoder))
                })

        return {
            "chats": chats,
            "total_count": total_count,
            "offset": offset,
            "limit": limit,
            "count": len(chats),
            "has_more": (offset + len(documents)) < total_count,
            "order_by": order_by.value
        }

    except PyMongoError as e:
        logger.error(f"MongoDB error in get_user_chats: {e}")
        raise ConnectionError(f"Database error: {e}")
    except Exception as e:
        logger.error(f"Error in get_user_chats: {e}")
        raise


async def get_chat_by_id(
        mongo_client: AsyncIOMotorClient,
        chat_id: str
) -> Optional[Dict[str, Any]]:
    """Get chat by chat_id."""
    try:
        history_chat_collection = await get_collection_with_tz(mongo_client, db_name=MONGO_DB_HISTORY,
                                                               collection_name=MONGO_CHAT_HISTORY_COLLECTION)

        chat_document = await history_chat_collection.find_one({"chat_id": chat_id})

        if chat_document:
            # adjust_chat_history
            chat_document = adjust_chat_history(dict(chat_document))

        else:
            logger.warning(f"Chat not found: chat_id={chat_id}")
            raise ValueError(f"Chat not found: {chat_id}")
        if chat_document.get('chat_settings', {}).get('status') != 'active':
            logger.warning(f"Chat is not active: chat_id={chat_id}")
            raise ValueError(f"Chat is not active: {chat_id}")
        return chat_document

    except PyMongoError as e:
        logger.error(f"MongoDB error in get_chat_by_id: {e}")
        raise ConnectionError(f"Database error: {e}")
    except Exception as e:
        logger.error(f"Error in get_chat_by_id: {e}")
        raise


async def update_chat_title(
        mongo_client: AsyncIOMotorClient,
        chat_id: str,
        new_title: str,
        user_id: Optional[str] = None
) -> bool:
    """Update chat title."""
    try:
        history_chat_collection = await get_collection_with_tz(mongo_client, db_name=MONGO_DB_HISTORY,
                                                               collection_name=MONGO_CHAT_HISTORY_COLLECTION)

        query = {"chat_id": chat_id}
        if user_id:
            query["user_id"] = user_id

        update_time = timestamp_now_il()

        result = await history_chat_collection.update_one(
            query,
            {
                "$set": {
                    "chat_settings.title": new_title,
                    "chat_settings.update_time": update_time
                }
            }
        )

        if result.modified_count > 0:
            logger.info(f"Chat title updated: chat_id={chat_id}")
            return True
        else:
            logger.warning(f"No chat found to update: chat_id={chat_id}")
            return False

    except PyMongoError as e:
        logger.error(f"MongoDB error in update_chat_title: {e}")
        raise ConnectionError(f"Database error: {e}")
    except Exception as e:
        logger.error(f"Error in update_chat_title: {e}")
        raise


async def delete_chat_by_id(mongo_client: AsyncIOMotorClient, chat_id: str, user_id: str) -> bool:
    """Delete chat by chat_id."""
    try:
        history_chat_collection = await get_collection_with_tz(mongo_client, db_name=MONGO_DB_HISTORY,
                                                               collection_name=MONGO_CHAT_HISTORY_COLLECTION)
        result = await history_chat_collection.delete_one({
            "chat_id": chat_id,
            "user_id": user_id,
            "chat_settings.status": "active"
        })

        if result.deleted_count > 0:
            logger.info(f"Chat deleted: chat_id={chat_id}")
            return True
        else:
            logger.warning(f"No chat found to delete: chat_id={chat_id}")
            return False

    except PyMongoError as e:
        logger.error(f"MongoDB error in delete_chat_by_id: {e}")
        raise ConnectionError(f"Database error: {e}")
    except Exception as e:
        logger.error(f"Error in delete_chat_by_id: {e}")
        raise


async def main():
    mongo_client = await get_mongo_client()
    user_id = "1d349ee1-b6d2-87eb-1bee-3a17b9452968"

    # Get user chats
    chats = await get_user_chats(
        mongo_client=mongo_client,
        user_id=user_id,
        offset=0,
        limit=10,
        order_by=OrderByEnum.UPDATED
    )
    print(chats)
    # update_title = await update_chat_title(
    #     mongo_client=mongo_client,
    #     chat_id="7c34341a-79c3-4098-bc2e-802ba61f8733",
    #     new_title="Updated Chat Title",
    #     user_id=user_id
    # )
    # print(update_title)
    #
    # Get chat by ID
    # chat = await get_chat_by_id(mongo_client, "45707430-b033-4d2a-8c37-ccfcc066f473")
    # print(chat)

    # Update chat title
    # updated = update_chat_title(mongo_client, "example_chat_id", "New Title", user_id)
    # print(f"Chat title updated: {updated}")


import asyncio

if __name__ == '__main__':
    # Example usage
    asyncio.run(main())
