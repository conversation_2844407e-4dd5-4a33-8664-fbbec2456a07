import traceback
from enum import Enum

from pydantic import BaseModel
from pymongo.errors import PyMongoError
from pymongo.results import InsertOneResult

from chat_history.schema.mongo_models import MongoChatSchema
from chat_history.services.audit_service import AuditService
from chat_history.schema.audit_models import AuditActionEnum, AuditStatusEnum
from configs.app_config import MONGO_DB_HISTORY, MONGO_CHAT_HISTORY_COLLECTION
from redis_db.redis_schema import RedisSchema
from utils.datetime_utils import timestamp_now_il
from middlewares.logging_utils import app_logger as logger

from typing import Any


def convert_redis_to_mongo(redis_obj: RedisSchema, encryption_manager: Any = None, user_key_id: str = None) -> MongoChatSchema:
    """
    Convert RedisSchema to MongoChatSchema with encrypted conversation.

    :param redis_obj: RedisSchema object.
    :param encryption_manager: EncryptionManager instance for encrypting conversation
    :param user_key_id: User key ID for audit logging
    :return: MongoChatSchema object.
    """
    ##by jerusalem_time_string
    redis_obj.chat_settings.update_time = timestamp_now_il()

    # Initialize fields
    encrypted_conversation = None
    conversation_hash = None
    conversation = redis_obj.conversation  # Keep for backward compatibility

    # Encrypt conversation if encryption manager is provided
    if encryption_manager and redis_obj.conversation:
        try:
            # Generate conversation hash
            conversation_hash = encryption_manager.generate_conversation_hash(redis_obj.conversation)

            # Encrypt conversation
            encrypted_conversation = encryption_manager.encrypt_conversation(redis_obj.conversation)

            # Log successful operations
            AuditService.log_hash_generation(
                user_id=redis_obj.user_id,
                chat_id=redis_obj.chat_id,
                conversation_hash=conversation_hash
            )

            AuditService.log_encryption_success(
                user_id=redis_obj.user_id,
                chat_id=redis_obj.chat_id,
                user_key_id=user_key_id or "unknown",
                details=f"Encrypted {len(redis_obj.conversation)} conversation items"
            )

            # Clear plain conversation for security (optional - keeping for backward compatibility)
            # conversation = []

        except Exception as e:
            logger.error(f"Failed to encrypt conversation for chat {redis_obj.chat_id}: {e}")
            logger.error(traceback.format_exc())

            AuditService.log_encryption_failure(
                user_id=redis_obj.user_id,
                chat_id=redis_obj.chat_id,
                error_message=str(e),
                user_key_id=user_key_id
            )

            # Continue without encryption in case of error

    return MongoChatSchema(
        user_id=redis_obj.user_id,
        chat_id=redis_obj.chat_id,
        domain=redis_obj.domain,
        encrypted_conversation=encrypted_conversation,
        conversation_hash=conversation_hash,
        conversation=conversation,
        summaries=redis_obj.summaries,
        model_settings=redis_obj.model_settings,
        chat_settings=redis_obj.chat_settings,
        filters=redis_obj.filters or {},
        citations=redis_obj.citations or [],
    )


def convert_mongo_to_redis(mongo_obj: MongoChatSchema, encryption_manager: Any = None, user_key_id: str = None) -> RedisSchema:
    """
    Convert MongoChatSchema to RedisSchema with decrypted conversation.

    :param mongo_obj: MongoChatSchema object.
    :param encryption_manager: EncryptionManager instance for decrypting conversation
    :param user_key_id: User key ID for audit logging
    :return: RedisSchema object.
    """
    conversation = mongo_obj.conversation or []  # Default to plain conversation

    # Decrypt conversation if encrypted data exists
    if encryption_manager and mongo_obj.encrypted_conversation:
        try:
            # Decrypt conversation
            decrypted_conversation = encryption_manager.decrypt_conversation(mongo_obj.encrypted_conversation)

            # Verify hash if available
            if mongo_obj.conversation_hash:
                hash_valid = encryption_manager.verify_conversation_hash(
                    decrypted_conversation,
                    mongo_obj.conversation_hash
                )

                AuditService.log_hash_verification(
                    user_id=mongo_obj.user_id,
                    chat_id=mongo_obj.chat_id,
                    verification_success=hash_valid,
                    stored_hash=mongo_obj.conversation_hash,
                    calculated_hash=encryption_manager.generate_conversation_hash(decrypted_conversation)
                )

                if not hash_valid:
                    logger.error(f"Hash verification failed for chat {mongo_obj.chat_id}")
                    # Continue with decrypted data but log the integrity issue

            # Use decrypted conversation
            conversation = decrypted_conversation

            AuditService.log_decryption_success(
                user_id=mongo_obj.user_id,
                chat_id=mongo_obj.chat_id,
                user_key_id=user_key_id or "unknown",
                details=f"Decrypted {len(conversation)} conversation items"
            )

        except Exception as e:
            logger.error(f"Failed to decrypt conversation for chat {mongo_obj.chat_id}: {e}")
            logger.error(traceback.format_exc())

            AuditService.log_decryption_failure(
                user_id=mongo_obj.user_id,
                chat_id=mongo_obj.chat_id,
                error_message=str(e),
                user_key_id=user_key_id
            )

            # Fall back to plain conversation if available
            if mongo_obj.conversation:
                conversation = mongo_obj.conversation
                logger.warning(f"Using fallback plain conversation for chat {mongo_obj.chat_id}")
            else:
                logger.error(f"No fallback conversation available for chat {mongo_obj.chat_id}")
                conversation = []

    return RedisSchema(
        user_id=mongo_obj.user_id,
        chat_id=mongo_obj.chat_id,
        domain=mongo_obj.domain,
        conversation=conversation,
        summaries=mongo_obj.summaries,
        model_settings=mongo_obj.model_settings,
        chat_settings=mongo_obj.chat_settings,
        filters=mongo_obj.filters or {}
    )





def fix_enums(obj):
    """
    Recursively converts Enum members to their values,
    and datetime/date objects to ISO 8601 strings.
    Converts Pydantic BaseModel instances to dictionaries.
    """
    if isinstance(obj, BaseModel):
        # Pydantic's .model_dump() already handles aliases if configured on the model.
        # Calling model_dump() here ensures we get a plain Python dict.
        return fix_enums(obj.model_dump())
    elif isinstance(obj, dict):
        return {k: fix_enums(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [fix_enums(i) for i in obj]
    elif isinstance(obj, Enum):
        return obj.value

    else:
        return obj


class ChatHistoryManager:

    def __init__(self, encryption_manager: Any, mongo_client: Any, user_key_manager: Any = None):
        self.encryption_manager = encryption_manager
        self.user_key_manager = user_key_manager
        self.client = mongo_client
        self.collection = self.client[MONGO_DB_HISTORY][MONGO_CHAT_HISTORY_COLLECTION]

    async def store_chat(self, redis_chat_data: RedisSchema) -> InsertOneResult:
        try:
            # Get user key ID for audit logging
            user_key_id = None
            if self.user_key_manager:
                try:
                    user_key_id = self.user_key_manager.get_user_key_id(redis_chat_data.user_id)
                except Exception as e:
                    logger.warning(f"Could not get user key ID for audit: {e}")

            # Convert with encryption
            mongo_chat = convert_redis_to_mongo(
                redis_chat_data,
                encryption_manager=self.encryption_manager,
                user_key_id=user_key_id
            )
            mongo_chat = fix_enums(mongo_chat)

            object_mongo = await self.collection.update_one(
                {"chat_id": mongo_chat["chat_id"]},
                {"$set": mongo_chat},
                upsert=True
            )

            # Log store operation
            AuditService.log_audit_event(
                schema="MongoChatSchema",
                user_id=redis_chat_data.user_id,
                action=AuditActionEnum.STORE,
                status=AuditStatusEnum.SUCCESS,
                user_key_id=user_key_id,
                chat_id=redis_chat_data.chat_id,
                details=f"Stored chat with {len(redis_chat_data.conversation)} conversation items"
            )

            return object_mongo
        except PyMongoError as e:
            AuditService.log_audit_event(
                schema="MongoChatSchema",
                user_id=redis_chat_data.user_id,
                action=AuditActionEnum.STORE,
                status=AuditStatusEnum.FAILURE,
                chat_id=redis_chat_data.chat_id,
                error_message=f"Database error: {e}"
            )
            raise ConnectionError(f"Database error: {e}")
        except Exception as e:
            AuditService.log_audit_event(
                schema="MongoChatSchema",
                user_id=redis_chat_data.user_id,
                action=AuditActionEnum.STORE,
                status=AuditStatusEnum.ERROR,
                chat_id=redis_chat_data.chat_id,
                error_message=str(e)
            )
            raise RuntimeError(f"An error occurred: {e}")

    async def extract_chat_history(self, chat_id: str) -> RedisSchema | None:
        try:
            client_data = await self.collection.find_one({"chat_id": chat_id})

            if not client_data:
                return None

            mongo_chat = MongoChatSchema.model_validate(client_data)

            # Get user key ID for audit logging
            user_key_id = None
            if self.user_key_manager:
                try:
                    user_key_id = self.user_key_manager.get_user_key_id(mongo_chat.user_id)
                except Exception as e:
                    logger.warning(f"Could not get user key ID for audit: {e}")

            # Convert with decryption
            redis_chat = convert_mongo_to_redis(
                mongo_chat,
                encryption_manager=self.encryption_manager,
                user_key_id=user_key_id
            )

            # Log retrieve operation
            AuditService.log_audit_event(
                schema="MongoChatSchema",
                user_id=mongo_chat.user_id,
                action=AuditActionEnum.RETRIEVE,
                status=AuditStatusEnum.SUCCESS,
                user_key_id=user_key_id,
                chat_id=chat_id,
                details=f"Retrieved chat with {len(redis_chat.conversation)} conversation items"
            )

            return redis_chat
        except PyMongoError as e:
            # Try to get user_id from chat_id for audit logging
            user_id = "unknown"
            try:
                if client_data:
                    user_id = client_data.get("user_id", "unknown")
            except:
                pass

            AuditService.log_audit_event(
                schema="MongoChatSchema",
                user_id=user_id,
                action=AuditActionEnum.RETRIEVE,
                status=AuditStatusEnum.FAILURE,
                chat_id=chat_id,
                error_message=f"Database error: {e}"
            )
            raise ConnectionError(f"Database error: {e}")
        except Exception as e:
            # Try to get user_id for audit logging
            user_id = "unknown"
            try:
                if client_data:
                    user_id = client_data.get("user_id", "unknown")
            except:
                pass

            AuditService.log_audit_event(
                schema="MongoChatSchema",
                user_id=user_id,
                action=AuditActionEnum.RETRIEVE,
                status=AuditStatusEnum.ERROR,
                chat_id=chat_id,
                error_message=str(e)
            )
            raise RuntimeError(f"An error occurred: {e}")
