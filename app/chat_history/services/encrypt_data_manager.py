from enum import Enum

from pydantic import BaseModel
from pymongo.errors import PyMongoError
from pymongo.results import InsertOneResult

from chat_history.schema.mongo_models import MongoChatSchema
from configs.app_config import MONGO_DB_HISTORY, MONGO_CHAT_HISTORY_COLLECTION
from redis_db.redis_schema import RedisSchema
from utils.datetime_utils import timestamp_now_il

from typing import Any


def convert_redis_to_mongo(redis_obj: RedisSchema) -> MongoChatSchema:
    """
    Convert RedisSchema to MongoChatSchema with encrypted conversation.

    :param redis_obj: RedisSchema object.
    :param encrypt_fn: A function that takes a list of ConversationItem and returns an encrypted list of dicts.
    :return: MongoChatSchema object.
    """
    ##by jerusalem_time_string
    redis_obj.chat_settings.update_time = timestamp_now_il()
    return MongoChatSchema(
        user_id=redis_obj.user_id,
        chat_id=redis_obj.chat_id,
        domain=redis_obj.domain,
        conversation=redis_obj.conversation,
        summaries=redis_obj.summaries,
        model_settings=redis_obj.model_settings,
        chat_settings=redis_obj.chat_settings,
        filters=redis_obj.filters or {},
        citations=redis_obj.citations or [],
    )


def convert_mongo_to_redis(mongo_obj: MongoChatSchema) -> RedisSchema:
    """
    Convert MongoChatSchema to RedisSchema.

    :param mongo_obj: MongoChatSchema object.
    :return: RedisSchema object.
    """
    return RedisSchema(
        user_id=mongo_obj.user_id,
        chat_id=mongo_obj.chat_id,
        domain=mongo_obj.domain,
        conversation=mongo_obj.conversation,
        summaries=mongo_obj.summaries,
        model_settings=mongo_obj.model_settings,
        chat_settings=mongo_obj.chat_settings,
        filters=mongo_obj.filters or {}
    )





def fix_enums(obj):
    """
    Recursively converts Enum members to their values,
    and datetime/date objects to ISO 8601 strings.
    Converts Pydantic BaseModel instances to dictionaries.
    """
    if isinstance(obj, BaseModel):
        # Pydantic's .model_dump() already handles aliases if configured on the model.
        # Calling model_dump() here ensures we get a plain Python dict.
        return fix_enums(obj.model_dump())
    elif isinstance(obj, dict):
        return {k: fix_enums(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [fix_enums(i) for i in obj]
    elif isinstance(obj, Enum):
        return obj.value

    else:
        return obj


class ChatHistoryManager:

    def __init__(self, encryption_manager: Any, mongo_client: Any):
        self.encryption_manager = encryption_manager
        self.client = mongo_client
        self.collection = self.client[MONGO_DB_HISTORY][MONGO_CHAT_HISTORY_COLLECTION]

    async def store_chat(self, redis_chat_data: RedisSchema) -> InsertOneResult:
        try:

            mongo_chat = convert_redis_to_mongo(redis_chat_data)
            mongo_chat = fix_enums(mongo_chat)

            object_mongo = await self.collection.update_one(
                {"chat_id": mongo_chat["chat_id"]},
                {"$set": mongo_chat},
                upsert=True

            )

            return object_mongo
        except PyMongoError as e:
            raise ConnectionError(f"Database error: {e}")
        except Exception as e:
            raise RuntimeError(f"An error occurred: {e}")

    async def extract_chat_history(self, chat_id: str) -> RedisSchema | None:
        try:
            client_data = await self.collection.find_one({"chat_id": chat_id})
            # client_data = await get_document(self.collection, {"chat_id": chat_id}, user_tz_str="Asia/Jerusalem")

            if not client_data:
                return None
            mongo_chat = MongoChatSchema.model_validate(client_data)
            # Convert MongoChatSchema to RedisSchema
            redis_chat = convert_mongo_to_redis(mongo_chat)
            # encrypted_conversation = mongo_chat.conversation
            # decrypted_conversation = self.encryption_manager.decrypt(encrypted_conversation).decode('utf-8')

            # check if the conversation hash matches
            # conversation_hash = hashlib.sha256(decrypted_conversation.encode('utf-8')).hexdigest()
            # if conversation_hash != client_data["conversation_hash"]:
            #     raise ValueError("Conversation integrity check failed.")

            return redis_chat
        except PyMongoError as e:
            raise ConnectionError(f"Database error: {e}")
        except Exception as e:
            raise RuntimeError(f"An error occurred: {e}")
