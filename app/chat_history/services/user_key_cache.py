import json
import base64
from typing import Optional
from datetime import datetime, timedelta

from utils.cache_db import get_redis
from utils.datetime_utils import timestamp_now_il
from middlewares.logging_utils import app_logger as logger
from chat_history.services.audit_service import AuditService
from chat_history.schema.audit_models import AuditActionEnum, AuditStatusEnum


class UserKeyCacheService:
    """
    Redis-based caching service for decrypted user keys
    Provides temporary caching with TTL for performance optimization
    """
    
    # Cache TTL in seconds (30 minutes)
    CACHE_TTL = 30 * 60
    
    # Cache key prefix
    CACHE_PREFIX = "user_key_cache"
    
    def __init__(self):
        self._redis = None
    
    async def get_redis(self):
        """Get Redis connection"""
        if self._redis is None:
            self._redis = await get_redis()
        return self._redis
    
    def _get_cache_key(self, user_id: str) -> str:
        """Generate cache key for user"""
        return f"{self.CACHE_PREFIX}:{user_id}"
    
    async def get_cached_user_key(self, user_id: str) -> Optional[bytes]:
        """
        Retrieve cached decrypted user key from Redis
        
        Args:
            user_id: User ID
            
        Returns:
            Optional[bytes]: Decrypted user key if cached and valid, None otherwise
        """
        try:
            redis = await self.get_redis()
            cache_key = self._get_cache_key(user_id)
            
            # Get cached data
            cached_data = await redis.get(cache_key)
            if not cached_data:
                logger.debug(f"No cached user key found for user {user_id}")
                return None
            
            # Parse cached data
            cache_entry = json.loads(cached_data.decode('utf-8'))
            
            # Check if cache entry is still valid
            cached_time = datetime.fromisoformat(cache_entry['cached_at'])
            expiry_time = cached_time + timedelta(seconds=self.CACHE_TTL)
            
            if datetime.now() > expiry_time:
                logger.debug(f"Cached user key expired for user {user_id}")
                await self.invalidate_user_key(user_id)
                return None
            
            # Decode and return the user key
            user_key = base64.b64decode(cache_entry['user_key'])
            
            # Log cache hit
            AuditService.log_audit_event(
                schema="UserKeyCacheService",
                user_id=user_id,
                action=AuditActionEnum.RETRIEVE,
                status=AuditStatusEnum.SUCCESS,
                details=f"Cache hit - retrieved user key from cache"
            )
            
            logger.debug(f"Retrieved cached user key for user {user_id}")
            return user_key
            
        except Exception as e:
            logger.error(f"Error retrieving cached user key for user {user_id}: {e}")
            
            AuditService.log_audit_event(
                schema="UserKeyCacheService",
                user_id=user_id,
                action=AuditActionEnum.RETRIEVE,
                status=AuditStatusEnum.ERROR,
                error_message=str(e)
            )
            
            return None
    
    async def cache_user_key(self, user_id: str, user_key: bytes) -> bool:
        """
        Cache decrypted user key in Redis with TTL
        
        Args:
            user_id: User ID
            user_key: Decrypted user key to cache
            
        Returns:
            bool: True if successfully cached, False otherwise
        """
        try:
            redis = await self.get_redis()
            cache_key = self._get_cache_key(user_id)
            
            # Prepare cache entry
            cache_entry = {
                'user_key': base64.b64encode(user_key).decode('utf-8'),
                'cached_at': datetime.now().isoformat(),
                'user_id': user_id
            }
            
            # Store in Redis with TTL
            cache_data = json.dumps(cache_entry)
            success = await redis.set(cache_key, cache_data, ex=self.CACHE_TTL)
            
            if success:
                # Log successful caching
                AuditService.log_audit_event(
                    schema="UserKeyCacheService",
                    user_id=user_id,
                    action=AuditActionEnum.STORE,
                    status=AuditStatusEnum.SUCCESS,
                    details=f"Cached user key with TTL {self.CACHE_TTL} seconds"
                )
                
                logger.debug(f"Successfully cached user key for user {user_id}")
                return True
            else:
                logger.error(f"Failed to cache user key for user {user_id}")
                
                AuditService.log_audit_event(
                    schema="UserKeyCacheService",
                    user_id=user_id,
                    action=AuditActionEnum.STORE,
                    status=AuditStatusEnum.FAILURE,
                    error_message="Redis set operation failed"
                )
                
                return False
                
        except Exception as e:
            logger.error(f"Error caching user key for user {user_id}: {e}")
            
            AuditService.log_audit_event(
                schema="UserKeyCacheService",
                user_id=user_id,
                action=AuditActionEnum.STORE,
                status=AuditStatusEnum.ERROR,
                error_message=str(e)
            )
            
            return False
    
    async def invalidate_user_key(self, user_id: str) -> bool:
        """
        Invalidate cached user key
        
        Args:
            user_id: User ID
            
        Returns:
            bool: True if successfully invalidated, False otherwise
        """
        try:
            redis = await self.get_redis()
            cache_key = self._get_cache_key(user_id)
            
            # Delete from Redis
            deleted_count = await redis.delete(cache_key)
            
            if deleted_count > 0:
                # Log successful invalidation
                AuditService.log_audit_event(
                    schema="UserKeyCacheService",
                    user_id=user_id,
                    action="invalidate",
                    status=AuditStatusEnum.SUCCESS,
                    details="User key cache invalidated"
                )
                
                logger.debug(f"Successfully invalidated cached user key for user {user_id}")
                return True
            else:
                logger.debug(f"No cached user key to invalidate for user {user_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error invalidating cached user key for user {user_id}: {e}")
            
            AuditService.log_audit_event(
                schema="UserKeyCacheService",
                user_id=user_id,
                action="invalidate",
                status=AuditStatusEnum.ERROR,
                error_message=str(e)
            )
            
            return False
    
    async def get_cache_stats(self) -> dict:
        """
        Get cache statistics
        
        Returns:
            dict: Cache statistics
        """
        try:
            redis = await self.get_redis()
            pattern = f"{self.CACHE_PREFIX}:*"
            
            # Get all cache keys
            keys = await redis.keys(pattern)
            
            stats = {
                'total_cached_keys': len(keys),
                'cache_prefix': self.CACHE_PREFIX,
                'cache_ttl_seconds': self.CACHE_TTL,
                'timestamp': timestamp_now_il()
            }
            
            logger.debug(f"Cache stats: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return {
                'error': str(e),
                'timestamp': timestamp_now_il()
            }
    
    async def clear_all_cache(self) -> int:
        """
        Clear all cached user keys (for maintenance/testing)
        
        Returns:
            int: Number of keys deleted
        """
        try:
            redis = await self.get_redis()
            pattern = f"{self.CACHE_PREFIX}:*"
            
            # Get all cache keys
            keys = await redis.keys(pattern)
            
            if keys:
                # Delete all cache keys
                deleted_count = await redis.delete(*keys)
                
                logger.info(f"Cleared {deleted_count} cached user keys")
                
                AuditService.log_audit_event(
                    schema="UserKeyCacheService",
                    user_id="system",
                    action="clear_cache",
                    status=AuditStatusEnum.SUCCESS,
                    details=f"Cleared {deleted_count} cached user keys"
                )
                
                return deleted_count
            else:
                logger.info("No cached user keys to clear")
                return 0
                
        except Exception as e:
            logger.error(f"Error clearing cache: {e}")
            
            AuditService.log_audit_event(
                schema="UserKeyCacheService",
                user_id="system",
                action="clear_cache",
                status=AuditStatusEnum.ERROR,
                error_message=str(e)
            )
            
            return 0
