import traceback
from typing import Optional

from chat_history.schema.audit_models import AuditLogEntry, AuditActionEnum, AuditStatusEnum
from utils.analytics import create_document
from middlewares.logging_utils import app_logger as logger


class AuditService:
    """Service for handling audit logging of encryption/decryption operations"""
    
    AUDIT_COLLECTION = "chat_encryption_audit"
    
    @staticmethod
    def log_audit_event(
        schema: str,
        user_id: str,
        action: AuditActionEnum,
        status: AuditStatusEnum,
        user_key_id: Optional[str] = None,
        chat_id: Optional[str] = None,
        details: Optional[str] = None,
        error_message: Optional[str] = None
    ) -> None:
        """
        Log an audit event for encryption/decryption operations
        
        Args:
            schema: Schema name (e.g., 'MongoChatSchema')
            user_id: User ID
            action: Action performed
            status: Operation status
            user_key_id: User key ID for encryption operations
            chat_id: Chat ID
            details: Additional details
            error_message: Error message if operation failed
        """
        try:
            audit_entry = AuditLogEntry(
                schema=schema,
                user_id=user_id,
                user_key_id=user_key_id,
                chat_id=chat_id,
                status=status,
                action=action,
                details=details,
                error_message=error_message
            )
            
            # Log to analytics system (Firestore)
            create_document(
                collection_name=AuditService.AUDIT_COLLECTION,
                data=audit_entry.model_dump()
            )
            
            # Also log to application logger for immediate visibility
            log_level = "info" if status == AuditStatusEnum.SUCCESS else "error"
            log_message = f"Audit: {action.value} {status.value} for user {user_id}"
            if chat_id:
                log_message += f" chat {chat_id}"
            if error_message:
                log_message += f" - {error_message}"
                
            if log_level == "info":
                logger.info(log_message)
            else:
                logger.error(log_message)
                
        except Exception as e:
            logger.error(f"Failed to log audit event: {e}")
            logger.error(f"Audit event details: schema={schema}, user_id={user_id}, action={action}, status={status}")
            logger.error(traceback.format_exc())
    
    @staticmethod
    def log_encryption_success(
        user_id: str,
        chat_id: str,
        user_key_id: str,
        details: Optional[str] = None
    ) -> None:
        """Log successful encryption operation"""
        AuditService.log_audit_event(
            schema="MongoChatSchema",
            user_id=user_id,
            action=AuditActionEnum.ENCRYPT,
            status=AuditStatusEnum.SUCCESS,
            user_key_id=user_key_id,
            chat_id=chat_id,
            details=details
        )
    
    @staticmethod
    def log_encryption_failure(
        user_id: str,
        chat_id: str,
        error_message: str,
        user_key_id: Optional[str] = None
    ) -> None:
        """Log failed encryption operation"""
        AuditService.log_audit_event(
            schema="MongoChatSchema",
            user_id=user_id,
            action=AuditActionEnum.ENCRYPT,
            status=AuditStatusEnum.FAILURE,
            user_key_id=user_key_id,
            chat_id=chat_id,
            error_message=error_message
        )
    
    @staticmethod
    def log_decryption_success(
        user_id: str,
        chat_id: str,
        user_key_id: str,
        details: Optional[str] = None
    ) -> None:
        """Log successful decryption operation"""
        AuditService.log_audit_event(
            schema="MongoChatSchema",
            user_id=user_id,
            action=AuditActionEnum.DECRYPT,
            status=AuditStatusEnum.SUCCESS,
            user_key_id=user_key_id,
            chat_id=chat_id,
            details=details
        )
    
    @staticmethod
    def log_decryption_failure(
        user_id: str,
        chat_id: str,
        error_message: str,
        user_key_id: Optional[str] = None
    ) -> None:
        """Log failed decryption operation"""
        AuditService.log_audit_event(
            schema="MongoChatSchema",
            user_id=user_id,
            action=AuditActionEnum.DECRYPT,
            status=AuditStatusEnum.FAILURE,
            user_key_id=user_key_id,
            chat_id=chat_id,
            error_message=error_message
        )
    
    @staticmethod
    def log_hash_generation(
        user_id: str,
        chat_id: str,
        conversation_hash: str
    ) -> None:
        """Log conversation hash generation"""
        AuditService.log_audit_event(
            schema="MongoChatSchema",
            user_id=user_id,
            action=AuditActionEnum.HASH_GENERATE,
            status=AuditStatusEnum.SUCCESS,
            chat_id=chat_id,
            details=f"Generated hash: {conversation_hash[:16]}..."
        )
    
    @staticmethod
    def log_hash_verification(
        user_id: str,
        chat_id: str,
        verification_success: bool,
        stored_hash: str,
        calculated_hash: str
    ) -> None:
        """Log conversation hash verification"""
        status = AuditStatusEnum.SUCCESS if verification_success else AuditStatusEnum.FAILURE
        details = f"Stored: {stored_hash[:16]}..., Calculated: {calculated_hash[:16]}..."
        error_message = None if verification_success else "Hash verification failed - data integrity compromised"
        
        AuditService.log_audit_event(
            schema="MongoChatSchema",
            user_id=user_id,
            action=AuditActionEnum.HASH_VERIFY,
            status=status,
            chat_id=chat_id,
            details=details,
            error_message=error_message
        )
