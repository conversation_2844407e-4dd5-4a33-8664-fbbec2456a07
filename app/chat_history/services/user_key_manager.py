import base64

from pymongo import MongoClient
from pymongo.errors import PyMongoError

from chat_history.encrypt_manger import EncryptionManager
from bson.binary import Binary


from datetime import datetime
import pytz


class UserKeyManager:

    def __init__(self, encryption_manager: EncryptionManager, mongo_uri: str, db_name: str, collection_name: str):
        self.encryption_manager = encryption_manager
        self.client = MongoClient(mongo_uri)
        self.db = self.client[db_name]
        self.collection = self.db[collection_name]

    def create_user_key(self, user_id: str):
        try:
            existing_user = self.collection.find_one({"user_id": user_id})
            if existing_user:
                return existing_user["encrypted_user_key"]

            user_key = self.encryption_manager.generate_user_key()
            encrypted_user_key = self.encryption_manager.encrypt(user_key)
            current_datetime = datetime.now(pytz.timezone("Asia/Jerusalem")).strftime("%H:%M %d/%m/%Y")

            self.collection.insert_one({
                "user_id": user_id,
                "encrypted_user_key": Binary(encrypted_user_key),
                "create_date": current_datetime,
            })
            return Binary(encrypted_user_key)
        except PyMongoError as e:
            raise ConnectionError(f"Database error: {e}")
        except Exception as e:
            raise RuntimeError(f"An error occurred: {e}")

    def get_user_key(self, user_id: str):
        try:
            user_data = self.collection.find_one({"user_id": user_id})
            if not user_data:
                raise ValueError(f"No key found for user {user_id}.")

            return Binary(self.encryption_manager.decrypt(user_data["encrypted_user_key"]))
        except PyMongoError as e:
            raise ConnectionError(f"Database error: {e}")
        except Exception as e:
            raise RuntimeError(f"An error occurred: {e}")