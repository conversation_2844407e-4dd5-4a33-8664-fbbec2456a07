import json
import hashlib
from typing import List

from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes

from utils.conversation_manager import ConversationItem


class EncryptionManager:
    _instance = None
    _kms_master_key = None

    @classmethod
    def initialize(cls, kms_master_key: bytes):
        """אתחול המחלקה עם מפתח ה-KMS"""
        if cls._instance is None:
            cls._kms_master_key = kms_master_key
            cls._instance = cls()
        return cls._instance

    @classmethod
    def get_instance(cls):
        """מחזיר את האובייקט המאוחסן"""
        if cls._instance is None:
            raise RuntimeError("EncryptionManager has not been initialized. Call initialize() first.")
        return cls._instance

    def encrypt(self, data: bytes) -> bytes:
        nonce = Fernet.generate_key()[:12]

        cipher = Cipher(algorithms.AES(self._kms_master_key), modes.GCM(nonce))
        encryptor = cipher.encryptor()
        ciphertext = encryptor.update(data) + encryptor.finalize()
        print("len data",len(ciphertext))
        return nonce + encryptor.tag + ciphertext

    def decrypt(self, encrypted_data: bytes) -> bytes:
        nonce = encrypted_data[:12]
        tag = encrypted_data[12:28]
        ciphertext = encrypted_data[28:]

        cipher = Cipher(algorithms.AES(self._kms_master_key), modes.GCM(nonce, tag))
        decryptor = cipher.decryptor()
        return decryptor.update(ciphertext) + decryptor.finalize()

    @staticmethod
    def generate_user_key() -> bytes:
        return Fernet.generate_key()

    def encrypt_conversation(self, conversation: List[ConversationItem]) -> bytes:
        """
        Encrypt a list of ConversationItem objects

        Args:
            conversation: List of ConversationItem objects

        Returns:
            bytes: Encrypted conversation data
        """
        # Convert conversation to JSON string
        conversation_data = [item.model_dump() for item in conversation]
        conversation_json = json.dumps(conversation_data, ensure_ascii=False)
        conversation_bytes = conversation_json.encode('utf-8')

        # Encrypt the data
        return self.encrypt(conversation_bytes)

    def decrypt_conversation(self, encrypted_data: bytes) -> List[ConversationItem]:
        """
        Decrypt conversation data back to List[ConversationItem]

        Args:
            encrypted_data: Encrypted conversation bytes

        Returns:
            List[ConversationItem]: Decrypted conversation items
        """
        # Decrypt the data
        decrypted_bytes = self.decrypt(encrypted_data)
        conversation_json = decrypted_bytes.decode('utf-8')
        conversation_data = json.loads(conversation_json)

        # Convert back to ConversationItem objects
        return [ConversationItem(**item) for item in conversation_data]

    def generate_conversation_hash(self, conversation: List[ConversationItem]) -> str:
        """
        Generate SHA-256 hash of conversation for integrity verification

        Args:
            conversation: List of ConversationItem objects

        Returns:
            str: SHA-256 hash of the conversation
        """
        # Convert conversation to consistent JSON representation
        conversation_data = [item.model_dump() for item in conversation]
        # Sort by timestamp and index to ensure consistent ordering
        conversation_data.sort(key=lambda x: (x.get('timestamp', ''), x.get('index', 0)))
        conversation_json = json.dumps(conversation_data, ensure_ascii=False, sort_keys=True)
        conversation_bytes = conversation_json.encode('utf-8')

        # Generate SHA-256 hash
        return hashlib.sha256(conversation_bytes).hexdigest()

    def verify_conversation_hash(self, conversation: List[ConversationItem], expected_hash: str) -> bool:
        """
        Verify conversation integrity using hash

        Args:
            conversation: List of ConversationItem objects
            expected_hash: Expected hash value

        Returns:
            bool: True if hash matches, False otherwise
        """
        calculated_hash = self.generate_conversation_hash(conversation)
        return calculated_hash == expected_hash


"""

class EncryptionManager:

    def __init__(self, kms_master_key: bytes):
        self.kms_master_key = kms_master_key
        self.backend = default_backend()

    def _generate_hash(self, data: bytes) -> str:
        return hashlib.sha256(data).hexdigest()

    def encrypt(self, data: bytes) -> bytes:
        nonce = os.urandom(12)  # NONCE ל-GCM בגודל 12 בייט
        cipher = Cipher(algorithms.AES(self.kms_master_key), modes.GCM(nonce), backend=self.backend)
        encryptor = cipher.encryptor()
        ciphertext = encryptor.update(data) + encryptor.finalize()
        return nonce + encryptor.tag + ciphertext  # שילוב NONCE, תג אימות, והטקסט המוצפן

    def decrypt(self, encrypted_data: bytes, expected_hash: str) -> bytes:
        nonce = encrypted_data[:12]  # NONCE בגודל 12 בייט
        tag = encrypted_data[12:28]  # תג אימות בגודל 16 בייט
        ciphertext = encrypted_data[28:]  # שאר המידע המוצפן

        cipher = Cipher(algorithms.AES(self.kms_master_key), modes.GCM(nonce, tag), backend=self.backend)
        decryptor = cipher.decryptor()
        decrypted_data = decryptor.update(ciphertext) + decryptor.finalize()

        # Verify the hash after decryption
        if self._generate_hash(decrypted_data) != expected_hash:
            raise ValueError("Data integrity check failed. Hash mismatch.")

        return decrypted_data

    @staticmethod
    def generate_user_key() -> bytes:
        return Fernet.generate_key()
"""

# def init_encryption_manager():
#     KMS_MASTER_KEY = os.urandom(32)  # מחליף את AWS KMS לדוגמה
#     EncryptionManager.initialize(KMS_MASTER_KEY)
#
#
# # קריאה חד-פעמית בזמן עליית ה-POD
# init_encryption_manager()