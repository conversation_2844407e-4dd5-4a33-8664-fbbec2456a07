from cryptography.fernet import Fernet
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes


class EncryptionManager:
    _instance = None
    _kms_master_key = None

    @classmethod
    def initialize(cls, kms_master_key: bytes):
        """אתחול המחלקה עם מפתח ה-KMS"""
        if cls._instance is None:
            cls._kms_master_key = kms_master_key
            cls._instance = cls()
        return cls._instance

    @classmethod
    def get_instance(cls):
        """מחזיר את האובייקט המאוחסן"""
        if cls._instance is None:
            raise RuntimeError("EncryptionManager has not been initialized. Call initialize() first.")
        return cls._instance

    def encrypt(self, data: bytes) -> bytes:
        nonce = Fernet.generate_key()[:12]

        cipher = Cipher(algorithms.AES(self._kms_master_key), modes.GCM(nonce))
        encryptor = cipher.encryptor()
        ciphertext = encryptor.update(data) + encryptor.finalize()
        print("len data",len(ciphertext))
        return nonce + encryptor.tag + ciphertext

    def decrypt(self, encrypted_data: bytes) -> bytes:
        nonce = encrypted_data[:12]
        tag = encrypted_data[12:28]
        ciphertext = encrypted_data[28:]

        cipher = Cipher(algorithms.AES(self._kms_master_key), modes.GCM(nonce, tag))
        decryptor = cipher.decryptor()
        return decryptor.update(ciphertext) + decryptor.finalize()

    @staticmethod
    def generate_user_key() -> bytes:
        return Fernet.generate_key()


"""

class EncryptionManager:

    def __init__(self, kms_master_key: bytes):
        self.kms_master_key = kms_master_key
        self.backend = default_backend()

    def _generate_hash(self, data: bytes) -> str:
        return hashlib.sha256(data).hexdigest()

    def encrypt(self, data: bytes) -> bytes:
        nonce = os.urandom(12)  # NONCE ל-GCM בגודל 12 בייט
        cipher = Cipher(algorithms.AES(self.kms_master_key), modes.GCM(nonce), backend=self.backend)
        encryptor = cipher.encryptor()
        ciphertext = encryptor.update(data) + encryptor.finalize()
        return nonce + encryptor.tag + ciphertext  # שילוב NONCE, תג אימות, והטקסט המוצפן

    def decrypt(self, encrypted_data: bytes, expected_hash: str) -> bytes:
        nonce = encrypted_data[:12]  # NONCE בגודל 12 בייט
        tag = encrypted_data[12:28]  # תג אימות בגודל 16 בייט
        ciphertext = encrypted_data[28:]  # שאר המידע המוצפן

        cipher = Cipher(algorithms.AES(self.kms_master_key), modes.GCM(nonce, tag), backend=self.backend)
        decryptor = cipher.decryptor()
        decrypted_data = decryptor.update(ciphertext) + decryptor.finalize()

        # Verify the hash after decryption
        if self._generate_hash(decrypted_data) != expected_hash:
            raise ValueError("Data integrity check failed. Hash mismatch.")

        return decrypted_data

    @staticmethod
    def generate_user_key() -> bytes:
        return Fernet.generate_key()
"""

# def init_encryption_manager():
#     KMS_MASTER_KEY = os.urandom(32)  # מחליף את AWS KMS לדוגמה
#     EncryptionManager.initialize(KMS_MASTER_KEY)
#
#
# # קריאה חד-פעמית בזמן עליית ה-POD
# init_encryption_manager()