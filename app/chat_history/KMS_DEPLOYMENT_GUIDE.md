# AWS KMS Chat Encryption Deployment Guide

## Overview

This guide covers the deployment of the AWS KMS-based encryption system for chat history. The implementation provides user-specific encryption keys managed by AWS KMS, with Redis caching for performance and comprehensive audit logging.

## Architecture

### Components
1. **AWS KMS Service** - Encrypts/decrypts user keys
2. **User Key Manager** - Manages user-specific encryption keys
3. **User Key Cache** - Redis-based caching for performance
4. **Encryption Manager** - Handles conversation encryption with user keys
5. **Chat History Manager** - Orchestrates the complete flow
6. **Audit Service** - Comprehensive logging to Firestore

### Data Flow
```
User Request → Get User Key (Cache/KMS) → Encrypt/Decrypt Conversation → Store/Retrieve Chat → Audit Log
```

## Prerequisites

### AWS Setup
1. **AWS KMS Key**: Create a customer-managed KMS key
2. **IAM Permissions**: Configure appropriate permissions for KMS operations
3. **AWS Credentials**: Set up credentials for the application

### Infrastructure
1. **MongoDB**: For storing encrypted user keys and chat data
2. **Redis**: For caching decrypted user keys
3. **Firestore**: For audit logging (existing analytics system)

## Configuration

### Environment Variables

Add these to your environment configuration:

```bash
# AWS KMS Configuration
AWS_KMS_KEY_ID=arn:aws:kms:us-east-1:123456789012:key/12345678-1234-1234-1234-123456789012
AWS_KMS_REGION=us-east-1

# User Key Cache Configuration
USER_KEY_CACHE_TTL=1800  # 30 minutes in seconds

# Existing configurations (ensure these are set)
MONGO_URL=mongodb://localhost:27017
REDIS_URL=redis://localhost:6379
S3_Region=us-east-1
```

### AWS KMS Key Policy

Example KMS key policy for the application:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "ChatEncryptionAccess",
            "Effect": "Allow",
            "Principal": {
                "AWS": "arn:aws:iam::123456789012:role/ChatApplicationRole"
            },
            "Action": [
                "kms:Encrypt",
                "kms:Decrypt",
                "kms:DescribeKey"
            ],
            "Resource": "*",
            "Condition": {
                "StringEquals": {
                    "kms:EncryptionContext:service": "chat_history",
                    "kms:EncryptionContext:purpose": "chat_encryption"
                }
            }
        }
    ]
}
```

### IAM Role Policy

Example IAM policy for the application role:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "kms:Encrypt",
                "kms:Decrypt",
                "kms:DescribeKey"
            ],
            "Resource": "arn:aws:kms:us-east-1:123456789012:key/12345678-1234-1234-1234-123456789012",
            "Condition": {
                "StringEquals": {
                    "kms:EncryptionContext:service": "chat_history",
                    "kms:EncryptionContext:purpose": "chat_encryption"
                }
            }
        }
    ]
}
```

## Deployment Steps

### 1. Initialize Services

```python
from app.chat_history.security.encrypt_manger import EncryptionManager
from app.chat_history.services.aws_kms_service import AWSKMSService
from app.chat_history.services.user_key_manager import UserKeyManager
from app.chat_history.services.encrypt_data_manager import ChatHistoryManager
from configs.app_config import AWS_KMS_KEY_ID, AWS_KMS_REGION

# Initialize encryption manager with master key
master_key = os.environ.get('KMS_MASTER_KEY').encode()  # 32 bytes
EncryptionManager.initialize(master_key)
encryption_manager = EncryptionManager.get_instance()

# Initialize KMS service
kms_service = AWSKMSService(AWS_KMS_KEY_ID, AWS_KMS_REGION)

# Test KMS connection
if not kms_service.test_kms_connection():
    raise RuntimeError("KMS connection failed")

# Initialize user key manager with KMS
user_key_manager = UserKeyManager(
    encryption_manager=encryption_manager,
    mongo_uri=MONGO_URL,
    db_name=MONGO_DB_HISTORY,
    collection_name="user_keys",
    kms_service=kms_service
)

# Initialize chat history manager
chat_manager = ChatHistoryManager(
    encryption_manager=encryption_manager,
    mongo_client=mongo_client,
    user_key_manager=user_key_manager
)
```

### 2. User Key Creation Flow

```python
# Create user key (automatically uses KMS if available)
user_id = "user123"
encrypted_user_key = user_key_manager.create_user_key(user_id)
print(f"Created KMS-encrypted user key for {user_id}")
```

### 3. Chat Storage Flow

```python
# Store chat with automatic encryption
redis_chat_data = RedisSchema(...)  # Your chat data
result = await chat_manager.store_chat(redis_chat_data)
print(f"Stored encrypted chat: {result}")
```

### 4. Chat Retrieval Flow

```python
# Retrieve chat with automatic decryption
chat_id = "chat456"
redis_chat = await chat_manager.extract_chat_history(chat_id)
print(f"Retrieved and decrypted chat: {redis_chat.chat_id}")
```

## Migration Strategy

### Phase 1: Deploy with KMS Support
1. Deploy the new code with KMS integration
2. Existing users continue with master key encryption
3. New users automatically get KMS encryption

### Phase 2: Migrate Existing Users
```python
# Migrate existing users to KMS
async def migrate_users_to_kms():
    users = get_all_users()  # Your user retrieval logic
    
    for user_id in users:
        try:
            success = await user_key_manager.migrate_user_to_kms(user_id)
            if success:
                print(f"Migrated {user_id} to KMS")
            else:
                print(f"Failed to migrate {user_id}")
        except Exception as e:
            print(f"Error migrating {user_id}: {e}")
```

### Phase 3: Cleanup (Optional)
- Remove master key encryption support
- Update schema to require KMS encryption

## Monitoring and Alerts

### Key Metrics to Monitor
1. **KMS Operation Success Rate**
   - Monitor encrypt/decrypt success rates
   - Alert on high failure rates

2. **Cache Hit Rate**
   - Monitor Redis cache performance
   - Alert on low hit rates

3. **Audit Log Anomalies**
   - Monitor for unusual patterns
   - Alert on security events

### CloudWatch Metrics
```python
# Example CloudWatch custom metrics
import boto3

cloudwatch = boto3.client('cloudwatch')

# KMS operation metrics
cloudwatch.put_metric_data(
    Namespace='ChatEncryption',
    MetricData=[
        {
            'MetricName': 'KMSEncryptionSuccess',
            'Value': 1,
            'Unit': 'Count',
            'Dimensions': [
                {
                    'Name': 'Service',
                    'Value': 'ChatHistory'
                }
            ]
        }
    ]
)
```

### Log Analysis Queries
```sql
-- Firestore/BigQuery queries for audit analysis

-- KMS operation failures
SELECT user_id, action, error_message, timestamp
FROM chat_encryption_audit
WHERE status = 'failure' AND action IN ('encrypt', 'decrypt')
ORDER BY timestamp DESC

-- Cache performance
SELECT 
  DATE(timestamp) as date,
  COUNT(*) as total_operations,
  SUM(CASE WHEN details LIKE '%cache hit%' THEN 1 ELSE 0 END) as cache_hits
FROM chat_encryption_audit
WHERE schema = 'UserKeyCacheService'
GROUP BY DATE(timestamp)
```

## Security Considerations

### Key Management
1. **KMS Key Rotation**: Enable automatic key rotation
2. **Access Control**: Use least privilege IAM policies
3. **Encryption Context**: Always use encryption context for KMS operations

### Data Protection
1. **In Transit**: All data encrypted in transit (HTTPS/TLS)
2. **At Rest**: Conversations encrypted with user-specific keys
3. **In Memory**: User keys cached with TTL, cleared on expiration

### Audit and Compliance
1. **Complete Audit Trail**: All operations logged to Firestore
2. **Data Integrity**: SHA-256 hashes verify conversation integrity
3. **Access Logging**: All key access operations logged

## Troubleshooting

### Common Issues

#### KMS Access Denied
```
Error: KMS ClientError AccessDenied: User is not authorized to perform kms:encrypt
```
**Solution**: Check IAM permissions and KMS key policy

#### Cache Connection Failed
```
Error: Redis connection failed
```
**Solution**: Verify Redis connectivity and credentials

#### Hash Verification Failed
```
Error: Hash verification failed - data integrity compromised
```
**Solution**: Check for data corruption, review recent changes

### Debug Commands

```python
# Test KMS connectivity
kms_service = AWSKMSService(AWS_KMS_KEY_ID)
if kms_service.test_kms_connection():
    print("KMS connection successful")

# Check cache statistics
cache_service = UserKeyCacheService()
stats = await cache_service.get_cache_stats()
print(f"Cache stats: {stats}")

# Verify user key encryption method
encryption_method = user_key_manager.get_user_encryption_method(user_id)
print(f"User {user_id} encryption method: {encryption_method}")
```

## Performance Optimization

### Caching Strategy
- **TTL**: 30 minutes default (configurable)
- **Invalidation**: Automatic on key updates
- **Preloading**: Consider preloading for high-traffic users

### Batch Operations
- Process multiple users in parallel for migrations
- Use connection pooling for database operations
- Implement circuit breakers for KMS operations

### Monitoring Performance
- Track encryption/decryption latency
- Monitor memory usage for cached keys
- Alert on performance degradation

## Backup and Recovery

### Key Backup
- KMS keys are automatically backed up by AWS
- Export user key metadata for disaster recovery
- Maintain audit logs for compliance

### Data Recovery
- Encrypted conversations can be recovered with user keys
- Hash verification ensures data integrity
- Audit logs provide operation history

## Testing

Run the comprehensive test suite:

```bash
python app/chat_history/test_kms_integration.py
```

This tests:
- User key encryption/decryption with KMS
- Conversation encryption with user keys
- Cache operations
- Complete integration flow
- Error handling scenarios
