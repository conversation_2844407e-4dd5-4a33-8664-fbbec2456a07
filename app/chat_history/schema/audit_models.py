from datetime import datetime
from enum import Enum
from typing import Optional

from pydantic import BaseModel, Field
from utils.datetime_utils import timestamp_now_il


class AuditActionEnum(str, Enum):
    """Enum for audit actions"""
    ENCRYPT = "encrypt"
    DECRYPT = "decrypt"
    STORE = "store"
    RETRIEVE = "retrieve"
    HASH_GENERATE = "hash_generate"
    HASH_VERIFY = "hash_verify"


class AuditStatusEnum(str, Enum):
    """Enum for audit status"""
    SUCCESS = "success"
    FAILURE = "failure"
    ERROR = "error"


class AuditLogEntry(BaseModel):
    """Model for audit log entries"""
    schema: str = Field(..., description="Schema name (e.g., 'MongoChatSchema')")
    user_id: str = Field(..., description="User ID")
    user_key_id: Optional[str] = Field(None, description="User key ID for encryption operations")
    chat_id: Optional[str] = Field(None, description="Chat ID")
    status: AuditStatusEnum = Field(..., description="Operation status")
    timestamp: str = Field(default_factory=timestamp_now_il, description="Timestamp in Jerusalem timezone")
    action: AuditActionEnum = Field(..., description="Action performed")
    details: Optional[str] = Field(None, description="Additional details about the operation")
    error_message: Optional[str] = Field(None, description="Error message if status is failure/error")

    class Config:
        use_enum_values = True
