from typing import Optional, List

from pydantic import BaseModel, Field, model_validator

from utils.conversation_manager import ConversationItem
from utils.pre_chat_dto import Filters, ModelSettings, ChatSettings


class MongoChatSchema(BaseModel):

    # id: Optional[Union[str, ObjectId]] = Field(default=None, alias="_id")
    user_id: str = Field(..., min_length=3)
    chat_id: str = Field(..., min_length=5)
    domain: str
    # Encrypted conversation data (new field)
    encrypted_conversation: Optional[bytes] = None
    # Conversation hash for integrity verification
    conversation_hash: Optional[str] = None
    # Keep original conversation field for backward compatibility (will be deprecated)
    conversation: Optional[List[ConversationItem]] = Field(default_factory=list)
    summaries: Optional[List[str]] = []
    model_settings: ModelSettings = Field(default_factory=ModelSettings)
    chat_settings: ChatSettings = Field(default_factory=ChatSettings)
    citations:Optional[List[dict]] = Field(default_factory=list)
    filters: Optional[Filters] = Field(default_factory=dict)

    @model_validator(mode="after")
    def check_update_time(cls, values):
        if values.chat_settings.update_time and values.chat_settings.update_time < values.chat_settings.start_time:
            raise ValueError("update_time cannot be before start_time")
        return values



    model_config = {
        "populate_by_name": True,
        "arbitrary_types_allowed": True,
    }

