import functools
import traceback
import async<PERSON>
from typing import Any, Callable, Optional

from chat_history.services.audit_service import AuditService
from chat_history.schema.audit_models import AuditActionEnum, AuditStatusEnum
from middlewares.logging_utils import app_logger as logger


def audit_log(
    schema: str,
    action: AuditActionEnum,
    user_id_param: str = "user_id",
    user_key_id_param: Optional[str] = None,
    chat_id_param: Optional[str] = None,
    details_template: Optional[str] = None
):
    """
    Decorator for automatic audit logging of function calls
    
    Args:
        schema: Schema name (e.g., 'UserKeyManager', 'ChatHistoryManager')
        action: Action being performed
        user_id_param: Parameter name that contains user_id
        user_key_id_param: Parameter name that contains user_key_id (optional)
        chat_id_param: Parameter name that contains chat_id (optional)
        details_template: Template for details message (optional)
    
    Usage:
        @audit_log("UserKeyManager", AuditActionEnum.ENCRYPT, user_id_param="user_id")
        def encrypt_user_key(self, user_id: str, user_key: bytes):
            # function implementation
            return result
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            return _execute_with_audit(func, schema, action, user_id_param, 
                                     user_key_id_param, chat_id_param, 
                                     details_template, args, kwargs)
        
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            return await _execute_with_audit_async(func, schema, action, user_id_param,
                                                 user_key_id_param, chat_id_param,
                                                 details_template, args, kwargs)
        
        # Return appropriate wrapper based on whether function is async
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


def _extract_param_value(param_name: str, args: tuple, kwargs: dict, func: Callable) -> Optional[str]:
    """Extract parameter value from function arguments"""
    if not param_name:
        return None
    
    try:
        # Try to get from kwargs first
        if param_name in kwargs:
            return str(kwargs[param_name])
        
        # Try to get from args using function signature
        import inspect
        sig = inspect.signature(func)
        param_names = list(sig.parameters.keys())
        
        if param_name in param_names:
            param_index = param_names.index(param_name)
            if param_index < len(args):
                return str(args[param_index])
        
        # Special handling for self/cls methods
        if param_name in ['user_id', 'chat_id'] and len(args) > 1:
            # Try common patterns
            if hasattr(args[1], param_name):
                return str(getattr(args[1], param_name))
        
        return None
    except Exception as e:
        logger.warning(f"Failed to extract parameter {param_name}: {e}")
        return None


def _generate_details(details_template: Optional[str], result: Any, args: tuple, kwargs: dict) -> Optional[str]:
    """Generate details message from template and result"""
    if not details_template:
        return None
    
    try:
        # Simple template substitution
        details = details_template
        
        # Replace common placeholders
        if hasattr(result, '__len__') and not isinstance(result, str):
            details = details.replace('{result_length}', str(len(result)))
        
        if isinstance(result, bytes):
            details = details.replace('{result_size}', f"{len(result)} bytes")
        
        if isinstance(result, dict) and 'modified_count' in result:
            details = details.replace('{modified_count}', str(result['modified_count']))
        
        return details
    except Exception as e:
        logger.warning(f"Failed to generate details from template: {e}")
        return None


def _execute_with_audit(func: Callable, schema: str, action: AuditActionEnum,
                       user_id_param: str, user_key_id_param: Optional[str],
                       chat_id_param: Optional[str], details_template: Optional[str],
                       args: tuple, kwargs: dict) -> Any:
    """Execute function with audit logging (sync version)"""
    
    # Extract parameters
    user_id = _extract_param_value(user_id_param, args, kwargs, func)
    user_key_id = _extract_param_value(user_key_id_param, args, kwargs, func) if user_key_id_param else None
    chat_id = _extract_param_value(chat_id_param, args, kwargs, func) if chat_id_param else None
    
    try:
        # Execute the function
        result = func(*args, **kwargs)
        
        # Generate details
        details = _generate_details(details_template, result, args, kwargs)
        
        # Log success
        AuditService.log_audit_event(
            schema=schema,
            user_id=user_id or "unknown",
            action=action,
            status=AuditStatusEnum.SUCCESS,
            user_key_id=user_key_id,
            chat_id=chat_id,
            details=details
        )
        
        return result
        
    except Exception as e:
        # Log failure
        error_message = str(e)
        
        AuditService.log_audit_event(
            schema=schema,
            user_id=user_id or "unknown",
            action=action,
            status=AuditStatusEnum.FAILURE,
            user_key_id=user_key_id,
            chat_id=chat_id,
            error_message=error_message
        )
        
        # Re-raise the exception
        raise


async def _execute_with_audit_async(func: Callable, schema: str, action: AuditActionEnum,
                                   user_id_param: str, user_key_id_param: Optional[str],
                                   chat_id_param: Optional[str], details_template: Optional[str],
                                   args: tuple, kwargs: dict) -> Any:
    """Execute function with audit logging (async version)"""
    
    # Extract parameters
    user_id = _extract_param_value(user_id_param, args, kwargs, func)
    user_key_id = _extract_param_value(user_key_id_param, args, kwargs, func) if user_key_id_param else None
    chat_id = _extract_param_value(chat_id_param, args, kwargs, func) if chat_id_param else None
    
    try:
        # Execute the async function
        result = await func(*args, **kwargs)
        
        # Generate details
        details = _generate_details(details_template, result, args, kwargs)
        
        # Log success
        AuditService.log_audit_event(
            schema=schema,
            user_id=user_id or "unknown",
            action=action,
            status=AuditStatusEnum.SUCCESS,
            user_key_id=user_key_id,
            chat_id=chat_id,
            details=details
        )
        
        return result
        
    except Exception as e:
        # Log failure
        error_message = str(e)
        
        AuditService.log_audit_event(
            schema=schema,
            user_id=user_id or "unknown",
            action=action,
            status=AuditStatusEnum.FAILURE,
            user_key_id=user_key_id,
            chat_id=chat_id,
            error_message=error_message
        )
        
        # Re-raise the exception
        raise


# Convenience decorators for common operations
def audit_encrypt(schema: str, user_id_param: str = "user_id", user_key_id_param: Optional[str] = None):
    """Convenience decorator for encryption operations"""
    return audit_log(schema, AuditActionEnum.ENCRYPT, user_id_param, user_key_id_param, 
                    details_template="Encrypted data: {result_size}")


def audit_decrypt(schema: str, user_id_param: str = "user_id", user_key_id_param: Optional[str] = None):
    """Convenience decorator for decryption operations"""
    return audit_log(schema, AuditActionEnum.DECRYPT, user_id_param, user_key_id_param,
                    details_template="Decrypted data: {result_size}")


def audit_store(schema: str, user_id_param: str = "user_id", chat_id_param: Optional[str] = None):
    """Convenience decorator for store operations"""
    return audit_log(schema, AuditActionEnum.STORE, user_id_param, chat_id_param=chat_id_param,
                    details_template="Stored data successfully")


def audit_retrieve(schema: str, user_id_param: str = "user_id", chat_id_param: Optional[str] = None):
    """Convenience decorator for retrieve operations"""
    return audit_log(schema, AuditActionEnum.RETRIEVE, user_id_param, chat_id_param=chat_id_param,
                    details_template="Retrieved data successfully")
