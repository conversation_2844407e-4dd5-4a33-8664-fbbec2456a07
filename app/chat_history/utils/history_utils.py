import json
from typing import Dict, Any

from pydantic.json import pydantic_encoder


def adjust_chat_history(chat_document: Dict[str, Any]) -> dict:
    chat_document = dict(chat_document)  # Convert to a mutable dict
    chat_document.pop('summaries', None)  # Remove _id if present
    chat_document['_id'] = str(chat_document.get('_id', ''))

    chat_document = json.loads(
        json.dumps(chat_document, default=pydantic_encoder)
    )
    conversation_chat = []
    for conversation_item in chat_document.get('conversation', []):
        if isinstance(conversation_item, dict) and conversation_item.get('action') in [0,2]:
            conversation_chat.append(conversation_item)
    chat_document['conversation'] = conversation_chat

    return chat_document
