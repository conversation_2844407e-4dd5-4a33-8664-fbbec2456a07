import json
from datetime import datetime
from enum import Enum
from typing import Dict, Any

from pydantic.json import pydantic_encoder


def adjust_chat_history(chat_document: Dict[str, Any]) -> dict:
    chat_document = dict(chat_document)  # Convert to a mutable dict
    chat_document.pop('summaries', None)  # Remove _id if present
    chat_document['_id'] = str(chat_document.get('_id', ''))

    chat_document = json.loads(
        json.dumps(chat_document, default=pydantic_encoder)
    )
    conversation_chat = []
    for conversation_item in chat_document.get('conversation', []):
        if isinstance(conversation_item, dict) and conversation_item.get('action') in [0,2]:
            conversation_chat.append(conversation_item)
    chat_document['conversation'] = conversation_chat

    return chat_document


class OrderByEnum(str, Enum):
    """Order by options for chat history."""
    UPDATED = "updated"
    CREATED = "created"


def get_mongo_field(order_by: OrderByEnum) -> str:
    """Convert enum to actual MongoDB field path."""
    field_mapping = {
        OrderByEnum.UPDATED: "chat_settings.update_time",
        OrderByEnum.CREATED: "chat_settings.start_time"
    }
    return field_mapping[order_by]


def convert_to_mutable_dict_recursive(obj):
    """
    Recursively converts immutable Mappings and Sequences into mutable dicts and lists.
    Also handles datetime/date objects by converting them to ISO format strings.
    """
    if isinstance(obj, datetime):
        return obj.isoformat()
    if isinstance(obj, dict):  # or collections.abc.Mapping
        return {k: convert_to_mutable_dict_recursive(v) for k, v in obj.items()}
    if isinstance(obj, list):  # or collections.abc.Sequence (but skip str)
        return [convert_to_mutable_dict_recursive(elem) for elem in obj]
    return obj


def validate_order_field(order_by: str) -> bool:
    """Validate order field."""
    try:
        OrderByEnum(order_by)
        return True
    except ValueError:
        return False
