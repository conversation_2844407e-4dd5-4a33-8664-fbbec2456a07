import concurrent.futures
from time import time
from typing import List

import cohere

from utils.dto import ExtractTextEmbeddingReuquest, ActionsEnum
from configs import app_config as conf
from configs.app_config import COHERE_HF_TOKENIZER_NAME, COHERE_EMBED_MODEL, EMBED_BATCH_SIZE, TOP_TOKENS
from middlewares.logging_utils import app_logger as logger, log_format
from utils.regex_util import clean_text_aggressive

_tokenizer = None


def get_tokenizer():
    try:
        global _tokenizer
        if _tokenizer is None:
            from transformers import AutoTokenizer
            _tokenizer = AutoTokenizer.from_pretrained(COHERE_HF_TOKENIZER_NAME)
        return _tokenizer
    except Exception as e:
        logger.error(f'Failed in get_tokenizer with COHERE_HF_TOKENIZER_NAME - {COHERE_HF_TOKENIZER_NAME}, error: {e}')
        raise Exception(str(e) if str(e) in conf.ERROR_KEYS else 'GET_TOKENIZER_ERROR')


def n_tokens(texts):
    try:
        if isinstance(texts, str):
            return len(get_tokenizer().tokenize(texts))
        else:
            if len(texts) == 0:
                return []

            sentence_lengths = []
            tokenizer = get_tokenizer()
            # sentence_lengths = [len(tokenizer.tokenize(sentence)) for sentence in clean_sentences]  # tgokenize one at a time
            # Encode the strings in a batch
            for t in texts:
                if len(t) == 0:
                    sentence_lengths.append(1)
                else:
                    sentence_lengths.append(len(tokenizer.tokenize(t)))

            return sentence_lengths
    except Exception as e:
        logger.error(f'Failed in n_tokens with texts {texts}, error: {e}')
        raise Exception(str(e) if str(e) in conf.ERROR_KEYS else 'N_TOKENS_ERROR')


async def embed_texts(cohere_client: cohere.Client, texts: List[str], input_type: str) -> List[List[float]]:
    # Function to embed a batch
    try:

        def embed_batch(batch_texts: List[str]) -> List[List[float]]:
            return cohere_client.embed(texts=batch_texts, model=COHERE_EMBED_MODEL, input_type=input_type).embeddings

        embeddings = []

        # Creating batches
        batches = [texts[i:i + EMBED_BATCH_SIZE] for i in range(0, len(texts), EMBED_BATCH_SIZE)]

        with concurrent.futures.ThreadPoolExecutor() as executor:
            results = list(executor.map(embed_batch, batches))

        # Await the results
        for res in results:
            embeddings.extend(res)

        return embeddings

    except Exception as e:
        logger.error(f'Failed in embed_texts with texts: {texts}, error: {e}')
        raise Exception(str(e) if str(e) in conf.ERROR_KEYS else 'EMBED_TEXTS_ERROR')


async def embed_text(cohere_client: cohere.Client, text: str, input_type: str) -> List[float]:
    try:
        response = await embed_texts(cohere_client, [text], input_type=input_type)

        return response[0]
    except Exception as e:
        raise e


async def extract_text_embedding(request: ExtractTextEmbeddingReuquest):
    cohere_client = request.cohere_client
    text_to_embed = request.text_to_embed
    chat_id = request.chat_id
    try:
        text_to_embed = clean_text_aggressive(text_to_embed)
        total_tokens = n_tokens(text_to_embed)
        logger.info(f"Token count of to_embedding: {total_tokens}")
        if total_tokens > TOP_TOKENS:
            # TODO - We want to know how many times it happened to check how we can document it so we can investigate later Elad said we will use analytics to document later
            logger.info(f'text to embed is : {total_tokens} tokens , and bigger then {TOP_TOKENS} tokens')
        embedding = await embed_text(cohere_client, text_to_embed, input_type="search_query")


        return embedding
    except Exception as e:
        logger.error(f'Failed to extract_context_embedding for: {chat_id}, error: {e}')
        raise Exception(str(e) if str(e) in conf.ERROR_KEYS else 'EXTRACT_TEXT_EMBEDDING_ERROR') from e
