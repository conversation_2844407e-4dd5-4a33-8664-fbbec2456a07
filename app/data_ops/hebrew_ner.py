import json
import re
from typing import List, Tuple


import configs.app_config as conf
from middlewares.logging_utils import app_logger as logger

# Load environment variables


def ner_model(ai_provider,text: str) -> dict:
    try:
        # Inject the user query into the <text> tag
        prompt_with_text=ai_provider.provider_choose('ner_haiku').prompt_template(text)

        message = ai_provider.provider_choose('ner_haiku').generate_message(prompt_with_text)

        # Log the raw response
        logger.info(f"Raw API response: {message}")

        # Extract and parse response content
        response_content = message.content
        if response_content and isinstance(response_content, list):
            content_block = response_content[0]
            if hasattr(content_block, 'text'):
                response_text = content_block.text

                # Log the response text
                logger.info(f"Response text: {response_text}")

                if response_text:
                    # Extract the JSON object from the response text
                    json_start = response_text.find('{')
                    json_end = response_text.rfind('}')
                    if json_start != -1 and json_end != -1:
                        json_text = response_text[json_start:json_end + 1]
                        if json_text:
                            try:
                                # Parse the JSON text to a dictionary
                                response_json = json.loads(json_text)
                                return response_json
                            except json.JSONDecodeError as e:
                                logger.error(f"Error parsing response as JSON: {e}")
                                return {"has_ner": False, "entities": []}
                    else:
                        logger.error(f"No valid JSON object found in response text: {response_text}")
                        return {"has_ner": False, "entities": []}
                else:
                    logger.error("Response text is empty")
                    return {"has_ner": False, "entities": []}
            else:
                logger.error(f"Response content is not a dictionary or does not contain 'text' key: {response_content}")
                return {"has_ner": False, "entities": []}
        else:
            logger.error("Response content is not a list or is empty")
            return {"has_ner": False, "entities": []}

    except Exception as e:
        logger.error(f"Error processing API response: {e}")
        return {"has_ner": False, "entities": []}


def check_hebrew_ner(ai_provider,query: str) -> Tuple[bool, List[dict]]:
    try:
        ner_results = ner_model(ai_provider,query)
        entities = ner_results.get("entities", [])

        if not entities:
            return False, []

        combined_entities = []
        query_words = query.split()

        for i, word in enumerate(query_words):
            if word in entities:
                combined_entities.append(word)
                if i + 2 < len(query_words) and query_words[i + 1] not in entities and query_words[i + 2] in entities:
                    combined_entities.append(query_words[i + 1])

        if len(combined_entities) > 1:
            combined_named_entity = ' '.join(combined_entities)
            combined_named_entity = re.sub(r' נ ', ' נגד ', combined_named_entity)

            if combined_named_entity not in entities:
                entities.append(combined_named_entity)

        return bool(entities), entities
    except Exception as e:
        logger.error(f'Failed in check_hebrew_ner with query - {query}, error: {e}')
        raise Exception(str(e) if str(e) in conf.ERROR_KEYS else 'CHECK_HEBREW_NER_ERROR')
