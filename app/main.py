from fastapi import <PERSON><PERSON><PERSON>
from dotenv import load_dotenv
from fastapi.middleware.cors import CORSMiddleware

from api.dependencies.mongo_db import get_mongo_client
from api.endpoints.chat_endpoint import Chat<PERSON>outer
from api.endpoints.endpoint_utils import utils_routers
from api.endpoints.history import history_router
from app.middlewares.trace_utils import EndpointNameMiddleware
from chat.chat_endpoint import chat_router
from configs.app_config import   GET_PROCEDURE_TYPE_URL
import configs.app_config as conf
import requests
import time
import json
import sys, os
from api.endpoints import  queries
import urllib3
from data_ops.bm25_encoder_manager import bm25_manager
from utils.analytics import FirestoreConnection
from utils.cache_db import close_redis
from middlewares.logging_utils import app_logger as logger
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


APP_DIR_PATH = os.path.dirname(__file__)
ROOT_DIR_PATH = os.path.dirname(APP_DIR_PATH)
sys.path.append(APP_DIR_PATH) # adding the "app" package to path
sys.path.append(ROOT_DIR_PATH) # adding the root directory to path

# setting up logger


# Setting up the environment

ENV_FILE_PATH = os.getenv('ENV_FILE_PATH', os.path.join(ROOT_DIR_PATH, 'configs', '.env'))
assert os.path.isfile(ENV_FILE_PATH), f'env file is missing from: {ENV_FILE_PATH}'
logger.info(f'Loading env file from: {ENV_FILE_PATH}')
load_dotenv(ENV_FILE_PATH)







logger.info('Setting up the app..')
is_dev = conf.ENVIRONMENT in ['local', 'dev']
logger.info(f'Environment is set to: {conf.ENVIRONMENT} (is_dev={is_dev})')
app = FastAPI(
    docs_url="/docs" if is_dev else None,
    redoc_url="/redoc" if is_dev else None
)




root_dir = os.path.dirname(os.path.abspath(__file__))

file_path = os.path.join(root_dir, 'utils/error_messages.json')

with open(file_path, 'r', encoding='utf-8') as file:
    data =json.load(file)

conf.MESSAGES_ERROR=data
conf.ERROR_KEYS = list(data.keys())


def get_with_retry(url, retries=3, delay=2):
    for attempt in range(retries):
        try:
            response = requests.get(url)
            if response.status_code == 200:
                return response.json()  
            else:
                print(f"Attempt {attempt+1} failed with status code {response.status_code}")
        except requests.exceptions.RequestException as e:
            raise RuntimeError('The call to url failed')
        
        if attempt < retries - 1:
            time.sleep(delay)
    
    raise RuntimeError('The call to url failed')

# conf.PROCEDURE_LIST = [dic.get('procName') for dic in get_with_retry(GET_PROCEDURE_TYPE_URL)]

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow any origin. Be careful with this in a production environment  # TODO - this should only allow our app once we know where and how it is deployed
    allow_credentials=True,
    allow_methods=["*"],  # Allow any method
    allow_headers=["*"],  # Allow any headers
)

# Add the EndpointNameMiddleware for logging purposes
app.add_middleware(EndpointNameMiddleware)

# Including the routers (API endpoints)

app.include_router(queries.router)

app.include_router(chat_router)
app.include_router(ChatRouter)
app.include_router(history_router)

app.include_router(utils_routers)

@app.on_event("startup")
async def startup_event():
    '''
    the decorator on event will run the function when the app starts up
    '''
    logger.info('Starting up app dependencies...')
    # Init firebase
    # init_connection_firestore()
    FirestoreConnection()

    # Init pinecone
    from api.dependencies.vector_db import init_pinecone
    ip = init_pinecone()

    from utils.cache_db import verify_redis_on_startup
    vr = verify_redis_on_startup()

    #load BM25 models to singleton object bm25_manager 
    bm25_manager.init_encoders()

    ## init mongodb
    mc= get_mongo_client()

    await ip
    await vr
    await mc



    logger.info('Finished starting up app dependencies')
    
    # Listen to key events for updating BM25 encoders
    # from threading import Thread
    # listener_thread = Thread(target=key_event_listener)
    # listener_thread.start()



@app.on_event("shutdown")
async def shutdown_event():
    await close_redis()



if __name__ == "__main__":
    import uvicorn
    logger.info('App is ready and will start serving requests shortly')
    # uvicorn.run(app, host="0.0.0.0", port=8000)  # single worker
    {uvicorn.run("main:app", host="0.0.0.0", port=8001, workers=1)}

    
