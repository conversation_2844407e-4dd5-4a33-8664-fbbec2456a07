from enum import Enum
from typing import List, Dict, Optional, Union, Any

from pydantic import BaseModel, field_validator

from utils.conversation_manager import ConversationEnum


class BM25MaagarEnum(str, Enum):
    law = 1
    verdict = 2


class DocumentTypeEnum(str, Enum):
    lawChunk = 'law_chunk'
    verdictChunk = 'verdict_chunk'
    fullVerdict = 'full_verdict'
    fullLaw = 'full_law'
    booksSummaries = 'books_summaries'


class SearchTypeEnum(str, Enum):
    init = ''
    preChat = "pre_chat"
    postChat = "post_chat"
    smartSearch = "smart_search"


class ConditionEnum(str, Enum):
    eq = "eq"
    neq = "neq"
    in_ = "in"  # 'in' is a reserved keyword in Python, so use 'in_'
    gt = "gt"
    lt = "lt"
    gte = "gte"
    lte = "lte"
    


class ColumnsEnum(str, Enum):
    title = "title"
    txt_id = "txt_id"
    book_id = "book_id"
    judges = "judges"
    date = "date"
    court = "court"
    procedures = "procedures"
    location = "location"
    procedure_type = "procedure_type"
    pages = "pages"
    decision_name = "decision_name"
    type = "type"
    sideNames = "side_names"
    legal_field_id = "legal_field_id"
    refCount = "ref_count"
    procedure_numbers = "procedure_numbers"
    prosecutors = "prosecutors"
    defenders = "defenders"
    representatives = "representatives"
    tribunal_id = "tribunal_id"


class Filter(BaseModel):
    column: ColumnsEnum
    condition: ConditionEnum
    value: Union[int, str, List[Union[str, int]]]


class SearchStatistics(BaseModel):
    clean_verdict_text: float
    embed_text: float
    sparse_embedding: float
    hybrid_convex_scale: float
    query_in_pincone: float
    combined_top_k_in_search: float
    consolidate_source_docs: float
    sorting_pinecone_results: float
    get_text_from_sql: float
    check_hebrew_ner: float
    sorting_results: float
    total_time: float



class QueryStatistics(BaseModel):
    get_procedures_txt_id_time: float
    get_full_text_time: float
    get_total_tokens_time: float
    get_chunks_for_large_texts_time: float
    embed_text_time: float
    search_query_in_multiple_indices_time: float
    clean_verdict_text: float
    embed_text_in_search: float
    sparse_embedding_in_search: float
    combined_top_k_in_search: float
    update_search_results: float
    count_tokens_time: float
    claude_time: float
    streaming_time: float
    redis_time: float
    related_time: float
    total_time: float


class TypeEnum(str, Enum):
    law = "law"
    verdict = "verdict"
    machshavot = "machshavot"
    ethics = "ethics"


class ListItemMachshavot(BaseModel):
    book_name: Optional[str] = None
    book_id: Optional[str] = None
    year: Optional[int] = None
    chapter_title: Optional[str] = None
    sub_chapter_id: Optional[str] = None
    sub_chapter_title: Optional[str] = None
    search_results_id: Optional[str] = None
    total_pages: Optional[int] = None
    page_number: Optional[int] = None
    legal_field: Optional[str] = None
    book_type: Optional[str] = None
    text: Optional[str] = None
    cId: Optional[str] = None

    ######
    source: Optional[str] = None
    doc_score: Optional[float] = None
    chunk_score: Optional[float] = None
    show_date: Optional[float] = None
    txtId: Optional[str] = None  ##book_id

    ####


class ChatRequest(BaseModel):
    chatId: str
    userId: str
    domain: str
    returnNearestAndRelated: Optional[bool]=False


class ChatResponse(BaseModel):
    chatId: str
    statusCode: int
    message: Optional[str] = None


class KilFromRedisRequest(BaseModel):
    chatId: str


class ActionsEnum(str, Enum):
    search = "search"
    query = "query"
    chat = "chat"


def create_conversation(conversation_old: Optional[List] = None):
    if conversation_old is None:
        conversation_old = []
    # Already handled the None case above

    for item in conversation_old:
        if "action" not in item:
            item["action"] = ConversationEnum.default_val.value

    return conversation_old


class Counter(BaseModel):
    userId: str
    action: ActionsEnum
    time: str


class SearchAnalytics(BaseModel):
    threadId: str  # thered_id
    type: SearchTypeEnum
    query: str
    templatePrompt: str
    vectors: List[float]  # to check
    filters: str
    # context : List[Dict[str, DocumentTypeEnum]]# [ {txId/cid,type} , ...]
    pineconeResults: List[Dict[str, str]]  # [ {cid,type} , ...]
    rerankResults: List[Dict[str, str]]
    embedTime: float
    vectorDbSearchTime: float
    sqlRetriveTime: float
    vectorDB: str  # pincone
    embedModel: str  # cohere
    cohereTokens: int
    rerankTime: float
    index: int  # in seach it 1
    metadata: str
    timeStamp: str
    domain: str
    txtId: Optional[Union[int, str]]
    userId: str


class QuestionsAndAnswersAnalytics(BaseModel):
    threadId: str
    # templatePrompt: str
    domain: str
    txtId: Optional[Union[int, str]]
    query: Optional[str]
    context: List[Dict[str, str]]  # [ {txId/cid,type} , ...]
    timeToFirstLetter: str
    timeToLastLetter: str
    tokensUpload: int
    tokenDownload: int
    genModel: str
    answer: str
    index: int
    metadata: str
    timeStamp: str
    userId: str


class SearchMultipleIndicesRequest(BaseModel):
    user_id: str
    chat_id: str
    query: str
    cohere_client: Any
    law_index: Any
    verdict_index: Any
    books_summaries_index: Any
    max_results: int
    filters: List[Filter] = []
    consolidate_source_docs: bool
    embedding: Optional[Any]
    search_analytics: Optional[Any]
    search_statistics_results: Optional[SearchStatistics]
    query_statistics_results: Optional[QueryStatistics]
    domain: Optional[TypeEnum]
    filters_formula: Optional[str]
    procedure: Optional[str]
    has_ner: Optional[bool]
    is_search: Optional[bool]
    search_in_pinecone_list: Optional[Any]
    ai_provider: Any


class CombinedTopKSearchRequest(BaseModel):
    law_index: Any
    verdict_index: Any
    books_summaries_index: Any
    embedding: Any
    query: str
    max_results: int
    filters: List[Filter] = []
    consolidate_source_docs: bool
    statistics_results: SearchStatistics
    sparse_law_embedding: Optional[Any]
    sparse_verdict_embedding: Optional[Any]
    filters_formula: Optional[str]
    procedure: Optional[str]
    has_ner: Optional[bool]
    is_search: Optional[bool]
    domain: Optional[TypeEnum]
    search_in_pinecone_list: Optional[Any]


class CollectionNameEnum(str, Enum):
    QuestionsAndAnswersAnalytics = "chat"
    SearchAnalytics = "search"
    ThreadAnalyitics = "thread"


class PreChatEndAnalyticsRequest(BaseModel):
    search_analytics_pre_chat: Any
    userId: Optional[str]
    chat_id: str
    domain: Optional[str]
    txt_Id: Optional[Union[int, str]]
    start: Optional[str]


# class SaveChatInRedisReuquest(BaseModel):
#     redis_pool: Any
#     user_id: str
#     chat_id: str
#     domain: str
#     query: Optional[str]
#     answer: Optional[str]
#     conversation: Optional[list]
#     summary: Optional[str]
#     chat_context: Optional[str]
#     txt_id: Any
#     search_results: Optional[list]
#     converted_results: Optional[list]
#     full_text_content: Optional[str]
#     last_q_and_a: Optional[str]
#     is_post: bool
#     chat_model: str = None
#     chat_mode: Optional[int] = 1
#     filters: Optional[List] = None
#     flag: Optional[bool] = True


class ServiceTypeEnum(str, Enum):
    chat = 'chat'
    search = 'search'
    view = 'view'


class ProductNameEnum(str, Enum):
    techdin = 3


class SearchIndexAndSortRequest(BaseModel):
    index: Any
    embeddings: Any
    top_k: int
    consolidate_source_docs: bool
    statistics_results: Any
    filter_dict: Dict[str, Any] = {}
    add_source: Optional[Any] = None
    sparse_vector: Optional[Any] = None
    query: Optional[str] = None
    sort: bool = True
    procedure: Optional[Any] = None
    has_ner: Optional[bool] = False
    is_law_index: bool = False




class SubChapterEnum(str, Enum):
    general = 1
    question = 2
    short_answer = 3
    long_answer = 4
    question_mc = 5

    @classmethod
    def get_or_default(cls, value):
        return getattr(cls, value, cls.general)


class AiLoadEntityRelationEnum(str, Enum):
    question_short_answer = 2
    question_long_answer = 3
    question_answer = 4
    question_mc_short_answer = 5
    question_mc_long_answer = 6
    general = 1  # ברירת מחדל

    @classmethod
    def get_or_default(cls, value):
        return getattr(cls, value, cls.general)


class MasterChunkBatchRequest(BaseModel):
    batch_id: str


class SourceEnum(str, Enum):
    """
    Enum for source types.
    """
    verdicts = "verdicts"
    ethics = "ethics"
    search = "search"


class ListItem(BaseModel):
    txtId: Any
    cId: Any
    source: str
    doc_score: float
    chunk_score: float
    normalized_score: float
    title: Optional[str] = None
    sectionNumbers: Optional[list] = None
    subject: Optional[str] = None
    text: Optional[str] = None
    legislation_id: Optional[Union[str, float]] = None
    chapter_title: Optional[str] = None
    provision_title: Optional[str] = None
    amendment_information: Optional[str] = None
    prosecutors: Optional[List[str]] = None
    defenders: Optional[List[str]] = None
    judges: Optional[List[str]] = None
    representatives: Optional[List[str]] = None
    court_name: Optional[str] = None
    procedures: Optional[Union[list, str]] = None
    show_date: Optional[float] = None
    location: Optional[str] = None
    procedure_type: Optional[list] = None
    pages: Optional[int] = None
    decision_name: Optional[str] = None
    ref_count: Optional[int] = None
    procedure_numbers: Optional[list] = None
    create_date: Optional[str] = None
    legal_field_book: Optional[str] = None
    side_names: Optional[List[str]] = None  # we dont need
    combined_names: Optional[List[str]] = []
    matched_ner_names: Optional[List[str]] = []
    refferer: Optional[bool] = None
    is_study_book: Optional[bool] = None
    sub_chapter_title: Optional[str] = None
    legal_field: Optional[str] = None
    book_type: Optional[str] = None
    page_number: Optional[int] = 0
    chapter: Optional[str] = None
    book_name: Optional[str] = None
    book_id: Optional[str] = None
    total_pages: Optional[int] = None

    @field_validator('page_number', mode='before')
    @classmethod
    def convert_str_to_int(cls, v):
        if isinstance(v, str) and v.strip().isdigit():
            return int(v)
        elif v in [None, '']:
            return None
        return v


class ListItemSample(BaseModel):
    txtId: Any
    cId: Any
    source: str
    doc_score: float
    chunk_score: float
    normalized_score: float
    title: Optional[str] = None
    sectionNumbers: Optional[list] = None
    subject: Optional[str] = None
    text: Optional[str] = None
    legislation_id: Optional[Union[str, float]] = None
    chapter_title: Optional[str] = None
    provision_title: Optional[str] = None
    amendment_information: Optional[str] = None
    prosecutors: Optional[List[str]] = None
    defenders: Optional[List[str]] = None
    judges: Optional[List[str]] = None
    representatives: Optional[List[str]] = None
    court_name: Optional[str] = None
    procedures: Optional[Union[list, str]] = None
    show_date: Optional[float] = None
    location: Optional[str] = None
    procedure_type: Optional[list] = None
    pages: Optional[int] = None
    decision_name: Optional[str] = None
    ref_count: Optional[int] = None
    procedure_numbers: Optional[list] = None
    create_date: Optional[str] = None
    legal_field_book: Optional[str] = None
    side_names: Optional[List[str]] = None
    combined_names: Optional[List[str]] = []
    matched_ner_names: Optional[List[str]] = []
    refferer: Optional[bool] = None
    is_study_book: Optional[bool] = None
    sub_chapter_title: Optional[str] = None
    legal_field: Optional[str] = None
    book_type: Optional[str] = None
    page_number: Optional[int] = 0
    chapter: Optional[str] = None
    book_name: Optional[str] = None
    book_id: Optional[str] = None
    total_pages: Optional[int] = None

    # 🔁 Validators to normalize types

    @field_validator('page_number', 'pages', 'ref_count', mode='before')
    @classmethod
    def convert_to_int(cls, v):
        if v in [None, '']:
            return None
        if isinstance(v, (float, str)) and str(v).strip().isdigit():
            return int(float(v))
        return v

    @field_validator('legislation_id', 'book_id', mode='before')
    @classmethod
    def convert_to_str(cls, v):
        if v is None:
            return None
        return str(v).strip()

    @field_validator('show_date', mode='before')
    @classmethod
    def convert_to_float(cls, v):
        if v in [None, '']:
            return None
        try:
            return float(v)
        except ValueError:
            return None

class ExtractTextEmbeddingReuquest(BaseModel):
    search_analytics_pre_chat: Optional[Any]=None
    user_id: str
    chat_id: str
    cohere_client: Any
    text_to_embed: str
    query_statistics_results: Optional[Any]=None


class RefactorValueFromRedisRequest(BaseModel):
    user_id: str
    chat_id: str
    start: float
    existing_value: Any


class ETLRequestStatusEnum(str, Enum):
    add = "add"
    remove = "remove"
    update = "update"
