from utils.dto import QueryStatistics, SearchStatistics


def update_statistics_results_clean_verdict_text(statistics_results, clean_verdict_text):
    statistics_results.clean_verdict_text = round(clean_verdict_text, 3)



def update_statistics_results_sparse_embedding(statistics_results, sparse_embedding):
    statistics_results.sparse_embedding = round(sparse_embedding, 3)

def update_statistics_results_hybrid_convex_scale(statistics_results, hybrid_convex_scale):
    statistics_results.hybrid_convex_scale += round(hybrid_convex_scale, 3)

def update_statistics_results_query_in_pincone(statistics_results, query_in_pincone):
    if statistics_results.query_in_pincone < query_in_pincone:
        statistics_results.query_in_pincone = round(query_in_pincone, 3)
    else:
        statistics_results.query_in_pincone = statistics_results.query_in_pincone

def update_statistics_results_combined_top_k_in_search(statistics_results, combined_top_k_in_search):
    statistics_results.combined_top_k_in_search = round(combined_top_k_in_search, 3)

def update_statistics_results_consolidate_source_docs(statistics_results, consolidate_source_docs_end):
    statistics_results.consolidate_source_docs = round(consolidate_source_docs_end, 3)

def update_statistics_results_sorting_pinecone_results(statistics_results, sorting_pinecone_results):
    statistics_results.sorting_pinecone_results += round(sorting_pinecone_results, 3)

def update_statistics_results_get_text_from_sql(statistics_results, get_text_from_sql):
    statistics_results.get_text_from_sql = round(get_text_from_sql, 3)


def update_statistics_results_sorting_results(statistics_results, sorting_results):
    statistics_results.sorting_results = round(sorting_results, 3)

def update_statistics_results_total_time(statistics_results, total_time):
    statistics_results.total_time = round(total_time, 3) 

def update_query_statistics_results_total_time(query_statistics_results, total_time):
    query_statistics_results.total_time = round(total_time, 3)

def update_query_statistics_results_related_time(query_statistics_results, related_time):
    query_statistics_results.related_time = round(related_time, 3)

def update_query_statistics_results_redis_time(query_statistics_results, redis_time):
    query_statistics_results.redis_time = round(redis_time, 3)

def update_query_statistics_results_streaming_time(query_statistics_results, streaming_time):
    query_statistics_results.streaming_time = round(streaming_time, 3)

def update_query_statistics_results_claude_time(query_statistics_results, claude_time):
    query_statistics_results.claude_time = round(claude_time, 3)

def update_query_statistics_results_count_tokens_time(query_statistics_results, count_tokens_time):
    query_statistics_results.count_tokens_time = round(count_tokens_time, 3)

def update_query_statistics_results_combined_top_k_in_search(query_statistics_results, combined_top_k_in_search):
    query_statistics_results.combined_top_k_in_search = round(combined_top_k_in_search, 3)

def update_query_statistics_results_sparse_embedding_in_search(query_statistics_results, sparse_embedding_in_search):
    query_statistics_results.sparse_embedding_in_search = round(sparse_embedding_in_search, 3)

def update_query_statistics_results_embed_text_in_search(query_statistics_results, embed_text_in_search):
    query_statistics_results.embed_text_in_search =embed_text_in_search

def update_query_statistics_results_clean_verdict_text(query_statistics_results, clean_verdict_text):
    query_statistics_results.clean_verdict_text = round(clean_verdict_text, 3)

def update_query_statistics_results_search_query_in_multiple_indices_time(query_statistics_results, search_query_in_multiple_indices_time):
    query_statistics_results.search_query_in_multiple_indices_time = round(search_query_in_multiple_indices_time, 3)

def update_query_statistics_results_embed_text_time(query_statistics_results, embed_text_time):
    query_statistics_results.embed_text_time = round(embed_text_time, 3)

def update_query_statistics_results_get_chunks_for_large_texts_time(query_statistics_results, get_chunks_for_large_texts_time):
    query_statistics_results.get_chunks_for_large_texts_time = round(get_chunks_for_large_texts_time, 3)

def update_query_statistics_results_get_total_tokens_time(query_statistics_results, get_total_tokens_time):
    query_statistics_results.get_total_tokens_time = round(get_total_tokens_time, 3)

def update_query_statistics_results_get_full_text_time(query_statistics_results, get_full_text_time):
    query_statistics_results.get_full_text_time = round(get_full_text_time, 3)

def update_query_statistics_results_get_procedures_txt_id_time(query_statistics_results, get_procedures_txt_id_time):
    query_statistics_results.get_procedures_txt_id_time = round(get_procedures_txt_id_time, 3)

def update_query_statistics_results_update_search_results_time(query_statistics_results, update_search_results):
    query_statistics_results.update_search_results = round(update_search_results, 3)
    

    
def get_search_statistics_results():
    return SearchStatistics(clean_verdict_text=0, embed_text=0, sparse_embedding=0,combined_top_k_in_search=0, hybrid_convex_scale=0,
                        query_in_pincone=0, consolidate_source_docs=0, sorting_pinecone_results=0, get_text_from_sql=0,
                        check_hebrew_ner=0, sorting_results=0, total_time=0)

def get_query_statistics_results():
    return QueryStatistics(get_procedures_txt_id_time=0, get_full_text_time=0, get_total_tokens_time=0, get_chunks_for_large_texts_time=0,
                            embed_text_time=0, search_query_in_multiple_indices_time=0, clean_verdict_text=0, embed_text_in_search=0, 
                            sparse_embedding_in_search=0, combined_top_k_in_search=0, update_search_results=0, count_tokens_time=0, claude_time=0, streaming_time=0,
                            redis_time=0, related_time=0, total_time=0) 