import asyncio
import datetime

from dotenv import load_dotenv

from app.utils.dto import CollectionNameEnum, QuestionsAndAnswersAnalytics, SearchAnalytics, SearchTypeEnum, \
    PreChatEndAnalyticsRequest
from configs.app_config import COHERE_EMBED_MODEL, S3_Bucket_Access_Key, S3_Bucket_Secret_Key, S3_Region, \
    S3_BUCKET_CONFIG_NAME, ENVIRONMENT
from middlewares.logging_utils import app_logger as logger

load_dotenv()  # This will load variables from a .env file, if you're using one

import json
from google.cloud import firestore
from google.oauth2 import service_account


class FirestoreConnection:
    _instance = None

    def __new__(cls, bucket_name=S3_BUCKET_CONFIG_NAME):
        if cls._instance is None:
            if ENVIRONMENT in ['dev', 'qa', 'local']:
                key_name = f"dev_firebase_config.json"
            elif ENVIRONMENT == 'prod':
                key_name = f"prod_firebase_config.json"
            else:
                raise Exception(f"Invalid environment: {ENVIRONMENT}")

            cls._instance = super(FirestoreConnection, cls).__new__(cls)
            cls._instance._initialize(bucket_name, key_name)
        return cls._instance

    def _initialize(self, bucket_name, key_name):
        # Initialize S3 client
        start = datetime.datetime.now()
        # import boto3
        # try:
        #     s3 = boto3.client(
        #         's3',
        #         aws_access_key_id=S3_Bucket_Access_Key,
        #         aws_secret_access_key=S3_Bucket_Secret_Key,
        #         region_name=S3_Region
        #     )
        #     # Download Firebase config file
        #     file_byte = s3.get_object(Bucket=bucket_name, Key=key_name).get('Body').read()
        #     download_file = datetime.datetime.now()
        # except Exception as e:
        #     logger.error(f"Error downloading Firebase config file from S3: {e}")
        #     raise e
        try:
            from pathlib import Path
            config_path = Path(__file__).parent / key_name
            # Load the JSON configuration
            with open(config_path, 'r') as f:
                file_json = json.load(f)
            # Initialize Firestore credentials
            credentials = service_account.Credentials.from_service_account_info(file_json)
            self.client = firestore.Client(credentials=credentials)
            logger.info(
                f"Firestore connection initialized successfully TotalTime: {datetime.datetime.now() - start}")
        except Exception as e:
            logger.error(f"Error initializing Firestore connection: {e}")
            raise e

    def get_client(self):
        return self.client


async def create_document_async(collection_name: str, data: dict):
    firestore_connection = FirestoreConnection().get_client()
    doc_ref = firestore_connection.collection(collection_name).document()
    doc_ref.set(data)
    logger.info({"message": "Document created successfully", "document_id": doc_ref.id,
                 "collection_name": str(collection_name)})


def create_document(collection_name: str, data: dict):
    asyncio.create_task(create_document_async(collection_name, data))




def get_search_analytics(threadId='', type=SearchTypeEnum.init._value_, vectors=[], query='', templatePrompt='',
                         filters='', pineconeResults=[], domain='', txtId=0,
                         rerankResults=[], embedTime=0.0, vectorDbSearchTime=0.0, sqlRetriveTime=0.0,
                         vectorDB='PINECONE', embedModel=COHERE_EMBED_MODEL, cohereTokens=0, rerankTime=0.0, index=0,
                         metadata='', timeStamp='', userId='', root='', version=''):
    return SearchAnalytics(threadId=threadId, type=type, query=query, vectors=vectors, templatePrompt=templatePrompt,
                           filters=filters, pineconeResults=pineconeResults, domain=domain, txtId=txtId,
                           rerankResults=rerankResults, embedTime=embedTime, vectorDbSearchTime=vectorDbSearchTime,
                           sqlRetriveTime=sqlRetriveTime, vectorDB=vectorDB, embedModel=embedModel,
                           cohereTokens=cohereTokens, rerankTime=rerankTime, index=index, metadata=metadata,
                           timeStamp=timeStamp, userId=userId, root=root, version=version)


# def get_questions_and_answers_analytics(threadId='', query='', domain='', txtId=0, timeToFirstLetter='', context=[],
#                                         timeToLastLetter='', tokensUpload=0, tokenDownload=0, genModel='claude',
#                                         answer='', index=0, metadata='', timeStamp='', userId=''):
#     return QuestionsAndAnswersAnalytics(threadId=threadId, query=query, domain=domain, txtId=txtId, context=context,
#                                         timeToFirstLetter=timeToFirstLetter, timeToLastLetter=timeToLastLetter,
#                                         tokensUpload=tokensUpload, tokenDownload=tokenDownload, genModel=genModel,
#                                         answer=answer, index=index, metadata=metadata, timeStamp=timeStamp,
#                                         userId=userId)


# async def save_chat_analytics(questions_and_answers_analytics):
#     from datetime import datetime
#
#     try:
#         questions_and_answers_analytics.timeStamp = str(datetime.now())
#         await create_document_async(CollectionNameEnum.QuestionsAndAnswersAnalytics.value,
#                                     questions_and_answers_analytics.dict())
#         logger.debug(
#             f'Starting to save questions_and_answers_analytics for chat_id: {questions_and_answers_analytics.threadId} stat:{questions_and_answers_analytics}')
#         ## save search_analytics_pre_chat to FB - json.dumps(search_analytics_pre_chat.dict(), ensure_ascii=False)
#     except Exception as e:
#         logger.error(f'Firebase error - {e}')


# async def log_pre_chat_end_analytics(data: PreChatEndAnalyticsRequest):
#     from datetime import datetime
#     search_analytics_pre_chat = data.search_analytics_pre_chat
#     chat_id = data.chat_id
#     search_analytics_pre_chat.timeStamp = str(datetime.now())
#     try:
#         create_document(CollectionNameEnum.SearchAnalytics.value, search_analytics_pre_chat.dict())
#         logger.debug(
#             f'Starting to save search_analytics_pre_chat for chat_id: {chat_id} stat:{search_analytics_pre_chat}')
#     except Exception as e:
#         logger.error(f'Firebase error - {e}')
#     logger.info(f'Starting to answer the question for chat_id: {chat_id}')
