import json
import re

import bisect
from typing import List

import configs.app_config as conf

from utils.dto import ListItemMachshavot, DocumentTypeEnum, ListItem, ListItemSample
from configs.app_config import SCORE_STARS_THRESHOLD
from middlewares.logging_utils import app_logger as logger
from utils.constants_util import STUDY_BOOK, NORMALIZE_NAMES


class ListItemEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, ListItem):
            return obj.dict()
        if isinstance(obj, ListItemMachshavot):
            return obj.dict()
        return super().default(obj)


def safe_convert_txt_id(txt_id_value):
    if isinstance(txt_id_value, str) and '-' in txt_id_value:
        # Handle custom cases, for example, logging a warning or processing differently
        print(f"Warning: txt_id contains non-numeric characters: {txt_id_value}")
        return transform_pattern(txt_id_value)
    try:
        return int(txt_id_value)
    except (ValueError, TypeError):
        return transform_pattern(txt_id_value)



def transform_pattern(input_str):
    parts = input_str.split('-')

    if len(parts) == 2 and parts[0].isdigit() and parts[1].isdigit():
        transformed_str = f"-{int(parts[0]):05d}00000{int(parts[1]):d}"
        return int(transformed_str)
    else:
        return input_str





def extract_procedure_number(query):
    # Search for either of the two procedure_number
    pattern = r'\d{2,}-\d{2}-\d{2}|\b\d+/\d+\b'
    match = re.search(pattern, query)
    return match.group() if match else None

def normalize_score(score):
    return [0., 0.5, 1., 1.5, 2., 2.5, 3.][bisect.bisect_left(SCORE_STARS_THRESHOLD, score)]


def get_type(res):
    maagar_id = res.get('metadata').get('maagarId')
    if maagar_id == 2:
        return DocumentTypeEnum.verdictChunk.value
    elif maagar_id == 1:
        return DocumentTypeEnum.lawChunk.value
    else:
        return ''




def convert_search_results_to_list_items(search_results) -> List[ListItemSample]:
    try:
        # the ids are returned as floats but they should be treated as ints
        return [ListItemSample(
            txtId=safe_convert_txt_id(result['metadata'].get('txt_id')) if result['metadata'].get(
                'txt_id') else transform_pattern(result.get('id')),
            cId=str(result.get('metadata', {}).get('cid') or result.get('id')) if 'metadata' in result and 'cid' in
                                                                                  result[
                                                                                      'metadata'] or 'id' in result else None,
            # TODO work accordig to PEP8
            source=result['metadata'].get('source', 'Na'),
            doc_score=result.get('score', 0),
            chunk_score=result.get('chunk_score', result.get('score', 0)),  # onlƒy if there was consolidation
            normalized_score=normalize_score(result.get('chunk_score', result['score'])) if result.get('score') else 0,
            title=adding_quotations_marks_to_procedure(
                ' ' + result['metadata'].get('title', ' ') or result['metadata'].get('verdict_title', STUDY_BOOK.get(
                    result['metadata'].get('legal_field_book')))),  # TODO - in law also add here the section number?
            sectionNumbers=result['metadata'].get('section_numbers', []),
            subject=result['metadata'].get('chapter_name', 'Na') if not result['metadata'].get('subject') else result[
                'metadata'].get('subject'),
            text=result['metadata'].get('text') or result['metadata'].get('summary_text') or result['metadata'].get(
                'chunk_text') or result['metadata'].get('provision_text', 'Na'),
            legislation_id=result['metadata'].get('legislation_id', ''),
            chapter_title=result['metadata'].get('chapter_title') or result['metadata'].get('chapter_name', ''),
            sub_chapter_title=result['metadata'].get('sub_chapter_name', ''),
            legal_field=result['metadata'].get('legal_field', ''),
            book_type=result['metadata'].get('book_type', ''),
            page_number=result['metadata'].get('page_number', ''),
            provision_title=result['metadata'].get('provision_title', ''),
            amendment_information=result['metadata'].get('amendment_information', ''),
            prosecutors=result['metadata'].get('prosecutors', []),
            defenders=result['metadata'].get('defenders', []),
            judges=result['metadata'].get('judges', []),
            representatives=result['metadata'].get('representatives', []),
            court_name=result['metadata'].get('court_name', ''),
            procedures=[result['metadata'].get('procedures')] if isinstance(result['metadata'].get('procedures'),
                                                                            str) else result['metadata'].get(
                'procedures', []),
            show_date=result['metadata'].get('show_date', 0),
            location=result['metadata'].get('location', ''),
            procedure_type=result['metadata'].get('procedure_type', []),
            pages=result['metadata'].get('pages', 0),
            decision_name=result['metadata'].get('decision_name', ''),
            ref_count=result['metadata'].get('ref_count', 0),
            procedure_numbers=result['metadata'].get('procedure_numbers', []),
            create_date=str(result['metadata'].get('create_date', '')),
            refferer=result['metadata'].get("refferer", False),
            chapter=result['metadata'].get('chapter_title') or result['metadata'].get('chapter_name', ''),
            book_name=result['metadata'].get('legal_field', '') + '-' + result['metadata'].get('book_type', ''),
            book_id=result['metadata'].get('book_id', ''),
            total_pages=result['metadata'].get('total_pages', 0),
            is_study_book=result['metadata'].get('legal_field_book') and result['metadata'].get(
                'legal_field_book') in STUDY_BOOK

        ).model_dump() for result in search_results
        ]
    except Exception as e:
        logger.error(f'Failed to convert search results to list items, error: {e}')
        raise Exception(str(e) if str(e) in conf.ERROR_KEYS else 'CONVERT_RESULTS_ERROR') from e


def adding_quotations_marks_to_procedure(text):
    try:
        # Invert the normalize_names dictionary
        inverted_normalize_names = {v: k for k, v in NORMALIZE_NAMES.items()}

        # Perform replacements in the text using the inverted dictionary
        for value, key in inverted_normalize_names.items():
            text = text.replace(value, key)

        return text

    except Exception as e:
        logger.error(f'Failed in adding_quotations_marks_to_procedure with text: {text}.error: {e}')
        raise Exception(str(e) if str(e) in conf.ERROR_KEYS else 'QUOTATIONS_MARKS_TO_PROCEDURE_ERROR')
