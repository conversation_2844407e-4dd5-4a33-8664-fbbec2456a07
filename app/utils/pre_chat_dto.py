from datetime import datetime
from typing import Any, Dict, <PERSON>, Optional
from typing import List, Optional

import pytz
from pydantic import BaseModel, Field, model_validator, PrivateAttr, ConfigDict

from configs.app_config import MAX_QUERY_LEN
from services.pre_chat.request_stratgey import ChatTypeClient
from utils.conversation_manager import ConversationItem, ConversationManager


def timestamp_3utc():
    return datetime.now(pytz.timezone("Asia/Jerusalem"))



from enum import Enum


class ChatTypeAnalytics(str, Enum):
    UNKNOWN = "unknown"
    VERDICT_QUERY = "verdict_query"
    VERDICT_DOC_QUERY = "verdict_doc_query"
    LAW_QUERY = "law_query"
    LAW_DOC_QUERY = "law_doc_query"
    THOUGHTS_LEGAL_FIELD_QUERY = "thoughts_legal_field_query"
    THOUGHTS_BOOK_QUERY = "thoughts_book_query"
    THOUGHTS_TRAINING_SUBJECT_QUERY = "thoughts_training_subject_query"


class TypeEnum(str, Enum):
    law = "law"
    verdict = "verdict"
    machshavot = "machshavot"
    ethics = "ethics"

    @classmethod
    def from_value(cls, domain):
        """Return the Enum member name from its value."""
        for member in cls:
            if member.value == domain:
                return member
        raise ValueError(f"No matching Enum for value: {domain}")


class DocRef(BaseModel):
    id: Union[int, str]
    type: str


class PreChatAnalytics(BaseModel):
    user_id: str
    chat_id: str
    query_index: int
    query_length: int
    timestamp: datetime = Field(default_factory=timestamp_3utc)
    chat_type_analytics: ChatTypeAnalytics = ChatTypeAnalytics.UNKNOWN
    prompt_version: Optional[str] = None
    ner: List[Any] = []
    docs_id: List[Any] = []
    search_alpha: Optional[float] = None


class VectorDbInstance(BaseModel):
    Machshavot: Any
    Verdict: Any
    Law: Any


class ConnectionSource(BaseModel):
    vector_db_instance: VectorDbInstance
    embed_client_instance: Any
    redis_instance: Any
    ai_provider: Any
    mongo_client: Any


class NerAgent(BaseModel):
    has_ner: bool = False
    entities: list = []


class SourceTypeEnum(str, Enum):
    book_id = "book_id"
    legal_field_id = "legal_field_id"
    txt_id = "txt_id"
    question_id = "question_id"


class Query(BaseModel):
    raw_query: str
    clean_query: Optional[str] = None
    embed_query: Optional[List[float]] = []
    ner: Optional[NerAgent] = None
    search_alpha: Optional[float] = 0.0

    @model_validator(mode="before")
    def init_ner(cls, values):
        values["ner"] = NerAgent(has_ner=False, entities=[])
        return values


class Filter(BaseModel):
    type: SourceTypeEnum
    value: Union[str, int]


class Filters(BaseModel):
    docs: Optional[List[Filter]] = []


class ModelSettings(BaseModel):
    model: str = "premium_model"
    citation: bool = True
    prompt_template: Optional[str] = None


class DataContext(BaseModel):
    type: str = "chunks_document"
    master_chunks: Optional[Dict[Union[int, str], Dict[str, str]]] = {}
    data_raw: Optional[Any] = {}
    full_text_content: Optional[str] = None

    model_config = {
        "strict": False
    }




class SearchTypeEnum(str, Enum):
    specific_document = "specific_document"
    full_db = "full_db"
    trainer = "trainer"

    @classmethod
    def from_value(cls, search_type):
        """Return the Enum member name from its value."""
        for member in cls:
            if member.value == search_type:
                return member
        raise ValueError(f"No matching Enum for value: {search_type}")


class ChatSettings(BaseModel):
    return_nearest_and_related: bool = True
    chat_start_time: datetime = Field(default_factory=timestamp_3utc)
    update_time: Optional[datetime] = Field(default_factory=timestamp_3utc)

    documents: List[Any] = Field(default_factory=list)
    search_type: Optional[SearchTypeEnum] = SearchTypeEnum.full_db
    chat_type_client: Optional[ChatTypeClient] = None
    session_index: int = 0
    citation_count: int = 1
    existing_chat: Optional[bool] = False
    title: Optional[str] = "צאט חדש"


class PreChatManager(BaseModel):
    connection_source: ConnectionSource
    chatId: Union[None, str] = None
    userId: str
    query: Union[Query, str] = None
    domain: TypeEnum
    conversation: List[ConversationItem] = Field(default_factory=list)
    summaries: Optional[List[str]] = []
    product_type: Optional[str] = None
    filters: Optional[Filters] = Field(default_factory=dict)
    data: Optional[DataContext] = Field(default_factory=DataContext)
    model_settings: Optional[ModelSettings] = Field(default_factory=ModelSettings)
    chat_settings: Optional[ChatSettings] = Field(default_factory=ChatSettings)
    citations: Optional[List[Dict]] = Field(default_factory=list)
    # pre_chat_analytics: Optional[PreChatAnalytics] = Field(default_factory=PreChatAnalytics)

    _conversation_manager: "ConversationManager" = PrivateAttr()

    @model_validator(mode="before")
    def init_query(cls, values):
        if isinstance(values.get("query"), str):
            values["query"] = Query(raw_query=values["query"], clean_query=None, embed_query=[])
        return values

    def model_post_init(self, __context):
        # Initialize ConversationManager AFTER model is built
        self._conversation_manager = ConversationManager(
            conversation=self.conversation,
            session_index=self.chat_settings.session_index,
        )

    # 🔥 Expose methods directly for convenience
    def add_to_conversation(self, item: ConversationItem):
        self._conversation_manager.add(item)
        self.conversation = self._conversation_manager.conversation
        self.chat_settings.session_index = self._conversation_manager.session_index


class PreChatResponse(BaseModel):
    chat_id: str
    return_nearest_and_related: bool
    message: Optional[str] = None


###this is will be the request object that will be passed from the route to the service all the validation will be done here
class PreChatRequest(BaseModel):
    chatId: Union[None, str] = None
    query: str = Field(..., max_length=MAX_QUERY_LEN, min_length=4)
    userId: str
    domain: TypeEnum
    filters: Optional[Filters] = Filters()

    model_config = ConfigDict(extra='forbid')

    @model_validator(mode='before')
    def validate_filters(cls, values):
        filters = values.get('filters', {})
        domain = values.get('domain')

        if isinstance(filters, dict):
            filters = Filters(**filters)
            values['filters'] = filters

        if filters and filters.docs:
            if len(filters.docs) > 1 and domain != "machshavot":
                raise ValueError("Only one filter is allowed at the moment.")

            valid_domains = {
                TypeEnum.law: ["txt_id"],
                TypeEnum.verdict: ["txt_id"],
                TypeEnum.ethics: ["txt_id"],
                TypeEnum.machshavot: ["book_id", "legal_field_id", "question_id"],
            }

            # קבלת סוגי הפילטרים המותרים עבור הדומיין
            valid_filter_types = valid_domains.get(domain)

            # אם הדומיין לא נמצא או שאין לו פילטרים מותרים
            if not valid_filter_types:
                raise ValueError(f"No valid filters for domain '{domain}'")

            # וולידציה שכל פילטר מתאים לדומיין
            for filter in filters.docs:
                if filter.type not in valid_filter_types:
                    raise ValueError(f"Filter type '{filter.type}' is not valid for domain '{domain}'")

        return values


if __name__ == '__main__':
    t = PreChatAnalytics(
        user_id="123",
        chat_id="456",
        query_index=1,
        query_length=10,
        chat_type_analytics="law"
    )
    print(t)



class ChatChunkContext(BaseModel):
    docId: Union[str, int]
    type: str
    cited: Optional[bool] = None
    master_chunk: Optional[bool] = None
    score: Optional[float] = None


class ListChunkContext(BaseModel):
    docId: Union[str, int]
    type: str
    score: Optional[float] = None
