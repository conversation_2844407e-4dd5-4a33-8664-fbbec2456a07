from utils.dto import ConditionEnum

# preprocess constants

SUFFIX_LETTERS  = ['ב', 'ל', 'ש', 'כ', 'מ', 'ו', 'ה']

NORMALIZE_NAMES = {
    " א\"ב ": " אב ",
    " דב\"ע ": " דבע ",
    " ד\"ט ": " דט ",
    " אפ\"ח ": " אפח ",
    " ד\"מ ": " דמ ",
    " א\"צ ": " אצ ",
    " ד\"נ ": " דנ ",
    " בג\"צ ": " בגצ ",
    " דנ\"א ": " דנא ",
    " בד\"א ": " בדא ",
    " דנ\"מ ": " דנמ ",
    " בד\"ם ": " בדם ",
    " דנ\"פ ": " דנפ ",
    " בה\"נ ": " בהנ ",
    " דנג\"צ": " דנגצ ",
    " ב\"ל ": " בל ",
    " ה\"כ ": " הכ ",
    " בע\"מ ": " בעמ ",
    " בפ\"מ ": " בפמ ",
    " ה\"נ ": " הנ ",
    " בפ\"ת ": " בפת ",
    " ה\"ע ": " הע ",
    " ב\"ק ": " בק ",
    " ה\"פ ": " הפ ",
    " הפ\"ב ": " הפב ",
    " בר\"ע ": " ברע ",
    " ה\"ת ": " הת ",
    " בר\"ש ": " ברש ",
    " ו\"ע ": " וע ",
    " ע\"ב ": " עב ",
    " ב\"ש ": " בש ",
    " וש\"מ ": " ושמ ",
    " בש\"א ": " בשא ",
    " בשג\"ץ ": " בשגצ ",
    " בש\"פ ": " בשפ ",
    " בש\"ע ": " בשע ",
    " מק\"מ ": " מקמ ",
    " עמ\"ש ": " עמש ",
    " נ\"ב ": " נב ",
    " ס\"ק ": " סק ",
    " סק\"ב ": " סקב ",
    " ע\"ע ": " עע ",
    " ע\"א ": " עא ",
    " ע\"פ ": " עפ ",
    " עפ\"א ": " עפא ",
    " עפ\"ג ": " עפג ",
    " עב\"ל ": " עבל ",
    " עפ\"ס ": " עפס ",
    " עבמ\"צ ": " עבמצ ",
    " עק\"מ ": " עקמ ",
    " עק\"נ ": " עקנ ",
    " עד\"מ ": " עדמ ",
    " עק\"פ ": " עקפ ",
    " ער\"מ ": " ערמ ",
    " עחה\"ס ": " עחהס ",
    " ע\"ש ": " עש ",
    " על\"ע ": " עלע ",
    " עש\"א ": " עשא ",
    " ע\"מ ": " עמ ",
    " עש\"מ ": " עשמ ",
    " עמ\"ה ": " עמה ",
    " עמ\"ח ": " עמח ",
    " עש\"ת ": " עשת ",
    " עמ\"י ": " עמי ",
    " עת\"א ": " עתא ",
    " עמ\"מ ": " עממ ",
    " עת\"מ ": " עתמ ",
    " עמ\"נ ": " עמנ ",
    " פ\"א ": " פא ",
    " תב\"כ ": " תבכ ",
    " תב\"מ ": " תבמ ",
    " פל\"ע ": " פלע ",
    " תב\"ר ": " תבר ",
    " פ\"מ ": " פמ ",
    " ת\"ד ": " תד ",
    " תה\"ג ": " תהג ",
    " תה\"ס ": " תהס ",
    " ת\"ח ": " תח ",
    " תח\"פ ": " תחפ ",
    " ת\"ט ": " תט ",
    " תי\"א ": " תיא ",
    " ק\"פ ": " קפ ",
    " תי\"פ ": " תיפ ",
    " רמ\"ש ": " רמש ",
    " ת\"מ ": " תמ ",
    " תמ\"ש ": " תמש ",
    " רע\"א ": " רעא ",
    " ת\"ע ": " תע ",
    " רע\"ב ": " רעב ",
    " ת\"פ ": " תפ ",
    " רע\"פ ": " רעפ ",
    " רצ\"פ ": " רצפ ",
    " רת\"ק ": " רתק ",
    " ת\"ק ": " תק ",
    " ש\"ע ": " שע ",
    " תר\"מ ": " תרמ ",
    " ש\"ש ": " שש ",
    " תת\"ח ": " תתח ",
    " תא\"מ ": " תאמ ",
    " תת\"ע ": " תתע ",
    " תא\"פ ": " תאפ ",
    " תא\"ק ": " תאק ",
    " תא\"ר ": " תאר ",
    " ת\"א ": " תא ",
    " ת\"צ ": " תצ ",
    " א\"ר ": " אר ",
    " א\"פ ": " אפ ",
    " ב\"א ": " בא ",
    " ב\"י ": " בי ",
    " ב\"מ ": " במ ",
    " ב\"צ ": " בצ ",
    " ב\"ה ": " בה ",
    " בע\"ה ": " בעה ",
    " ב\"ד ": " בד ",
    " בע\"ק ": " בעק ",
    " ב\"ח ": " בח ",
    " ב\"ר ": " בר ",
    " בש\"ו ": " בשו ",
    " בש\"ה ": " בשה ",
    " בש\"ז ": " בשז ",
    " בש\"ח ": " בשח ",
    " בש\"ת ": " בשת ",
    " ג\"מ ": " גמ ",
    " ג\"ז ": " גז ",
    " ד\"ר ": " דר ",
    " ה\"מ ": " המ ",
    " ה\"ד ": " הד ",
    " ה\"ע ": " הע ",
    " ה\"ש ": " הש ",
    " ח\"א ": " חא ",
    " ח\"י ": " חי ",
    " ח\"מ ": " חמ ",
    " ח\"ר ": " חר ",
    " ח\"ן ": " חנ ",
    " ט\"ע ": " טע ",
    " כ\"ל ": " כל ",
    " מ\"ב ": " מב ",
    " מ\"ע ": " מע ",
    " מ\"נ ": " מנ ",
    " מ\"ח ": " מח ",
    " מ\"י ": " מי ",
    " מ\"ר ": " מר ",
    " מ\"פ ": " מפ ",
    " מ\"ק ": " מק ",
    " מ\"ש ": " מש ",
    " מ\"ת ": " מת ",
    " נ\"א ": " נא ",
    " ס\"ב ": " סב ",
    " ס\"מ ": " סמ ",
    " ע\"ד ": " עד ",
    " ע\"צ ": " עצ ",
    " ע\"ז ": " עז ",
    " ע\"ח ": " עח ",
    " ע\"כ ": " עק ",
    " פ\"ר ": " פר ",
    " פ\"ש ": " פש ",
    " צ\"ב ": " צב ",
    " צ\"ח ": " צח ",
    " צ\"מ ": " צמ ",
    " צ\"נ ": " צנ ",
    " צ\"פ ": " צפ ",
    " ק\"ג ": " קג ",
    " ר\"ח ": " רח ",
    " ר\"ע ": " רע ",
    " ר\"ת ": " רת ",
    " ש\"ח ": " שח ",
    " ש\"ט ": " שט ",
    " ש\"ר ": " שר ",
    " ת\"ב ": " תב ",
    " ת\"ו ": " תו ",
    " ת\"ל ": " תל ",
    " ת\"ר ": " תר ",
    " ת\"ש ": " תש "
}

DEFAULT_TARGET_WORDS = [ 'פסק-דין','פסק-', 'פסק דינו', 'נפסק', 'החלטה', 'פסק דין', 'פסק', 'פסק-ד', 'פ ס ק ד י ן', 'פ ס ק - ד י ן']

WORD_TO_SEARCH = ["ניתן", "ניתנה"] 
LAWYER_PREFIXES = ["עורך דין", "היועץ"]

# chat_and_query_utils constants
STUDY_BOOK = {
    'labor': 'דיני עבודה',
    'Property_law': 'דיני קניין',
    'family': 'דיני משפחה',
    'contract': 'דיני חוזים',
    'torts': 'דיני נזיקין',
    'private_international': 'משפט בינלאומי פרטי',
    'constitutional': 'משפט חוקתי',
    'administrative': 'משפט מנהלי',
    'corporate_law': 'דיני תאגידים',
    'evidence': 'דיני ראיות',
    'civil_procedure': 'סדר דין אזרחי',
    'criminal_procedure': 'סדר דין פלילי', 
    'hotzlap': 'דיני הוצאה לפועל',
    'criminal_law': 'דיני עונשין',
    'ivri': 'משפט עברי'
}

# completion constants
CLAUDE_ROLES = {'user': "user", 'system': 'system', 'assistant': 'assistant'}

# pinecone_utils constants
UPPER_SORT_ORDER = 7
LOWER_SORT_ORDER = 10
FILTERS_CONDITION_MAP = {
        ConditionEnum.eq: "$eq",
        ConditionEnum.neq: "$ne",
        ConditionEnum.in_: "$in",
        ConditionEnum.gt: "$gt",
        ConditionEnum.lt: "$lt",
        ConditionEnum.gte: "$gte",
        ConditionEnum.lte: "$lte"
    }

 # This dict maps the ColumnsEnum values to the corresponding columns in pinecone
FILTERS_COLUMNS_MAP = {
    "title": "title",
    "txt_id": "txt_id",

    "judges": "judges", # irrelevant for laws
    "date": "show_date", # irrelevant for laws
    "court": "court_name",  # irrelevant for laws
    "procedures": "procedures",
    "location": "location",
    "procedure_type": "procedure_type",
    "pages": "pages",
    "decision_name": "decision_name", 
    #"side_names": "side_names", # irrelevant for laws
    "type": "type",
    "ref_count": "ref_count", # irrelevant for laws, type int
    "procedure_numbers": "procedure_numbers",
    "prosecutors": "prosecutors",
    "defenders": "defenders",
    "representatives": "representatives",
    "legal_field": "legal_field_book" , #filter for Ask the Court (list of 14  books)
    "tribunal_id":"tribunal_id",
} 

LAW_FILTERS = ['title', 'txt_id']
VERDICT_FILTERS = ['judges','show_date','court_name','procedures', 'location', 'procedure_type', 'pages', 'decision_name', 'type', 'ref_count','procedure_numbers', 'prosecutors', 'defenders', 'representatives','tribunal_id','book_id','legal_field_id']

# sql_utils constants
HEBREW_KEYS = ['fullText', 'textOnly', 'text']

# etl_utils\query constants
EXPRESSIONS = ["צוואה הדדית", "פיצול סעדים", "מזונות משקמים", "כשרות משפטית", "חוזה אחיד", "כליאת שווא", "מטרד לציבור", "הפרת חוזה", "זיקת הנאה", "פירוק שיתוף", "עסקה נוגדת", "תקנת השוק", "הסמכות הנמשכת"]


PROCEDURE_LIST_TO_ERROR_MESSAGE = ''

MIN_QUERY_LEN_WITH_VALID_AND_NOT_VALID_PN_IN_CHAT = 6 
MIN_QUERY_LEN_WITH_VALID_PT_AND_PN_IN_CHAT = MIN_QUERY_LEN_WITH_VALID_AND_NOT_VALID_PN_IN_SEARCH = 4
MIN_QUERY_LEN_WITH_VALID_PT_AND_PN_IN_SEARCH = 8
CHUNKS_TO_CONTEXT = 44

# master chunk
MASTER_CHUNK_MAX_TOTAL_TOKENS = 150000