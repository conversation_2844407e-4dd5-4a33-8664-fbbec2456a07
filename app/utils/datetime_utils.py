from datetime import datetime

from pydantic import field_validator


def timestamp_now_il():
    return datetime.now(pytz.timezone("Asia/Jerusalem"))


def timestamp_now_utc():
    return datetime.now(pytz.utc)


def timestamp_utc_to_jerusalem(timestamp: datetime) -> datetime:
    """
    Convert UTC timestamp to JST (Japan Standard Time).

    :param timestamp: UTC datetime object.
    :return: JST datetime object.
    """
    if timestamp.tzinfo is None:
        timestamp = pytz.utc.localize(timestamp)
    return timestamp.astimezone(pytz.timezone("Asia/Jerusalem"))


from datetime import datetime
import pytz

def normalize_to_utc(dt: datetime) -> datetime:
    if dt.tzinfo is None:
        dt = pytz.timezone("Asia/Jerusalem").localize(dt)
    return dt.astimezone(pytz.utc)





def utc_datetime_field_validator(*fields):
    @field_validator(*fields, mode="before")
    def _validator(cls, v):
        if v is None:
            return v
        if isinstance(v, str):
            v = datetime.fromisoformat(v)
        return normalize_to_utc(v)
    return _validator