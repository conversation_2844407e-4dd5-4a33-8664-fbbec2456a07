import html
import re

from configs import app_config as conf
from middlewares.logging_utils import app_logger as logger
from utils.constants_util import SUFFIX_LETTERS

CASE_NUM_PATTERN = re.compile(r"\b(\d{1,6}(?:-|/|\\)\d{2}(?:-|/|\\\d{2})?)\b")

def _case_num_replacement_function(match):
    # should be \d+-\d+\\d{2} OR \d+\\d{2}
    try:
        first_group = match.group(1)
        if len(match.groups()) > 1:
            second_group = match.group(2)
            third_group = match.group(3)[-2:] if match.group(3) else ''  # extract last 2 characters if exists

            formatted_string = first_group + '-' + second_group  # todo - why not remove this line?
            if third_group:
                return first_group + '-' + second_group + '-' + third_group

            return first_group + '/' + second_group
        else:
            return first_group
    except Exception as e:
        logger.error(f'Failed in _case_num_replacement_function. error: {e}')
        print(f'Failed in _case_num_replacement_function. error: {e}')
        return None
        # raise Exception(str(e) if str(e) in conf.ERROR_KEYS else 'CASE_NUM_REPLACEMENT_ERROR')


def replace_hyphen(text):
    try:
        text = re.sub(r'\s*([/-])\s*', r'\1', text)
        pattern = r'(?<!-)\b(\d{2,8})-(\d{2})\b(?!-)'
        return re.sub(pattern, lambda x: f"{x.group(1)}/{x.group(2)}", text)
    except Exception as e:
        print(f'Failed in replace_hyphen with text: {text}, error: {e}')
        logger.error(f'Failed in replace_hyphen with text: {text}, error: {e}')
        return text
        # raise Exception(str(e) if str(e) in conf.ERROR_KEYS else 'REPLACE_HYPHEN_ERROR')


def normalize_case_nums(text):
    # inner
    try:
        text = remove_rtl_problematic_hyphen_space(text)
        text = replace_hyphen(text)
        match=CASE_NUM_PATTERN.sub(_case_num_replacement_function, text)
        if match:
            return match
        return text
    except Exception as e:
        print(f'Failed in normalize_case_nums with text {text}. error: {e}')
        logger.error(f'Failed in normalize_case_nums with text {text}. error: {e}')
        return text



def normalize_common_abbreviations(text):
    try:
        text = re.sub(r'\b[לשמבכ]?נ׳\b', ' נגד ', text)
        text = re.sub(r'\b[לשמבכ]?נ\b', ' נגד ', text)
        text = re.sub(r'\b[לשמבכ]?עו"ד\b', ' עורך דין ', text)
        text = re.sub(r'\b[לשמבכ]?עוהד\b', ' עורך דין ', text)
        text = re.sub(r'\b[לשמבכ]?כשמפ\b', ' חוק הכשרות המשפטית והאפוטרופסות ', text)
        text = re.sub(r'\b[לשמבכ]?תקסדא\b', ' תקנות סדר הדין האזרחי ', text)
        text = re.sub(r'\b[לשמבכ]?תקדסדפ\b', ' תקנות סדר הדין הפלילי ', text)
        text = re.sub(r'\b[לשמבכ]?חסדפ\b', ' חוק סדר הדין הפלילי ', text)
        text = re.sub(r'\b[לשמבכ]?פלתד\b', ' פיצוי לנפגעי תאונות דרכים ', text)
        text = re.sub(r'\b[לשמבכ]?הוצלפ\b', ' תקנות ההוצאה לפועל ', text)
        text = re.sub(r'\b[לשמבכ]?חדלפ\b', ' חוק חדלות פירעון ', text)
        text = re.sub(r'\b[לשמבכ]?חשבדר\b', ' חוק שיפוט בתי דין רבניים ', text)
        text = re.sub(r'\b[לשמבכ]?פקנז\b', ' פקודת הנזיקין ', text)
        text = re.sub(r'\b[לשמבכ]?חוי\b', ' חוק יסוד ', text)
        text = re.sub(r'\b[לשמבכ]?לשכד\b', ' חוק לשכת עורכי הדין ', text)
        text = re.sub(r'\b[לשמבכ]?פשטר\b', ' פקודת פשיטת הרגל ', text)
        text = re.sub(r'\b[לשמבכ]?המעמ\b', ' חוק מס ערך מוסף ', text)
        text = re.sub(r'\b[לשמבכ]?התוב\b', ' חוק התכנון והבניה ', text)
        text = re.sub(r'\b[לשמבכ]?ביהע\b', ' חוק בית הדין לעבודה ', text)
        text = re.sub(r'\b[לשמבכ]?ביהמש\b', ' בית המשפט ', text)
        text = re.sub(r'\b[לשמבכ]?פסשמ\b', ' פקודת סדרי השלטון והמשפט ', text)
        text = re.sub(r'\b[לשמבכ]?סדא\b', ' סדר דין האזרחי ', text)
        text = re.sub(r'\b[לשמבכ]?סדפ\b', ' סדר דין הפלילי ', text)
        text = re.sub(r'\s+', ' ', text)

        return text
    except Exception as e:
        logger.error(f'Failed in normalize common abbreviations with text: {text}, error: {e}')
        print(f'Failed in normalize common abbreviations with text: {text}, error: {e}')
        return text
        # raise Exception(str(e) if str(e) in conf.ERROR_KEYS else 'NORMALIZE_ERROR')


def remove_rtl_problematic_hyphen_space(text):
    try:
        text = re.sub('(?<=\d) -(?=\d)', '-', text)
        text = re.sub('(?<=\d)- (?=\d)', '-', text)
        return text
    except Exception as e:
        print(f'Failed in remove_rtl_problematic_hyphen_space with text: {text}, error: {e}')


def clean_verdict_text(text: str, remove_punct=False):
    # inner
    text = re.sub(r'\b(|כבוד|כב|שופטת|שופט|השופט|השופטת|בכ|המאשימה|הנאשם|העותרים|המבקש|כח|בא|גב|מר|ידיד|ידידת|המערער)\b', '', text)
    text = normalize_case_nums(text)
    # Handle spaced dates for LTR
    text = re.sub(r'(?<!\d)\s*(\d+)\s*\.\s*(\d+)\s*\.\s*(\d+)\s*(?!\d)', r'\1.\2.\3', text)
    # Handle spaced dates for RTL
    text = re.sub(r'(?<!\d)\s*(\d+)\s*(?=\.)\s*(\d+)\s*(?=\.)\s*(\d+)\s*\.', r'\3.\2.\1.', text)
    text = re.sub(r'(?<=\D)\s*(\d+\.\d+\.\d+)\s*(?=\D)', r' \1 ', text)
    #text = re.sub(r'\(([^0-9]*?)\)', r'\1', text) # remove the parenthesis around number
    text = re.sub(r'\'|"', '', text)  # Remove apostrophes and double quotes
    text = normalize_common_abbreviations(text)
    text = re.sub(r'\*', '', text)  # Replace asterisks
    text = re.sub(r'\s*"|\s*\'', '', text)  # Remove single and double quotes
    text = re.sub(r'_+', ' ', text)  # Replace any pattern of underscores with a single space
    text = re.sub(r'(?<=[a-zA-Z])-(?=[a-zA-Z])', ' ', text)  # Replace '-' with ' ' if '-' is next to text not numbers
    text = re.sub(r'\.( *\.)+', '.', text)  # combine multiple dots
    # text = re.sub(r',', '', text)  # Remove commas

    text = re.sub('\.\.+', '.', text) # replace multiple dots with one dot in all string columns

    text = re.sub(' \[\d+\] ', ' ', text)  # remove references that are e.g. '[613]' in the text

    # To fix dates with intermediate spaces
    # text = text.replace(':', '')
    text = remove_rtl_problematic_hyphen_space(text)

    if remove_punct:
        text = re.sub(r'[^\w\d\s/\-]', '', text)  # Remove all non-word characters except / and - which are used for the special procedure number


    return text


def remove_hebrew_nikkud(text):
    # Unicode range for Hebrew letters is U+0590 to U+05FF
    # Unicode range for Hebrew nikkud is U+0591 to U+05C7
    nikkud_pattern = r'[\u0591-\u05C7]'
    return re.sub(nikkud_pattern, '', text)


def remove_unwanted_chars(text):
    try:
        # out facing
        if not isinstance(text, str):
            return text
        text = remove_rtl_problematic_hyphen_space(text)
        text = remove_hebrew_nikkud(text)
        ###################################################
        ### should be done before we remove special chart ##
       # text = re.sub(r'\[.*?\]', '', text)
        # look in whatsapp what should be changed here (order of operations)
        text = html.unescape(text)  # Decode HTML entities
        text = text.replace('\x0d', ' ')  # Replace carriage returns with a space
        # text = re.sub(r'[^\w\d\s\'./()\[\]_*;,:\-]', '', text)  # Remove all non-word characters except /, ', ., (, )
        #text = re.sub(r'[^\w\d\s\'./\(\)\"\[\]-_\*,;:\-]', '', text)
        text = re.sub(r'[ \t\r\f\v]+', ' ', text)  # Replace multiple spaces (excluding new lines) with one space
        text = re.sub(r'\n+', '\n', text)  # Replace multiple new lines with a single new line
        text = re.sub(r'_x000D_', ' ', text)  # Remove '_x000D_' and replace with a space
        text = re.sub(r'000d', ' ', text)  # Remove '_x000D_' and replace with a space
        text = re.sub(r'00d', ' ', text)  # Remove '_x000D_' and replace with a space
        text = re.sub(r'0d', ' ', text)  # Remove '_x000D_' and replace with a space
        text = re.sub(r'\xa0', ' ', text)  # Remove '_x000D_' and replace with a space
        text = re.sub(r'for_avoda', '', text)  # Remove 'for_avoda' and replace with a space TODO rami - why? >>> the term appears in multiple verdicts
        text = re.sub(r'\u200e|\u200f|\u202a|\u202c|\u202d|\u202e', '', text)  # Remove invisible characters
        text = re.sub(r'&quot;', '"', text)
        text = re.sub(r'&qout;', '"', text)
        text = text.replace('\ufb35', '\u05d5')
        text = text.replace('\ufb2f', '\u05d0')
        text = text.replace('\ufb34', '\ufb23')
        text = text.replace('\ufb3b', '\u05db')
        text = text.replace('\ufb2a', '\u05e9')
        #text = fix_dates_for_rtl_text(text)

        return text.strip().lower()
    except Exception as e:
        logger.error(f'Failed in remove_unwanted_chars with text: {text}, error: {e}')
        raise Exception(str(e) if str(e) in conf.ERROR_KEYS else 'REMOVE_CHARS_ERROR')


def format_procedure_number_string(procedure):
    pattern = re.compile(r'[^\d]*(\d{1,7})[-/](\d{1,7})(?:[-/](\d{1,7})|(?:[-/a-zA-Z]+))?')

    match = pattern.match(procedure)
    if not match:
        print('procedure number not valid')
        logger.error(f'procedure number not valid')
        raise Exception('PROCEDURE_NUMBER_NOT_VALID')

    parts = match.groups()

    if parts[2]:
        return f"{parts[0]}/{parts[2]}"
    else:
        return f"{parts[0]}/{parts[1]}"


def extract_procedure_from_query_text(query: str):

    try:
        query = re.sub(r'"', "", query)
        query = re.sub(
            r'(\d{1,8}[-/]\d{1,8}[-/]?\d{0,2})\s*([א-ת]+)|([א-ת]+)\s*(\d{1,8}[-/]\d{1,8}[-/]?\d{0,2})',
            lambda m: "{} {}".format(
                m.group(2) or m.group(3),
                m.group(1).replace('-', '/', 1) if m.group(1) and m.group(1).count('-') == 1 else m.group(1) or m.group(4)
            ),
            query
        )
        # Search for the formatted procedure type and number
        match = re.search(r'([א-ת״׳"]+)\s*(?:\[.*?\])?\s*(\d{1,8}[-/]\d{1,8}[-/]?\d{0,2})', query)

        return "{} {}".format(match.group(1), match.group(2)) if match else None
    except Exception as e:
        logger.error(f'Failed in extract_procedure_from_query_text with query: {query}, error: {e}')
        raise Exception(str(e) if str(e) in conf.ERROR_KEYS else 'EXTRACT_PROCEDURE_ERROR')



def clean_text_aggressive(text):
    # inner
    try:
        temp_text = remove_unwanted_chars(text)
        try:
            temp_text = clean_verdict_text(temp_text)
        except Exception as e:
            logger.error(f'Failed in clean_verdict_text with text: {temp_text}, error: {e}')
        return temp_text
    except Exception as e:
        logger.error(f'Failed in clean_text_aggressive with text: {text}, error: {e}')
        return text


def remove_pt_suffix(pt):
    modified_pt = None

    original_pt = pt.replace('"', '')

    # Check if the string is not empty and the first letter is in the specified letters
    if pt[0] in SUFFIX_LETTERS:
        if original_pt[1:] in conf.PROCEDURE_LIST:
            modified_pt = original_pt[1:]
            return original_pt, modified_pt

    original_pt = original_pt if original_pt in conf.PROCEDURE_LIST else None
    return original_pt, modified_pt


if __name__ == '__main__':
    text=" רעפ 7892/04"
    print(extract_procedure_from_query_text(text))