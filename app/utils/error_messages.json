{"GET_FULL_TEXT_ERROR": {"description": "Failed to get full text and text only for txt_id: {txt_id}", "value": ".תוכן המסמך שחיפשת איננו זמין"}, "RESULTS_NONE_MESSAGE": {"description": "Failed to get search results for query", "value": "לא נמצאו מסמכים התואמים את החיפוש שלך."}, "PREPARE_CHAT_ERROR": {"description": "Failed to prepare chat for chat_id: {chat_id}", "value": "חלה שגיאה תוך כדי חשיבה על התשובה :) אנא רעננו את העמוד ונסו לשאול שוב"}, "MISSIND_REDIS_ERROR": {"description": "Missing required data in redis -  context none in redis", "value": "אופס... איבד<PERSON>ו אורינטאציה. אנא רעננו את העמוד ונסו שוב"}, "CHAT_CLAUDE_ERROR": {"description": "Failed to chat for chat_id: {chat_id}", "value": "מממ...אנחנו מתקשים כרגע לספק תשובות. נסו שוב בעוד דקותיים"}, "REL_REF_ERROR": {"description": "Failed to get related and refferer for chat_id: {chat_id}", "value": "לא הצלחנו להציג מידע משלים"}, "NOT_EXIST_IN_REDIS": {"description": "existing value for chat_id: {chat_id} was not found in REDIS so cannot handle the request", "value": "יתכן ועבר זמן רב מידי מאז שאלתך הקודמת. אנא התחילו צ'ט חדש"}, "VERDIC_TITLE_ERROR": {"description": "Error_in_adjust_verdict_chunk_title_function with error", "value": "לא נמצאה כותרת למסמך"}, "UPDATE_VERDICT_ERROR": {"description": "Error in update_verdicts_in_search_results function", "value": "שגיאה בעיד<PERSON>ון סדר הופעת המסמכים"}, "REL_REF_METADATA_ERROR": {"description": "Error in get_related_and_refferer_metadata function with related_and_refferer_ids", "value": "שגיאה בהבאת נתוני ליבה של מידע משלים"}, "FIRST_CHUNK_METADATA_ERROR": {"description": "Error in get only first chunk metadata by txt id function with error", "value": "{txt_id} שגיאה בהבאת נתוני ליבה עבור מסמך "}, "TOKEN_TO_REDIS_ERROR": {"description": "Error in add tokens to redis with chat_id {chat_id}", "value": "אופס...גם לנו מתרחשות לעיתים תקלות בניהול משאבים"}, "CONVERSATION_TO_REDIS_ERROR": {"description": "Error in add conversation to_redis with chat_id {chat_id}", "value": "אופס...גם לנו מתרחשות לעיתים תקלות בניהול משאבים"}, "PREPARE_REQUEST_PRPS_ERROR": {"description": "Error in prepare request prps", "value": "אופס...גם לנו מתרחשות לעיתים תקלות בניהול משאבים"}, "PROCEDURE_IN_QUERY_ERROR": {"description": "Failed to check for procedure in query", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "EXTRACT_ANSWER_AND_SUMMARY_FROM_CLAUDE_ERROR": {"description": "Failed to extract data from claude answer", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "RELATED_CHUNKS_ERROR": {"description": "Failed to get related chunks for chat_id: {chat_id}", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "CHAT_REQUEST_TOKENS_ERROR": {"description": "Failed to count chat request tokens with chat_id: {chat_id}", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "CONVERT_RESULTS_ERROR": {"description": "Failed to convert search results to list items", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "LOG_SEARCH_STATISTICS_ERROR": {"description": "Failed in log search statistics", "value": "שגיאת מערכת"}, "CONTEXT_WITHOUT_TXT_ID_ERROR": {"description": "Failed to generate context without txt_id for chat_id: {chat_id}", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "CLAUDE_QUERY_PROMPT_ERROR": {"description": "Error in prep claude query prompt", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "CLAUDE_QUESTION_COMPLETION_ERROR": {"description": "Error in claude question completion", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "FORMAT_MESSAGE_ERROR": {"description": "Error in format message", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "ASYNC_STREAM_CLAUDE_COMPLETION_ERROR": {"description": "Error in async claude completion", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "STREAM_CLAUDE_COMPLETION_ERROR": {"description": "Error in stream claude completion", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "EMBED_TEXTS_ERROR": {"description": "Failed in embed texts with texts", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "SPARSE_EMBEDDING_ERROR": {"description": "Failed in get sparse embedding with metadata_or_query", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "EXTRACT_TEXT_EMBEDDING_ERROR": {"description": "Failed to extract context embedding", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "CHECK_HEBREW_NER_ERROR": {"description": "Failed in check hebrew ner with query", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "SORT_ITEMS_ERROR": {"description": "Failed in sort items", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "QUERY_PINRCONE_ERROR": {"description": "Failed in query pinecone", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "MERGE_SORT_RESULTS_ERROR": {"description": "Failed in merge results", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "FILTERS_BY_INDEX_ERROR": {"description": "Failed get filters by index with filters", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "COMBINED_TOP_K_ERROR": {"description": "Failed in combined top k search", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "CONVERT_PINECONE_FILTERS_ERROR": {"description": "Failed in convert filters to pinecone metadata", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "PARSE_PINECONE_FILTERS_ERROR": {"description": "Failed in parse pinecone filters", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "STORE_IN_VEDCTOR_DB_ERROR": {"description": "Failed in store in vector db", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "CLEAR_PINECONE_INDEX_ERROR": {"description": "Failed in clear pinecone index", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "GET_TOKENIZER_ERROR": {"description": "Failed in get_tokenizer with COHERE_HF_TOKENIZER_NAME", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "CASE_NUM_REPLACEMENT_ERROR": {"description": "Failed in case num replacement function", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "NORMALIZE_ERROR": {"description": "Failed in normalize common abbreviations with text", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "REMOVE_CHARS_ERROR": {"description": "Failed in remove unwanted chars with text", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "CLEAN_SIDE_NAMES_ERROR": {"description": "Failed in clean side names with side_names", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "NORMALISE_NAMES_ERROR": {"description": "Failed in normalize names with data", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "EXTRACT_SIDE_NAMES_ERROR": {"description": "Failed in extract side names with data", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "EXTRACT_LAWYER_NAMES_ERROR": {"description": "Failed in extract lawyer names with data", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "CLEAN_LAW_TEXT_ERRORt": {"description": "Failed in clean law text with tex", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "FIX_DATE_FOR_RTL_ERROR": {"description": "Failed in fix dates for rtl text with text", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "CLEAN_VERDICT_TEXT_ERROR": {"description": "Failed in clean verdict text with text", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "EXTRACT_PROCEDURE_ERROR": {"description": "Failed in extract procedure from query text with query", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "SPLIT_LAW_REXT_ERROR": {"description": "Failed in split law text with txt_id: {txt_id}", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "ETASE_BEFORE_WORD_ERROTR": {"description": "Failed in erase before word with text", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "TRIM_END_OF_VERDICT_ERROR": {"description": "Failed in trim end of verdict with verdict_text", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "SPLIT_LONG_STRING_ERROR": {"description": "Failed in split long string", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "SPLIT_TEXT_INTO_CHUNKS_ERROR": {"description": "Failed in split text into chunks with text", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "N_TOKENS_ERROR": {"description": "Failed in n_tokens with texts", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "PREPARE_METADATA_ERROR": {"description": "Failed in prepare metadata with txt_id {txt_id}", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "PREP_LINE_LAW_METADATA_ERROR": {"description": "Failed in prep line from law metadata", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "PREP_LINE_VERDICT_METADATA_ERROR": {"description": "Failed in prep line from verdict metadata", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "CLEAN_SINGLE_NAME_ERROR": {"description": "Failed in clean single name with name", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "PARSE_NAMES_LIST_ERROR": {"description": "Failed in parse names list with names", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "EXTRACT_ANSWER_AND_SUMMARY_ERROR": {"description": "Failed in extract answer and summary with text", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "EXTRACT_CHUNK_ERROR": {"description": "Failed in extract chunks with text", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "QUOTATIONS_MARKS_TO_PROCEDURE_ERROR": {"description": "Failed in adding quotations marks to procedure with text", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "SET_WITH_TTL_ERROR": {"description": "Error setting key {key} in Redis", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "EXTRACT_FROM_REDIS_ERROR": {"description": "Failed to extract chat values from redis for chat_id: {chat_id}", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "SAVE_IN_REDIS_ERROR": {"description": "Failed to convert data to json for chat_id: {chat_id}", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "TEXT_ONLY_AND_FULL_TEXT_ERROR": {"description": "Error in get text only and full text function", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "GET_CHUNK_TEXT_ERROR": {"description": "Error in get chunk texts function with error", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "GET_CHUNK_METADATA_ROWS_ERROR": {"description": "Error in get chunk metadata rows function with error", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "FIRST_AND_LAST_CIDS_ERROR": {"description": "Error in get three first and last cids text function with error", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "GET_MAAGAR_ID_AND_TOTAL_TOKENS_ERROR": {"description": "Error in get maagar id and total tokens for txt id function", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "HTML_AND_TEXT_ONLY_CONTENT_ERROR": {"description": "Error in get html and full text content - {INFO}", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "BM25_ENCODER_ERROR": {"description": "Failed in get bm25 encoder", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "REFACTOR_REDIS_VALUE_ERROR": {"description": "Failed to refactor redis value", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "PROCEDURE_NOT_EXIST": {"description": "Error in convert procedure to txt_id", "value": "לא נמצא מסמך התואם את מספר ההליך {INFO} שהקלדת.\n אנא ודא שהקלדת את מספר ההליך נכון.\n באפשרותך להשתמש בדף החיפוש כדי לאתר אותו"}, "PROCEDURE_NOT_EXIST_IN_SEARCH": {"description": "Error in convert procedure to txt_id", "value": "לא נמצא מסמך התואם את מספר ההליך {INFO} שהקלדת.\n אנא ודא שהקלדת את מספר ההליך נכון"}, "SEARCH_ERROR": {"description": "Failed in search", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "PROCEDURE_NUMBER_NOT_VALID": {"description": "procedure number not valid", "value": "מספר ההליך שהכנסת שגוי"}, "PROCEDURE_LIST_TO_ERROR_MESSAGE_ERROR": {"description": "fount many procedure numbers", "value": "במערכת קיימים מספר הליכים שיכולים להתאים לשאלתך {INFO}\n אנא עדכנו שאלתכם בהתאם."}, "GET_WITH_RETRY_ERROR": {"description": "falied in get with retry", "value": "שגיאת מערכת. נא לרענן את העמוד ולנסות שנית"}, "PROCEDURE_NUMBER_IS_NOT_ETHICS": {"description": "procedure number is not ethics", "value": "מספר ההליך שהקלדת אינו שייך למאגר האתיקה"}}