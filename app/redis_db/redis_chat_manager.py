from typing import <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>

from utils.pre_chat_dto import PreChat<PERSON>anager, Chat<PERSON><PERSON>
from redis_db.redis_schema import RedisSchema
from middlewares.logging_utils import app_logger as logger


async def save_pre_chat_to_redis(pre_chat_object: PreChatManager):
    """Save data to Redis"""
    try:
        if not pre_chat_object.data.data_raw and not pre_chat_object.data.full_text_content and pre_chat_object.chat_settings.chat_type != ChatEnum.trainer:
            raise ValueError("No data to save")
        # Get Redis instance from connection_source
        redis_pool = pre_chat_object.connection_source.redis_instance

        # Create a RedisChatManager instance
        redis_manager = RedisChatManager(redis_pool)

        # Create RedisSchema from PreChatManager
        redis_schema = await redis_manager.create_chat_from_pre_chat_manager(pre_chat_object)

        # Save to Redis
        success = await redis_manager.save_chat(redis_schema)

        if success:
            logger.info(f'Successfully saved chat data to Redis for chat_id: {pre_chat_object.chatId}')
        else:
            logger.error(f'Failed to save chat data to Redis for chat_id: {pre_chat_object.chatId}')
            raise ValueError("SAVE_IN_REDIS_ERROR")


    except Exception as e:
        logger.error(f'Error saving data to Redis: {e}, chat_id: {pre_chat_object.chatId}')
        raise ValueError("SAVE_IN_REDIS_ERROR")

class RedisChatManager:
    """
    Manages chat data in Redis, including loading, saving, and converting between
    RedisSchema and PreChatManager objects.
    """

    # Constant for TTL (24 hours in seconds)
    CHAT_CACHE_TTL = 24 * 60 * 60  # 24 hours

    def __init__(self, redis_pool):
        self.redis = redis_pool
        # self.mongo_client = None  # Will be initialized when needed

    @staticmethod
    def create_new_chat_id() -> str:
        """Create a new UUID4 for a chat"""
        import uuid
        return str(uuid.uuid4())

    async def exists_in_redis(self, key: str) -> bool:
        """Check if a key exists in Redis"""
        try:
            return await self.redis.exists(key) > 0
        except Exception as e:
            logger.error(f"Error checking if key {key} exists in Redis: {e}")
            return False

    async def load_from_redis(self, key: str) -> Union[None, RedisSchema]:
        """Load data from Redis and deserialize it to RedisSchema"""
        try:
            data = await self.redis.get(key)
            if not data:
                return None

            # Deserialize the JSON data to RedisSchema
            return RedisSchema.model_validate_json(data)
        except Exception as e:
            logger.error(f"Error loading data from Redis for key {key}: {e}")
            return None

    async def load_chat(self, chat_id: Optional[str], user_id: str, domain: str) -> Tuple[str, Optional[RedisSchema]]:
        """
        Load a chat from Redis or create a new one

        Args:
            chat_id: The chat ID to load, or None to create a new chat
            user_id: The user ID
            domain: The domain (verdict, law, machshavot)

        Returns:
            Tuple of (chat_id, RedisSchema or None)
        """
        # Case 1: New chat (chat_id is None)
        if not chat_id:
            new_chat_id = self.create_new_chat_id()
            return new_chat_id, None

        # Case 2: Existing chat in Redis
        if await self.exists_in_redis(chat_id):
            chat_data = await self.load_from_redis(chat_id)
            if chat_data:
                return chat_id, chat_data

        # Case 3: Not in Redis, try MongoDB (placeholder for now)
        # In the future, implement MongoDB loading here
        logger.warning(f"Chat {chat_id} not found in Redis, would check MongoDB in the future")

        # For now, treat it as a new chat with the provided chat_id
        return chat_id, None

    async def save_chat(self, data: RedisSchema) -> bool:
        """Save chat data to Redis
        Args:
            data: The RedisSchema object to save
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Use the chat_id as the key
            key = data.chat_id

            serialized_data = data.model_dump_json()
            # compressed = gzip.compress(serialized_data.encode('utf-8'))

            # Store the serialized data in Redis with TTL
            success = await self.redis.set(key, serialized_data, ex=self.CHAT_CACHE_TTL)

            return success
        except Exception as e:
            logger.error(f"Error saving data to Redis for chat_id {data.chat_id}: {e}")
            return False

    # async def update_redis_schema(self,existing: RedisSchema, **updates) -> RedisSchema:
    #     data = existing.dict()
    #     data.update(updates)
    #     return RedisSchema(**data)
    async def create_chat_from_pre_chat_manager(self, pre_chat_manager: PreChatManager) -> RedisSchema:
        """
        Create a RedisSchema instance from a PreChatManager object

        Args:
            pre_chat_manager: The PreChatManager object

        Returns:
            RedisSchema: The created RedisSchema object
        """
        # Handle query extraction safely
        if hasattr(pre_chat_manager, 'query'):
            if hasattr(pre_chat_manager.query, 'raw_query'):
                query = pre_chat_manager.query.raw_query
            else:
                query = pre_chat_manager.query
        else:
            query = None

        # Handle txt_id extraction safely

        # Handle data_context extraction safely
        data_context = pre_chat_manager.data.model_dump()

        conversation = pre_chat_manager.conversation
        # Handle model_settings extraction safely
        model_settings = pre_chat_manager.model_settings.model_dump()

        # Extract data from pre_chat_manager
        chat_data = RedisSchema(
            user_id=pre_chat_manager.userId,
            chat_id=pre_chat_manager.chatId,
            domain=pre_chat_manager.domain,
            query=query,
            conversation=conversation,
            summaries=pre_chat_manager.summaries,
            data=data_context,
            model_settings=model_settings,
            chat_settings=pre_chat_manager.chat_settings,
            filters=pre_chat_manager.filters,
            # Generate a title based on the query
        )

        return chat_data
