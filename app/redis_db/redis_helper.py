from redis_db.redis_schema import RedisSchema
from utils.pre_chat_dto import PreChatManager, TypeEnum, PreChatRequest
from utils.conversation_manager import ConversationEnum


async def convert_to_pre_chat_manager(redis_schema: RedisSchema, user_request: PreChatRequest,
                                      connection_source=None) -> PreChatManager:
    """
    Convert a RedisSchema to a PreChatManager object

    Args:
        redis_schema: The RedisSchema object
        connection_source: The connection source for the PreChatManager
        user_request: The user request object

    Returns:
        PreChatManager: The created PreChatManager object
    """
    from utils.pre_chat_dto import PreChatManager, Query

    try:
        # Create the Query object - handle None query
        query = Query(raw_query=user_request.query or "", clean_query=None, embed_query=[])

        domain = TypeEnum.from_value(redis_schema.domain)

        # Create and return the PreChatManager object
        return PreChatManager(
            connection_source=connection_source,
            chatId=redis_schema.chat_id,
            userId=redis_schema.user_id,
            query=query,
            domain=domain,
            summaries=redis_schema.summaries,
            conversation=redis_schema.conversation,
            model_settings=redis_schema.model_settings,
            chat_settings=redis_schema.chat_settings,
            filters=user_request.filters,
        )
    except Exception as e:
        from middlewares.logging_utils import app_logger as logger
        logger.error(f"Error converting RedisSchema to PreChatManager: {e}")

        # Create a minimal PreChatManager with required fields
        return PreChatManager(
            connection_source=connection_source,
            chatId=redis_schema.chat_id,
            userId=redis_schema.user_id,
            query=Query(raw_query=redis_schema.query or "", clean_query=None, embed_query=[]),
            domain=TypeEnum.from_value(redis_schema.domain),
        )


def add_to_conversation(conversation, role, content, action, prompt=None, entry_type="answer"):
    """
    Adds a validated entry to the conversation list with a timestamp.

    :param conversation: List to store conversation entries.
    :param role: Role of the participant (e.g., "assistant" or "user").
    :param content: The content of the message.
    :param action: Action type (e.g., "both", "user", "assistant").
    :param prompt: Optional prompt associated with the entry.
    :param entry_type: Type of the entry (default is "answer").
    :return: None
    :raises ValueError: If required fields are invalid.
    """
    # Validate required fields
    if not role or role not in ["assistant", "user"]:
        raise ValueError("Invalid 'role'. Must be 'assistant' or 'user'.")
    if not content or not isinstance(content, str):
        raise ValueError("Invalid 'content'. Must be a non-empty string.")
    if action not in ConversationEnum._value2member_map_:
        raise ValueError(f"Invalid 'action'. Must be one of {list(ConversationEnum._value2member_map_.keys())}.")
    from datetime import datetime
    import pytz

    # Define the Israel timezone
    formatted_time = datetime.now(pytz.timezone("Asia/Jerusalem")).strftime("%H:%M %d/%m/%Y")

    # Append the validated entry with a timestamp
    entry = {
        "role": role,
        "content": content,
        "prompt": prompt,
        "action": action,
        "type": entry_type,
        "timestamp": formatted_time
    }
    conversation.append(entry)


def generate_title_from_query(query: str) -> str:
    """
    Generate a title from the query

    Args:
        query: The query string

    Returns:
        str: The generated title
    """
    # Simple implementation: take the first 30 characters of the query
    if not query:
        return "New Chat"

    # Truncate to 30 chars and add ellipsis if needed
    title = query.strip()[:30]
    if len(query) > 30:
        title += "..."

    return title


