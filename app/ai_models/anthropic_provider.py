import os
from abc import abstractmethod, <PERSON>
from typing import Optional, List, Dict
from middlewares.logging_utils import app_logger as logger
from anthropic import AsyncAnthropic
from anthropic.types import RawContentBlockDeltaEvent, TextDelta, CitationsDelta, MessageStopEvent
from anthropic.types.beta.message_create_params import MessageCreateParamsNonStreaming
from anthropic.types.beta.messages.batch_create_params import Request
from dotenv import load_dotenv
from pydantic import BaseModel, <PERSON>
from typing import Any

from utils.pre_chat_dto import TypeEnum
from configs.app_config import CHAT_COMPLETION_MACHSHAVOT, CLAUDE_ANSWER_TOP_P, LAW_QUESTION_MODEL, \
    MASTER_CHUNK_MAX_ANSWER_TOKENS, \
    MASTER_CHUNK_CLAUDE_ANSWER_TEMPERATURE, CLAUDE_ANSWER_TEMPERATURE, \
    CHAT_COMPLETION_RATIO_MODEL, ANTHROPIC_MASTER_CHUNK_MODEL, MA<PERSON>_ANSWER_TOKENS, \
    CHAT_COMPLETION_STANDARD_MODEL, CHAT_COMPLETION_PREMIUM_MODEL, NER_MODEL
from .prompts import *
from .provider import AIProvider

load_dotenv()


class TextObject(BaseModel):
    text: str
    type: str = "text"


class CitationObject(BaseModel):
    type: str = "chunk"
    cid: Optional[str] = Field(default="")
    chunk_index: Optional[int] = Field(default=0)
    document_index: Optional[int] = Field(default=0)
    document_title: Optional[str] = Field(default="")
    offset_start: int = 0
    offset_end: int = 0
    citation_number: int = 0
    source: Optional[str] = Field(default="")
    section_numbers: Optional[Any] = Field(default="")
    decision_name: Optional[str] = Field(default="")
    provision_title: Optional[str] = Field(default="")
    title: Optional[str] = Field(default="")
    chapter_title: Optional[str] = Field(default="")
    sub_chapter_title: Optional[str] = Field(default="")
    txt_id: Optional[int] = Field(default=0)
    subject: Optional[str] = Field(default="")
    judges: Optional[List[str]] = Field(default_factory=list)
    page_number: Optional[int] = Field(default=0)
    pages: Optional[int] = Field(default=0)
    cited_text: Optional[str] = Field(default="")
    book_id: Optional[str] = Field(default="")
    sub_chapter_id: Optional[str] = Field(default="")
    book_name: Optional[str] = Field(default="")
    content: Optional[str] = Field(default="")
    show_date: Optional[str] = Field(default="")
    court_name: Optional[str] = Field(default="")
    location: Optional[str] = Field(default="")
    ref_count: Optional[int] = Field(default=0)
    book_type: Optional[str] = Field(default="")
    score: Optional[float] = Field(default=0.0)
    master_chunk: Optional[bool] = Field(default=False)


class StopEvent(BaseModel):
    input_tokens: int
    output_tokens: int
    cache_read_input_tokens: int
    model_version: str


def process_citation_delta_law_machshavot(event: Any, mapper_txtid: dict, citation_counter: int,
                                          search_results: Any, master_chunks: Any) -> CitationObject | None:
    """
    Process citation delta for laws and machshavot and specific verdict (full document)
    Args:
        event: Event object containing citation information
        mapper_txtid: Dictionary mapping document index to txt_id and cid
        citation_counter: Counter for citation numbers
        search_results: List of search results to include as documents
        master_chunks: Dictionary of master chunks
    """
    txt_id = None
    cid = None
    try:
        citation = event.delta.citation
        document_index = citation.document_index
        mapper_data = mapper_txtid.get(document_index, {})
        txt_id = mapper_data.get("txt_id")
        cid = mapper_data.get("cid")
        chunk_data = next((x for x in search_results if x.get("id") == cid), None)

        cited_text_clean = citation.cited_text.replace("\n", " ").replace("\r", " ")
        cited_text = f'''[-{citation_counter}-] - {cited_text_clean}'''
        master_chunk = mapper_data.get("master_chunk", False)
        mapper_data["cited"] = True

        if master_chunk:
            chunk_data = next((x for x in search_results if x.get("txt_id") == txt_id), None)
            content = str(master_chunks[str(txt_id)])
        else:
            content = chunk_data.get("content", "")

        if chunk_data is None:
            raise ValueError(f"Chunk not found for document index: {document_index}")

        citation_obj = CitationObject(
            cid=cid,
            chunk_index=chunk_data.get("chunk_index", 0),
            document_index=document_index,
            document_title=getattr(citation, "document_title", ""),
            offset_start=citation.start_char_index,
            offset_end=citation.end_char_index,
            citation_number=citation_counter,
            source=chunk_data.get("source", ""),
            section_numbers=chunk_data.get("section_numbers", ""),
            provision_title=chunk_data.get("provision_title", ""),
            title=chunk_data.get("title", ""),
            chapter_title=chunk_data.get("chapter_title", ""),
            sub_chapter_title=chunk_data.get("sub_chapter_title", ""),
            txt_id=chunk_data.get("txt_id", 0),
            subject=chunk_data.get("subject", ""),
            judges=chunk_data.get("judges", []),
            page_number=chunk_data.get("page_number", 0),
            cited_text=cited_text or "",
            book_id=chunk_data.get("book_id", ""),
            sub_chapter_id=chunk_data.get("sub_chapter_id", ""),
            book_name=chunk_data.get("book_name", ""),
            content=content,
            decision_name=chunk_data.get("decision_name", ""),
            show_date=chunk_data.get("show_date", ""),
            court_name=chunk_data.get("court_name", ""),
            location=chunk_data.get("location", ""),
            ref_count=chunk_data.get("ref_count", 0),
            pages=int(chunk_data.get("pages", 0)),
            book_type=chunk_data.get("book_type", ""),
            score=chunk_data.get("score", 0.0),
            type=chunk_data.get("type", "chunk"),
            master_chunk=master_chunk
        )

        return citation_obj

    except Exception as e:
        logger.warning(
            f"Error in process_citation_delta_law_machshavot for laws and machshavot and spesfic verdict (full document)  : {str(e)}, txt_id: {txt_id},cid: {cid}, citation_counter: {citation_counter}")

        return None


def process_citation_delta_with_offset(event: Any, mapper_txtid: dict, citation_counter: int,
                                       search_results: Any, master_chunks: Any) -> CitationObject | None:
    """
    Process citation delta with offset for verdcit only when the citation is not in the full document and get chunks document

    """
    txt_id = None
    cid = None
    try:
        citation = event.delta.citation
        document_index = citation.document_index
        cited_offset_start = citation.start_char_index
        cited_offset_end = citation.end_char_index

        mapper_data = mapper_txtid.get(document_index, {})
        txt_id = mapper_data.get("txt_id")
        cid_offsets = mapper_data.get("cid_offsets", [])
        master_chunk = mapper_data.get("master_chunk", False)
        mapper_data["cited"] = True

        if master_chunk:
            chunk_data = next((x for x in search_results if x.get("txt_id") == txt_id), None)
            content = str(master_chunks.get(str(txt_id), ""))
        else:
            if not cid_offsets:
                raise ValueError(f"No cid_offsets found for document index {document_index}")

            matched_cid_data = next(
                (item for item in cid_offsets if item["start_offset"] <= cited_offset_start < item["end_offset"]),
                None
            )

            if not matched_cid_data:
                logger.error(
                    f"No matching cid found for offset {cited_offset_start} in document index {document_index}")
                return None

            cid = matched_cid_data["cid"]
            matched_cid_data["cited"] = True

            chunk_data = next((x for x in search_results if x.get("id") == cid), None)
            if chunk_data is None:
                logger.error(f"Chunk not found for cid: {cid}")
                return None

            content = chunk_data.get("content", "")

        cited_text_clean = citation.cited_text.replace("\n", " ").replace("\r", " ").replace("||", " ")
        cited_text = f'''[-{citation_counter}-] - {cited_text_clean}'''

        citation_obj = CitationObject(
            cid=cid,
            chunk_index=chunk_data.get("chunk_index", 0),
            document_index=document_index,
            document_title=getattr(citation, "document_title", ""),
            offset_start=cited_offset_start,
            offset_end=cited_offset_end,
            citation_number=citation_counter,
            source=chunk_data.get("source", ""),
            section_numbers=str(chunk_data.get("section_numbers", "")),
            provision_title=chunk_data.get("provision_title", ""),
            title=chunk_data.get("title", ""),
            chapter_title=chunk_data.get("chapter_title", " "),
            sub_chapter_title=chunk_data.get("sub_chapter_title", ""),
            txt_id=chunk_data.get("txt_id", 0),
            subject=chunk_data.get("subject", ""),
            judges=chunk_data.get("judges", []),
            page_number=chunk_data.get("page_number", 0),
            cited_text=cited_text or "",
            book_id=chunk_data.get("book_id", ""),
            book_name=chunk_data.get("book_name", ""),
            content=content,
            decision_name=chunk_data.get("decision_name", ""),
            show_date=chunk_data.get("show_date", ""),
            court_name=chunk_data.get("court_name", ""),
            location=chunk_data.get("location", ""),
            ref_count=chunk_data.get("ref_count", 0),
            pages=int(chunk_data.get("pages", 0)),
            book_type=chunk_data.get("book_type", ""),
            score=chunk_data.get("score", 0.0),
            master_chunk=master_chunk,
            type=chunk_data.get("type", "chunk"),
        )

        return citation_obj

    except Exception as e:
        logger.warning(
            f"Error inprocess_citation_delta_with_offset for verdcit only  : {str(e)}, txt_id: {txt_id},cid: {cid}, citation_counter: {citation_counter}")

        return None


class AnthropicProvider(AIProvider, ABC):
    def __init__(self, api_key: str, model: str, max_tokens: int, temperature: float, top_p: float):
        self.client = Anthropic(api_key=api_key, timeout=60)
        self.model = model
        self.max_tokens = max_tokens
        self.temperature = temperature
        self.top_p = top_p

        if self.client is None:
            raise RuntimeError('Anthropic {} client not found'.format(self.__class__.__name__))
        # Initialize the client and configuration constants for this provider

    @abstractmethod
    def generate_message(self, text: str):
        pass

    async def stream_generate_message(self, conversation: list, model: str, mapper_cid_id: dict,
                                      citation_number: int = 0):
        pass

    def parse_message(self, message: str):
        pass

    def tokenize(self, text: str) -> int:
        res = self.client.messages.count_tokens(
            model=self.model,
            messages=[
                {"role": "user", "content": str(text)}
            ]
        )
        try:
            return res.input_tokens
        except:
            return 0

    def prompt_template(self, text: str):
        raise NotImplementedError(
            "prompt_template method not implemented for this provider {}".format(self.__class__.__name__))

    def create_batch_generate_message(self, custom_id, texts: list[str]):
        pass


def _get_system_prompt(model):
    """
    Get the appropriate system prompt based on the model

    Args:
        model: Model name or domain

    Returns:
        System prompt string
    """
    if model == TypeEnum.machshavot:
        return SYSTEM_EDUCATION_ASSITANT
    else:
        return LEGAL_ASSISTANT_CHAT


class ChatAnthropic(AnthropicProvider):
    def __init__(self):
        super().__init__(api_key=os.getenv("ANTHROPIC_API_KEY"),
                         model=CHAT_COMPLETION_PREMIUM_MODEL,
                         max_tokens=MAX_ANSWER_TOKENS,
                         temperature=CLAUDE_ANSWER_TEMPERATURE,
                         top_p=CLAUDE_ANSWER_TOP_P)
        self.client_async = AsyncAnthropic(api_key=os.getenv("ANTHROPIC_API_KEY"), timeout=60)
        # Initialize the client and configuration constants for this provider

    def _set_up_model(self, model):
        if model == "standard_model":
            self.model = CHAT_COMPLETION_STANDARD_MODEL
        elif model == TypeEnum.machshavot:
            self.model = CHAT_COMPLETION_MACHSHAVOT
        else:
            self.model = CHAT_COMPLETION_PREMIUM_MODEL

    async def stream_generate_message(self, full_conversation: list, model: str, mapper_cid_id: dict,
                                      citation_number: int = 1, search_results: Optional[List[dict]] = None,
                                      master_chunk: Optional[Dict[str, Any]] = None,
                                      citation_with_offset: Optional[bool] = False):
        """
        Stream a message from Anthropic API with or without citations

        Args:
            full_conversation: List of conversation messages
            model: Model name or domain
            mapper_cid_id: List of search results to include as documents
            citation_number: Citation number for the current message
            search_results: List of search results to include as documents
            master_chunk: Master chunk data
            new_citation: Flag to indicate which citation processing function to use
        Returns:
            Generator yielding message content
        """
        # Set up the model if provided
        self._set_up_model(model) if model else None

        system = _get_system_prompt(model)

        # Stream the message from Anthropic API
        async with (self.client_async.messages.stream(
                max_tokens=self.max_tokens,
                temperature=self.temperature,
                top_p=self.top_p,
                system=system,
                messages=full_conversation,
                model=self.model,
        ) as stream):
            # Initialize variables for processing the stream

            citation_counter = citation_number
            # Process each event in the stream
            async for event in stream:
                # print(str(event))
                if isinstance(event, RawContentBlockDeltaEvent):
                    if isinstance(event.delta, CitationsDelta):
                        # Process citation delta if search results were provided
                        if not citation_with_offset:
                            cb = process_citation_delta_law_machshavot(
                                event, mapper_cid_id, citation_counter, search_results, master_chunk
                            )
                        else:
                            cb = process_citation_delta_with_offset(
                                event, mapper_cid_id, citation_counter, search_results, master_chunk
                            )
                        if cb:
                            citation_counter = cb.citation_number + 1
                            yield cb
                    elif isinstance(event.delta, TextDelta):
                        yield TextObject(text=event.delta.text)


                elif isinstance(event, MessageStopEvent):
                    # Process stop event
                    yield StopEvent(
                        input_tokens=event.message.usage.input_tokens,
                        output_tokens=event.message.usage.output_tokens,
                        cache_read_input_tokens=event.message.usage.cache_read_input_tokens,
                        model_version=self.model
                    )

                # else:
                #     print(str(event))

    def generate_message(self, text: str):
        raise NotImplementedError(
            "generate_message method not implemented for this provider {}".format(self.__class__.__name__))


class MasterChunkAnthropic(AnthropicProvider):
    def __init__(self):
        super().__init__(api_key=os.getenv("ANTHROPIC_MASTER_CHUNK_API"),
                         model=ANTHROPIC_MASTER_CHUNK_MODEL,
                         max_tokens=MASTER_CHUNK_MAX_ANSWER_TOKENS,
                         temperature=MASTER_CHUNK_CLAUDE_ANSWER_TEMPERATURE,
                         top_p=CLAUDE_ANSWER_TOP_P)
        # Initialize the client and configuration constants for this provider

    def generate_message(self, text: str):
        response = self.client.messages.create(
            messages=[
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": text
                        }
                    ]
                }
            ],
            model=self.model,
            max_tokens=self.max_tokens,
            temperature=self.temperature,
            top_p=self.top_p
        )

        return response

    def create_batch_generate_message(self, custom_id, texts: dict):
        response = self.client.beta.messages.batches.create(
            requests=[
                Request(
                    custom_id=str(txt_id),
                    params=MessageCreateParamsNonStreaming(
                        messages=[
                            {
                                "role": "user",
                                "content": [
                                    {
                                        "type": "text",
                                        "text": str(text)
                                    }
                                ]
                            }
                        ],
                        model=self.model,
                        max_tokens=self.max_tokens,
                        temperature=self.temperature,
                        top_p=self.top_p
                    )
                )
                for txt_id, text in texts.items()
            ]
        )

        return response

    def get_message_batch(self, batch_id):
        messages = {}
        message_batch = self.client.messages.batches.retrieve(
            message_batch_id=batch_id)

        if message_batch.processing_status != "ended":
            return str(message_batch.processing_status), messages

        for batch_result in self.client.messages.batches.results(
                batch_id,
        ):
            messages[int(batch_result.custom_id)] = batch_result.result.message
        return message_batch.processing_status, messages

    def prompt_template(self, text: str):
        return """
            You are a legal expert tasked with summarizing an important legal document in Hebrew up to 300 words. Your goal is to carefully read through the provided text and extract key points, organizing them into a structured summary.

            First, carefully read through the following legal text:

            <legal_text>

            {{LEGAL_TEXT}}

            </legal_text>

            Your task is to create a summary of this legal decision. The summary should be in Hebrew  up to 300 words and must include the following seven sections:

            1. summary_of_facts

            2. legal_questions (detailed list of all the legal issues)

            3. court_discussion (solution to the legal questions and issues)

            4. court_final_decisions

            5. summarized_judges_opinions (only if exist: note the differences between them. Identify and explain the judges in both the majority and minority opinions, with particular attention to dissenting opinions. 100-150 words)

            6. important_phrases (provide 5 base-form tokens representing the most important legal topics discussed in the text)

            7. judges
            For each section:

            - Extract the relevant information from the legal text

            - Present it clearly and concisely

            - Maintain the original language and phrasing where possible, especially for legal terminology

            - Include specific legal references or citations where relevant

            When writing your summary:

            - Use clear and precise language

            - Focus on the most important points

            - Maintain objectivity in your reporting

            - Use legal terminology accurately

            - Provide brief explanations for complex legal terms

            After creating the summary, review it for internal consistency and accuracy.

            structure:
            ||Summary_of_Facts%% <value>
            ||Legal_Questions%%  <value>
            ||Summarized_Judges_Opinions%% <value>
            ||Legal_Questions%% <value>
            ||Important_phrases%% <value>
            ||Judges%% <value>
            with no additional  introductory before or betwen sections.

            Between each section be sure to add the following delimiter: "||". and before the value of each field add following delimiter "%%".
            """.replace("{{LEGAL_TEXT}}", text.strip())


class RatioAnthropic(AnthropicProvider):
    def __init__(self):
        super().__init__(api_key=os.getenv("ANTHROPIC_RATIO_API_KEY"),
                         model=CHAT_COMPLETION_RATIO_MODEL,
                         max_tokens=MASTER_CHUNK_MAX_ANSWER_TOKENS,
                         temperature=CLAUDE_ANSWER_TEMPERATURE,
                         top_p=CLAUDE_ANSWER_TOP_P)
        # Initialize the client and configuration constants for this provider

    def generate_message(self, text: str):
        # Custom logic for generating message using ratio-specific logic
        response = self.client.messages.create(model=self.model,
                                               max_tokens=self.max_tokens,
                                               stop_sequences=['\n\nHuman:', '\n\nAssistant:'],
                                               temperature=self.temperature,
                                               top_p=self.top_p,
                                               messages=[
                                                   {
                                                       "role": "user",
                                                       "content": [
                                                           {
                                                               "type": "text",
                                                               "text": text
                                                           }
                                                       ]
                                                   }
                                               ]
                                               )
        return response


class LawQuestionAnthropic(AnthropicProvider):
    def __init__(self):
        super().__init__(
            api_key=os.getenv("ANTHROPIC_MASTER_CHUNK_API"),
            model=LAW_QUESTION_MODEL,
            max_tokens=MASTER_CHUNK_MAX_ANSWER_TOKENS,
            temperature=MASTER_CHUNK_CLAUDE_ANSWER_TEMPERATURE,
            top_p=CLAUDE_ANSWER_TOP_P
        )
        self.prompt = None

    def prompt_template(self, text: str):
        return """
                You are a legal expert you are tasked with generating in Hebrew only the top 4 most important questions about the legal issues that appear in the laws document in Hebrew try to ask various questions about different sections of the law and try to have elaborated Hebrew questions. Your job is to carefully read through the text ask the most important questions that a lawyer will be highly interested to know on the provided laws documents.
                Read the following legal text carefully:
                <legal_text>
                {LEGAL_TEXT}
                </legal_text>""".format(LEGAL_TEXT=text)

    def generate_message(self, text: str):
        # Custom logic for generating message with law question handling
        response = self.client.messages.create(
            model=self.model,
            max_tokens=self.max_tokens,
            temperature=self.temperature,
            top_p=self.top_p,
            messages=[
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": text

                        }
                    ]
                }
            ]
        )
        return response


class NerModelAnthropic(AnthropicProvider):
    def __init__(self):
        super().__init__(
            api_key=os.getenv("ANTHROPIC_NER_API_KEY"),
            model=NER_MODEL,
            max_tokens=1012,
            temperature=0.5,
            top_p=CLAUDE_ANSWER_TOP_P
        )
        self.system = ("You are an AI assistant trained to perform named entity recognition (NER) on "
                       "Hebrew text. Your task is to identify and extract named entities of type PERSON, "
                       "such as the names of people. For this task, you should ignore placeholders and "
                       "generic references to individuals.")

    def prompt_template(self, text: str):
        return f"""
        {{
            "type": "text",
            "text": " Text to analyze: {text}

            Exclude any placeholders or nonspecific references such as "צד א", "צד ב'", "אח שלי", "לקוח שלי", "גרושתו", etc.

            Please read through the text carefully, looking for any mentions of PERSON entities. In Hebrew, PERSON entities may include:
            - First name
            - Last name
            - First name + last name
            - First initial + last name
            - First initial + middle initial + last name
            - Title + last name
            When you identify a PERSON entity, extract the full name as it appears, preserving any initials or titles.

            After you have finished analyzing the text, respond with a JSON object containing two fields:
            1. 'has_ner': a boolean value indicating whether the text contains any named entities of type PERSON (true) or not (false).
            2. 'entities': an array containing all the PERSON named entities you found in the text. If no PERSON entities are found, this should be an empty array.
            NO additional explenation or text alloued
            }}
                    """

    def generate_message(self, text: str):
        # Custom logic for generating message with NER-specific logic
        response = self.client.messages.create(messages=[{"role": "user", "content": text}],
                                               model=self.model,
                                               max_tokens=self.max_tokens,
                                               temperature=self.temperature,
                                               top_p=self.top_p,
                                               system=self.system
                                               )

        return response
