from abc import abstractmethod, ABC

class AIProvider(ABC):

    @abstractmethod
    def generate_message(self, text: str):
        pass

    @abstractmethod
    def parse_message(self, message):
        pass

    @abstractmethod
    def tokenize(self, context):
        pass

    def prompt_template(self, text):
        pass


class AIProviderFactory:

    def __init__(self):
        self._creators = {}



    def register_provider(self, provider_name, creator):
        self._creators[provider_name] = creator

    def provider_choose(self, provider_name) -> AIProvider:
        creator = self._creators.get(provider_name)
        if not creator:
            raise ValueError(provider_name)
        return creator()