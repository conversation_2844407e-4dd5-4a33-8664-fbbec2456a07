import contextvars

from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware


endpoint_name_ctx = contextvars.ContextVar("endpoint_name", default="unknown")
request_token_ctx = contextvars.ContextVar("request_token", default=None)



class EndpointNameMiddleware(BaseHTTPMiddleware):
    # Create a context variable to store the endpoint name for each request for logging purposes

    async def dispatch(self, request: Request, call_next):
        # Set the endpoint name context variable
        endpoint_name_ctx.set(request.url.path)
        request_token_ctx.set(request.headers.get("token", None))

        response = await call_next(request)
        # Clear the context variable
        endpoint_name_ctx.set("unknown")
        request_token_ctx.set(None)  # לשים ערך ברירת מחדל

        return response