# from fastapi.testclient import TestClient
# from app.main import app
# import pytest
#
# client = TestClient(app)
#
# @pytest.mark.parametrize("txtId", ["sampleText1", "sampleText2"])
# def test_embed(txtId):
#     response = client.post("/embed", json={"txtId": txtId})
#
#     # Ensure status code 204 (No Content) is returned on successful embedding
#     assert response.status_code == 200
#
#     # If there's an error, the API should return a 500 status code and a message
#     if response.status_code == 500:
#         assert "message" in response.json()
#
