def create_chunks_from_raw(chunks: dict) -> dict[str, LawChunkModel]:
    combined = {}
    for cid, chunk in chunks.items():
        ctext = chunk.get('cText')
        if not ctext:
            continue # Skip if ctext is empty
        combined[cid] = LawChunkModel(
            id=cid,
            content=ctext,
            **chunk
        )
    return combined


def normalize_titles(metadata: dict) -> dict:
    chapter = (metadata.get("chapter_title") or "").strip()
    provision = (metadata.get("provision_title") or "").strip()
    provision_text = (metadata.get("provision_text") or "").strip()

    if not chapter and provision:
        metadata["chapter_title"] = provision
    elif not provision and chapter:
        metadata["provision_title"] = chapter
    elif not chapter and not provision and provision_text:
        fallback = provision_text[:50]
        metadata["chapter_title"] = fallback
        metadata["provision_title"] = fallback

    return metadata



def update_chunks_with_metadata(combined: dict[str, LawChunkModel], meta_datas: dict[str, dict], doc_id: int) -> None:
    for cid, metadata in meta_datas.items():
        metadata = normalize_titles(metadata)  # <- הוספה כאן
        existing_chunk = combined.get(cid)

        if existing_chunk:
            updated_fields = {
                field_name: new_value
                for field_name, new_value in metadata.items()
                if new_value is not None and getattr(existing_chunk, field_name, None) in [None, ""]
            }
            if updated_fields:
                combined[cid] = existing_chunk.model_copy(update=updated_fields)
        else:
            logger.error(
                f"LawDocumentStrategy.get_full_chunks: Chunk with cid={cid} not found in combined chunks for doc_id={doc_id} in Table AiEmbed"
            )
