"""
User Chat History Module

Provides a function to retrieve and group user chat history from MongoDB
with time-based grouping and pagination support.
"""

from datetime import datetime, timedelta
from typing import List, Dict, Any
from collections import defaultdict
import pytz

from motor.motor_asyncio import AsyncIOMotorClient
from pymongo.errors import PyMongoError

from configs.app_config import MONGO_DB_NAME
from middlewares.logging_utils import app_logger as logger

# Jerusalem timezone (UTC+3)
JERUSALEM_TZ = pytz.timezone("Asia/Jerusalem")
CHATS_COLLECTION = "chats"


def get_jerusalem_now() -> datetime:
    """Get current datetime in Jerusalem timezone."""
    return datetime.now(JERUSALEM_TZ)


def get_time_boundaries() -> Dict[str, datetime]:
    """
    Calculate time boundaries for grouping chats.
    All boundaries are in Jerusalem timezone.
    
    Returns:
        Dict with boundary datetimes
    """
    now = get_jerusalem_now()
    
    # Start of today (00:00:00 Jerusalem time)
    today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
    
    # Start of this week (Monday 00:00:00 Jerusalem time)
    days_since_monday = now.weekday()
    week_start = today_start - timedelta(days=days_since_monday)
    
    # Start of this month (1st day 00:00:00 Jerusalem time)
    month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    
    # Start of this year (January 1st 00:00:00 Jerusalem time)
    year_start = now.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
    
    return {
        "now": now,
        "today_start": today_start,
        "week_start": week_start,
        "month_start": month_start,
        "year_start": year_start
    }


def classify_chat_by_time(created_at_utc: datetime, boundaries: Dict[str, datetime]) -> str:
    """
    Classify a chat into a time group based on its creation time.
    
    Args:
        created_at_utc: UTC datetime when chat was created
        boundaries: Time boundaries dict from get_time_boundaries()
        
    Returns:
        str: Time group label in Hebrew or year string
    """
    # Convert UTC to Jerusalem timezone
    if created_at_utc.tzinfo is None:
        created_at_utc = pytz.utc.localize(created_at_utc)
    
    chat_time_local = created_at_utc.astimezone(JERUSALEM_TZ)
    
    # Check time groups in order (most recent first)
    if chat_time_local >= boundaries["today_start"]:
        return "היום"
    elif chat_time_local >= boundaries["week_start"]:
        return "מוקדם יותר השבוע"
    elif chat_time_local >= boundaries["month_start"]:
        return "מוקדם יותר החודש"
    elif chat_time_local >= boundaries["year_start"]:
        return "מוקדם יותר השנה"
    else:
        # For previous years, return the year as string
        return str(chat_time_local.year)


async def get_user_chat_history(
    mongo_client: AsyncIOMotorClient,
    user_id: str, 
    page: int = 0, 
    page_size: int = 20
) -> List[Dict[str, Any]]:
    """
    Retrieve user's chat history with time-based grouping and pagination.
    
    Args:
        mongo_client: Motor MongoDB client
        user_id: User identifier
        page: Page number (0-based)
        page_size: Number of items per page
        
    Returns:
        List of dictionaries with time-grouped chats:
        [
            {
                "label": "היום",
                "chats": [...]
            },
            ...
        ]
    """
    try:
        db = mongo_client[MONGO_DB_NAME]
        collection = db[CHATS_COLLECTION]
        
        # Calculate pagination
        skip = page * page_size
        
        # Query for user's chats, sorted by created_at descending
        query = {"user_id": user_id}
        
        # Get paginated results
        cursor = collection.find(query).sort("created_at", -1).skip(skip).limit(page_size)
        documents = await cursor.to_list(length=page_size)
        
        if not documents:
            return []
        
        # Get time boundaries for grouping
        boundaries = get_time_boundaries()
        
        # Group chats by time periods
        groups_dict = defaultdict(list)
        
        for doc in documents:
            try:
                created_at = doc.get('created_at')
                if not created_at:
                    logger.warning(f"Chat {doc.get('_id')} missing created_at field")
                    continue
                
                # Classify into time group
                time_group = classify_chat_by_time(created_at, boundaries)
                
                # Convert ObjectId to string for JSON serialization
                chat_item = {
                    "_id": str(doc.get('_id')),
                    "user_id": doc.get('user_id'),
                    "chat_id": doc.get('chat_id'),
                    "title": doc.get('title', 'Untitled Chat'),
                    "created_at": created_at.isoformat() if hasattr(created_at, 'isoformat') else str(created_at)
                }
                
                # Add any additional fields from the document
                for key, value in doc.items():
                    if key not in chat_item and key != '_id':
                        chat_item[key] = value
                
                groups_dict[time_group].append(chat_item)
                
            except Exception as e:
                logger.error(f"Error processing chat document {doc.get('_id')}: {e}")
                continue
        
        # Convert to the required output format
        result = []
        
        # Define the order of time groups
        time_group_order = [
            "היום",
            "מוקדם יותר השבוע", 
            "מוקדם יותר החודש",
            "מוקדם יותר השנה"
        ]
        
        # Add ordered time groups first (only if they have chats)
        for group_label in time_group_order:
            if group_label in groups_dict:
                result.append({
                    "label": group_label,
                    "chats": groups_dict[group_label]
                })
        
        # Add year-based groups (sorted in descending order)
        year_groups = [label for label in groups_dict.keys() if label not in time_group_order]
        year_groups.sort(reverse=True)  # Most recent years first
        
        for year_label in year_groups:
            result.append({
                "label": year_label,
                "chats": groups_dict[year_label]
            })
        
        return result
        
    except PyMongoError as e:
        logger.error(f"MongoDB error in get_user_chat_history: {e}")
        raise ConnectionError(f"Database error: {e}")
    except Exception as e:
        logger.error(f"Error in get_user_chat_history: {e}")
        raise


# Standalone function that can be imported directly
async def get_user_chat_history_standalone(
    user_id: str, 
    page: int = 0, 
    page_size: int = 20,
    mongo_client: AsyncIOMotorClient = None
) -> List[Dict[str, Any]]:
    """
    Standalone version of get_user_chat_history that can be called directly.
    
    Args:
        user_id: User identifier
        page: Page number (0-based)
        page_size: Number of items per page
        mongo_client: Optional MongoDB client (if None, will need to be provided)
        
    Returns:
        List of dictionaries with time-grouped chats
    """
    if mongo_client is None:
        raise ValueError("mongo_client is required")
    
    return await get_user_chat_history(
        mongo_client=mongo_client,
        user_id=user_id,
        page=page,
        page_size=page_size
    )


def format_chat_for_display(chat: Dict[str, Any]) -> Dict[str, Any]:
    """
    Format a chat document for display purposes.
    
    Args:
        chat: Raw chat document
        
    Returns:
        Formatted chat dictionary
    """
    try:
        # Parse created_at if it's a string
        created_at = chat.get('created_at')
        if isinstance(created_at, str):
            try:
                created_at = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
            except ValueError:
                created_at = datetime.now(pytz.utc)
        elif created_at is None:
            created_at = datetime.now(pytz.utc)
        
        # Convert to Jerusalem timezone for display
        if created_at.tzinfo is None:
            created_at = pytz.utc.localize(created_at)
        
        jerusalem_time = created_at.astimezone(JERUSALEM_TZ)
        
        return {
            "_id": chat.get('_id'),
            "user_id": chat.get('user_id'),
            "chat_id": chat.get('chat_id'),
            "title": chat.get('title', 'Untitled Chat'),
            "created_at": created_at.isoformat(),
            "created_at_local": jerusalem_time.strftime("%Y-%m-%d %H:%M"),
            "created_date": jerusalem_time.strftime("%Y-%m-%d"),
            "created_time": jerusalem_time.strftime("%H:%M")
        }
        
    except Exception as e:
        logger.error(f"Error formatting chat for display: {e}")
        return chat


def get_time_group_summary(groups: List[Dict[str, Any]]) -> Dict[str, int]:
    """
    Get a summary of chat counts by time group.
    
    Args:
        groups: List of time-grouped chats from get_user_chat_history
        
    Returns:
        Dictionary with group labels and chat counts
    """
    summary = {}
    for group in groups:
        summary[group["label"]] = len(group["chats"])
    return summary
