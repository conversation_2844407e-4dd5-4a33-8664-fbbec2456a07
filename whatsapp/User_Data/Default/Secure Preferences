{"extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "Discover great apps, games, extensions and themes for Google Chrome.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Web Store", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "/Applications/Google Chrome.app/Contents/Frameworks/Google Chrome Framework.framework/Versions/136.0.7103.114/Resources/web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chrome PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "/Applications/Google Chrome.app/Contents/Frameworks/Google Chrome Framework.framework/Versions/136.0.7103.114/Resources/pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "pinned_tabs": [], "protection": {"macs": {"browser": {"show_home_button": "F73D56B0ABD52DFEB302BEA75B5CDA1A54D00288D62FAFADAEFBFEC8ED667264"}, "default_search_provider_data": {"template_url_data": "8C84EFAB120F007DFBBB1CF70E472CCCC6547E8136EF6AD27BA685407DADFD0A"}, "enterprise_signin": {"policy_recovery_token": "A2F363B29C8773115C8B8855A2DE57C4B63D0155E07F33606EB074390BEFABCB"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "52935EDE46FFA00ED76BC2F57509D879C91383C71B5D96C3C0076A71F003DEE9", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "BB5E67F0E35F6C627FEB1FFB10F649D811D8E8BC701A7C400C0793C5D4B2A720"}, "ui": {"developer_mode": "ACF24A52E217BDC553F8D1F8E29B0CCC7CAE9F1BA99521EEB39ACCD751263674"}}, "google": {"services": {"account_id": "7792FEA0AD5DCEC32181AEA8CA56285038A1F35A31711B81D63494AFD5A093F7", "last_signed_in_username": "9CA115EBA9283BBE322C5E807D2B59E576B2769BCEC9D1C9F720218B02FEDF04", "last_username": "D027E08410BC7A3E8DC850CCCDBEF754A5FE1478D15510156507DE37F48D00E5"}}, "homepage": "588E857668C5D06DCDEE5420907FDF8C3D999494D0F07DAB63F1FAA778012133", "homepage_is_newtabpage": "B7DED397473460A01175AB31F67DB2D4F33D77A242B4F2A9B270C94F1448078E", "media": {"storage_id_salt": "9B99399A5E01DDFA1AC308E4C6F1AFFEF6A080B03B0A3C66447D174CB57BB8E8"}, "pinned_tabs": "AD416A923A9A6C39832FAA165AD8B0D7E639C052C5CB6DF115E30C6B850A65FC", "prefs": {"preference_reset_time": "14CEB8590BFAE35AA9BD42299F93487408031F18E576A48A3CDF8A8DA9F24DFA"}, "safebrowsing": {"incidents_sent": "ACB162517184000309AA59CB9C08A0C857C0DFA469EA96BD831223003BBB7EBC"}, "search_provider_overrides": "BD624560FB5E5041EA5DC2C04A991A4EBC0D2FFCA31F2F8B366B82CB2B5D7120", "session": {"restore_on_startup": "9E13814C9210A5EEDE5E3F1138CFC0297309F91EEA75EBBEC3886E3CB44AE0B7", "startup_urls": "EF00AB3D165E50BF48A578418DBE0E34588BF21B0A7C7B99FD5072039CF37EA9"}}, "super_mac": "D430E1344ADA8ABCABAA5D684BA7B148D92D35A5C93A6258B7F283659E25BAEB"}}