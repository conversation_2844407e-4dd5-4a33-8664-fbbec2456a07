import re
from time import time
from typing import List, <PERSON>ple

import chardet

from db_utils.sql_helper import get_text_only_and_full_text
from utils.constants_util import MASTER_CHUNK_MAX_TOTAL_TOKENS
from etl.etl_dto import JobResult, RatioStatusEnum, RatioResult, MasterChunkStatusEnum, TxtIdMasterChunkResult, \
    TxtIdRatioResult, StatusEnum, TxtIdResult, LawQuestionsResult, LawQuestionsStatusEnum, TxtIdLawQuestionsResult, \
    MasterChunkResult, MasterChunkResultBatch
from middlewares.logging_utils import app_logger as logger
from db_utils.pinecone_helper import store_in_vector_db
from etl.etl_utils.etl_helpers import prep_law_chunk_for_embedding, prep_verdict_chunk_for_embedding, prepare_metadata, split_verdict_text, split_law_text, should_ingest
from data_ops.pinecone_func import get_sparse_embedding
from data_ops.embedding_helper import n_tokens, embed_texts
from db_utils.sql_utils import store_in_chunk_db, store_summary, store_ratio, get_maagar_id_and_total_tokens_for_txt_id
from etl.etl_utils.etl_utils_sql import populate_text_verdicts_metadata, is_resource_a_law, get_full_row, \
    populate_text_metadata, populate_text_laws_metadata, get_verdict_child_text


def debug_encoding(text, identifier):
    # Check the character encoding of the text
    encoding_detected = chardet.detect(text.encode())['encoding']

    # Log the detected encoding and the first 500 characters of the text
    logger.info(f"{identifier}: Detected encoding - {encoding_detected}, Text preview - {text[:500]}")


def _safe_store_in_vector_db(is_law, index, all_is_law, embeddings, sparse_embeddings, all_metadata, all_cids, txt_ids,
                             status):
    try:
        embeddings_ = [emb for il, emb in zip(all_is_law, embeddings) if il == is_law]
        sparse_embeddings_ = [emb for il, emb in zip(all_is_law, sparse_embeddings) if il == is_law]
        metadata_ = [md for il, md in zip(all_is_law, all_metadata) if il == is_law]
        # prev_chunk_ids_ = [pci for pci in prev_chunk_ids if txt_id_to_is_law[int(pci.split('-')[0])] == is_law]
        if embeddings_:
            t0 = time()
            store_in_vector_db(all_cids, index, embeddings_, sparse_embeddings_, metadata_, txt_ids, status)
            logger.info(
                f'Storing {len(embeddings_)} embeddings in {"law" if is_law else "verdict"} index took {time() - t0:.2f} secs')
    except Exception as e:
        logger.error(f"Error in safe_store_in_vector_db: {e}")
        raise


def _safe_store_in_chunk_db(job_result, all_metadata, all_raw_texts, all_cleaned_chunks, txt_ids, txt_id_result_list,
                            status, runId=''):
    try:
        t0 = time()
        text_length_token = n_tokens(all_cleaned_chunks)

        txt_ids_with_null_ntokens = []
        chunks_ids = [chunk_metadata["cid"] for chunk_metadata in all_metadata]
        if text_length_token == None or text_length_token == [] or 0 in text_length_token or None in text_length_token:
            txt_ids_with_null_ntoken = str({"chunks_ids": chunks_ids, "chunks": all_cleaned_chunks})
            update_txt_ids_with_null_ntoken(job_result, txt_ids_with_null_ntoken)
            txt_ids_with_null_ntokens.append(txt_ids)

        # TODO - for the verdict at least, we already tokenized so could have save some time by reusing it
        rows = [{"txtId": chunk_metadata["txt_id"], "cId": chunk_metadata["cid"], "cText": raw_text,
                 'nTokens': text_len_tok, "maagarId": chunk_metadata["maagarId"], "runId": runId}
                for chunk_metadata, raw_text, text_len_tok in zip(all_metadata, all_raw_texts, text_length_token)]
        store_in_chunk_db(txt_ids, rows, status)
        if len(txt_ids_with_null_ntokens) > 0:
            message = 'This txt_id data saved in AiEmbed table but with nTokens null'
            [update_txtid_result_by_id(txt_id, txt_id_result_list, StatusEnum.saved_in_sql_with_null_ntokens, message)
             for txt_id in txt_ids_with_null_ntokens]
        logger.info(f'Storing {len(rows)} rows in SQL took {time() - t0:.2f} secs')
    except Exception as e:
        logger.error(f"Error in safe_store_in_chunk_db: {e}")
        raise


def validate_chunk(text):
    # Check if text is longer than 10 characters
    if len(text) <= 18:
        return False

    # Check for one or more Hebrew letters
    if not re.search(r'[\u0590-\u05FF]+', text):
        return False

    # Check for at least two white spaces
    if len(re.findall(r'\s', text)) < 2:
        return False

    return True


async def ingest_new_resources(status, txt_ids: List[int], cohere_client, law_index, verdicts_index, txt_id_result_list,
                               job_result,ai_provider, runId=''):
    have_exception = False
    t = time()
    if len(txt_ids) < 5:
        logger.info(f'Start ingesting txt_ids {txt_ids}')
    else:
        logger.info(f'Start ingesting {len(txt_ids)} txt_ids')

    # read the resources (sync, batch)
    try:
        results_list = get_full_row(job_result, txt_ids)
    except Exception as error:
        have_exception = True
        return have_exception, error

    result_ids = [result.get('txtId') for result in results_list]
    result_ids_with_error = list()
    if len(result_ids) < len(txt_ids):
        for id_ in txt_ids:
            if id_ not in result_ids:
                update_txtid_result_by_id(txt_id=id_, txt_id_result_list=txt_id_result_list, status=StatusEnum.started,
                                          message='This txtId, not a verdic or a law')
                result_ids_with_error.append(id_)

                # filter out results that are irrelevant for us (sync, sequential)
    results_list = [results_dict for results_dict in results_list if should_ingest(results_dict)]
    result_ids_should_ingest = [result.get('txtId') for result in results_list]

    if len(result_ids_should_ingest) < len(result_ids):
        for id_ in result_ids:
            if id_ not in result_ids_should_ingest:
                update_txtid_result_by_id(txt_id=id_, txt_id_result_list=txt_id_result_list, status=StatusEnum.started,
                                          message='This txtId did not need to ingest, so it does not continue.')

    if len(results_list) == 0:
        logger.info('All the given txt_ids should not be inserted')
        return txt_ids
    t_ret, t = time() - t, time()
    update_job_result_retrieval_time(job_result=job_result, retrieval_time=t_ret)

    # prep for each result (sync, sequential):
    all_chunks, all_cleaned_chunks, all_metadata, all_is_law, all_txt_ids, all_raw_texts, chunks_per_txt_id = [], [], [], [], [], [], []
    txt_id_to_is_law = {}

    for results_dict in results_list:
        try:
            txt_id = results_dict['txtId']
            is_law = is_resource_a_law(results_dict)
            full_text = results_dict['text']  # it will be xml for laws and clean content for verdicts
            split_text, prep_text, text_key = (
            split_law_text, prep_law_chunk_for_embedding, 'provision_text') if is_law else (
            split_verdict_text, prep_verdict_chunk_for_embedding, 'content')
            debug_encoding(full_text, f"txt_id - {txt_id}")
        except Exception as e:
            update_txtid_result_by_id(txt_id=txt_id, txt_id_result_list=txt_id_result_list, status=StatusEnum.started,
                                      message=f'It still has not reached the division into chunks, an error in the splits, the error is: {str(e)}')
            result_ids_with_error.append(txt_id)

        if txt_id not in result_ids_with_error:
            # Split the text into chunks
            try:
                t_chunks_creat = time()
                chunks = split_text(txt_id,
                                    full_text)  # these chunks are the (slightly cleaned) raw texts that will be stored in the SQL and may be visible to the user
                chunks_spliting_time = time() - t_chunks_creat
                logger.info(f'chunks_spliting_time {chunks_spliting_time}')
                update_job_result_chunks_spliting_time(job_result, chunks_spliting_time)
                update_txtid_result_by_id(txt_id=txt_id, txt_id_result_list=txt_id_result_list,
                                          status=StatusEnum.chunks_created, message='This txtId chunks created')
            except Exception as e:
                update_txtid_result_by_id(txt_id=txt_id, txt_id_result_list=txt_id_result_list,
                                          status=StatusEnum.chunks_created,
                                          message=f'It was not divided into chunks, the error is: {str(e)}')
                result_ids_with_error.append(txt_id)

        chunks = [chunk for chunk in chunks if validate_chunk(chunk.get("provision_text"))] if is_law else [chunk for
                                                                                                            chunk in
                                                                                                            chunks if
                                                                                                            validate_chunk(
                                                                                                                chunk.get(
                                                                                                                    "content"))]

        if txt_id not in result_ids_with_error:
            try:
                # Clean the text
                cleaned_chunks_start = time()
                cleaned_chunks = [prep_text(chunk) for chunk in
                                  chunks]  # these are the heavily cleaned  (possibly prepended with additional metadata) chunks that are passed to embedding
                cleaned_chunks_time = time() - cleaned_chunks_start
                logger.info(f'cleaned_chunks_time {cleaned_chunks_time}')
                update_job_result_cleaned_chunks_time(job_result, cleaned_chunks_time)
            except Exception as e:
                update_txtid_result_by_id(txt_id=txt_id, txt_id_result_list=txt_id_result_list,
                                          status=StatusEnum.chunks_created,
                                          message=f'Error in cleaning the chunks, the error is: {str(e)}')
                result_ids_with_error.append(txt_id)

        if txt_id not in result_ids_with_error:
            try:
                # prepare metadata to be stored
                prepare_metadata_start = time()
                metadata = prepare_metadata(txt_id, chunks, is_law, results_dict)
                for chunk in metadata:
                    chunk.update({"maagarId": results_dict.get("maagarId")})
                prepare_metadata_time = time() - prepare_metadata_start
                logger.info(f'prepare_metadata_time {prepare_metadata_time}')
                update_job_result_prepare_metadata_time(job_result, prepare_metadata_time)
            except Exception as e:
                update_txtid_result_by_id(txt_id=txt_id, txt_id_result_list=txt_id_result_list,
                                          status=StatusEnum.chunks_created,
                                          message=f'Error loading metadata, the error is: {str(e)}')
                result_ids_with_error.append(txt_id)

        if txt_id not in result_ids_with_error:
            try:

                all_txt_ids.extend(
                    [txt_id] * len(chunks))  # may not be identical to the input due to filtering of irrelevant ids
                all_is_law.extend([is_law] * len(chunks))
                all_chunks.extend(chunks)
                all_cleaned_chunks.extend(cleaned_chunks)
                txt_id_chunks = {"txtId": txt_id, "chunks": cleaned_chunks}
                chunks_per_txt_id.append(txt_id_chunks)
                all_metadata.extend(metadata)
                all_raw_texts.extend((chunk[text_key] for chunk in chunks))
                txt_id_to_is_law[txt_id] = is_law
            except Exception as e:
                update_txtid_result_by_id(txt_id=txt_id, txt_id_result_list=txt_id_result_list,
                                          status=StatusEnum.chunks_created,
                                          message=f'Error before sending to embeddings, after loading the metadata , the error is: {str(e)}')
                result_ids_with_error.append(txt_id)

    t_prep, t = time() - t, time()
    all_cids = [chunk.get('cid') for chunk in all_chunks]
    update_job_result_chunks_time(job_result=job_result, chunks_time=t_prep)

    update_job_result_chunks_amount(job_result=job_result, chunks_amount=len(all_chunks))
    if len(all_chunks) == 0:
        [update_txtid_result_by_id(txt_id=id_, txt_id_result_list=txt_id_result_list, status=StatusEnum.chunks_created,
                                   message='There are no chunks, nothing to go on') for id_ in result_ids_should_ingest
         if id_ not in result_ids_with_error]
        raise JobResult(text_ids_results=txt_id_result_list, message='There are no chunks, nothing to go on',
                        status_code=500)

    try:
        future_embeddings = await embed_texts(cohere_client, all_cleaned_chunks, input_type='search_document')

        t0 = time()
        # while we wait for them, let's create the sparse embeddings
        sparse_embeddings = [get_sparse_embedding(is_law, metadata, enc_type='document') for is_law, metadata in
                             zip(all_is_law, all_metadata)]
        logger.info(f'Creating sparse embeddings took {time() - t0:.2f} seconds')  # ****

        # Retrieving the results
        embeddings = future_embeddings

        # rows_to_delete, prev_chunk_ids = future_chunk_ids

        [update_txtid_result_by_id(txt_id=id_, txt_id_result_list=txt_id_result_list,
                                   status=StatusEnum.sent_to_embeddings, message='This txtId embeded.') for id_ in
         result_ids_should_ingest if id_ not in result_ids_with_error]
    except Exception as e:
        mess = f'There is an error in embeding process , regardless of txtId, There was a problem calling coheir, please try sending this batch again. Or there is a problem saving to a table in sql db, Error details: {e}'
        [update_txtid_result_by_id(txt_id=id_, txt_id_result_list=txt_id_result_list,
                                   status=StatusEnum.sent_to_embeddings, message=mess) for id_ in
         result_ids_should_ingest if id_ not in result_ids_with_error]
        raise JobResult(text_ids_results=txt_id_result_list, message=mess, status_code=500)

    t_emb, t = time() - t, time()
    update_job_result_cohear_time(job_result=job_result, cohear_time=t_emb)

    try:
        _safe_store_in_vector_db(True, law_index, all_is_law, embeddings, sparse_embeddings, all_metadata, all_cids,
                                 txt_ids, status)  # שמירה ב PINCONE
        _safe_store_in_vector_db(False, verdicts_index, all_is_law, embeddings, sparse_embeddings, all_metadata,
                                 all_cids, txt_ids, status)  # שמירה ב PINCONE
        [update_txtid_result_by_id(txt_id=id_, txt_id_result_list=txt_id_result_list,
                                   status=StatusEnum.saved_in_pincone, message='This txtId chunks saved in PINCONE DB.')
         for id_ in result_ids_should_ingest if id_ not in result_ids_with_error]
    except Exception as e:
        if 'metadata size is 43137 bytes, which exceeds the limit of 40960 bytes per vector' in e.body:
            ids_with_pinecone_metadata_limit_error = txt_ids
            update_ids_with_pinecone_metadata_limit_error(job_result, ids_with_pinecone_metadata_limit_error)
        mess = f'Error saving to pincone, not dependent on txtId. third party error. The error message is {e}'
        [update_txtid_result_by_id(txt_id=id_, txt_id_result_list=txt_id_result_list,
                                   status=StatusEnum.saved_in_pincone, message=mess) for id_ in result_ids_should_ingest
         if id_ not in result_ids_with_error]
        raise JobResult(text_ids_results=txt_id_result_list, message=mess, status_code=500)

    t_pin_store = time() - t
    update_job_result_pincone_time(job_result=job_result, pincone_time=t_pin_store)

    try:
        all_chunks = [chunk.get('provision_text') for chunk in all_chunks] if is_law else [chunk.get('content') for
                                                                                           chunk in all_chunks]
        _safe_store_in_chunk_db(job_result, all_metadata, all_raw_texts, all_chunks, txt_ids, txt_id_result_list,
                                status, runId)
        [update_txtid_result_by_id(txt_id=id_, txt_id_result_list=txt_id_result_list, status=StatusEnum.saved_in_sql,
                                   message='This txtId chunks saved in sql DB.') for id_ in result_ids_should_ingest if
         id_ not in result_ids_with_error]
    except Exception as e:
        mess = f'Error saving to sql, not dependent on txtId. third party error. The error message is {e}'
        [update_txtid_result_by_id(txt_id=id_, txt_id_result_list=txt_id_result_list,
                                   status=StatusEnum.did_not_save_in_sql, message=mess[:450]) for id_ in
         result_ids_should_ingest if id_ not in result_ids_with_error]
        raise JobResult(text_ids_results=txt_id_result_list, message=mess, status_code=500)

    t_sql_store, t = time() - t, time()
    update_job_result_sql_time(job_result=job_result, sql_time=t_sql_store)

    t_tot = t_ret + t_prep + t_emb + t_pin_store + t_sql_store
    t_store = t_pin_store + t_sql_store
    ids_stored = set(all_txt_ids)
    logger.info(
        f'Done storing {len(ids_stored)} ids out of {len(txt_ids)} given ({len(all_chunks)} chunks) in {t_tot:.2f} seconds (Retrieval took {t_ret:.2f}s, Prep {t_prep:.2f}s, Embed {t_emb:.2f}s and Store {t_store:.2f}s)')
    update_job_result_total_time(job_result=job_result, total_time=t_tot)

    # After storing in chunk_db, call populate_text_metadata
    try:
        populate_text_metadata_start = time()
        populate_text_metadata(results_list, chunks_per_txt_id, txt_ids, job_result, status,ai_provider, runId)
        # if is_law:
        #     populate_text_laws_metadata(all_metadata, txt_ids, runId=runId)
        logger.info(f'Populated metadata for txt_ids: {txt_ids}')
        populate_text_metadata_time = time() - populate_text_metadata_start
        logger.info(f'populate_text_metadata_time {populate_text_metadata_time}')
        update_job_result_populate_text_metadata_time(job_result, populate_text_metadata_time)

        t_tot = t_tot + populate_text_metadata_time
        update_job_result_total_time(job_result=job_result, total_time=t_tot)
    except Exception as e:
        logger.error(f"Error in populate_text_metadata for txt_ids {txt_ids}: {e}")
        message = f'Error in populate_text_metadata - {e}'
        [update_txtid_result_by_id(txt_id, txt_id_result_list, StatusEnum.saved_in_aimetadata_sql_table, message) for
         txt_id in txt_ids]
        update_txt_ids_not_saved_in_aimetadata(job_result, txt_ids)

    return have_exception, ''


def ingest_verdicts_metadata(status, txt_ids: List[int], job_result, run_id,ai_provider):
    if len(txt_ids) < 5:
        logger.info(f'Start ingesting verdicts_metadata for txt_ids {txt_ids}')
    else:
        logger.info(f'Start ingesting verdicts_metadata for {len(txt_ids)} txt_ids')

    try:
        results_list = get_full_row(job_result, txt_ids)
    except Exception as e:
        logger.error(f"Error in get_full_row for txt_ids {txt_ids}: {e}")

    results_list = [results_dict for results_dict in results_list if should_ingest(results_dict)]
    # txt_ids_with_exception = []
    try:
        populate_text_verdicts_metadata(status, results_list, txt_ids, ai_provider,run_id)
    except Exception as e:
        logger.error(f"Error in populate_text_metadata for txt_ids {txt_ids}: {e}")
        # txt_ids_with_exception.append(txt_ids)

    logger.info(f'Finished ingesting verdicts metadata for {len(txt_ids)} txt_ids')

    return


def ingest_laws_metadata(status, txt_ids: List[int], job_result, run_id):
    if len(txt_ids) < 5:
        logger.info(f'Start ingesting laws_metadata for txt_ids {txt_ids}')
    else:
        logger.info(f'Start ingesting laws_metadata for {len(txt_ids)} txt_ids')
    try:
        results_list = get_full_row(job_result, txt_ids)
    except Exception as e:
        logger.error(f"Error in get_full_row for txt_ids {txt_ids}: {e}")

    results_list = [results_dict for results_dict in results_list if should_ingest(results_dict)]
    all_metadata = []
    for resulte_dict in results_list:
        full_text = resulte_dict['text']
        txt_id = resulte_dict['txtId']
        chunks = split_law_text(txt_id, full_text)
        for chunk in chunks:
            chunk.update({'txt_id': txt_id, 'title': resulte_dict['txtTtl']})
        all_metadata.extend(chunks)
    try:
        populate_text_laws_metadata(status, all_metadata, txt_ids, run_id)
        logger.info(f'Finished ingesting laws metadata for {len(txt_ids)} txt_ids')

    except Exception as e:
        logger.error(f"Error in populate_text_metadata for txt_ids {txt_ids}: {e}")
        # txt_ids_with_exception.append(txt_ids)

    return


def summarize_verdict(sql_session, txt_id):
    logger.info(f'Got a request to summarize txt_id {txt_id}')
    logger.warning(
        'summary is not yet implemented, so creating an empty result (i.e. success with an empty summary being stored )')
    summary = ''  # TODO
    store_summary(txt_id, summary)

    return {}
    # cleaned_text = clean_text(request.txtId)
    # summary = create_summary(cleaned_text)
    # Assuming there's a store_summary() function to store the summary in a database

###not used we can remove it
def create_verdict_ratio(sql_session, txt_id, anthropic_client):
    logger.info(f'Got a request to create a ratio for txt_id {txt_id}')
    logger.warning(
        'ratio is not yet implemented, so creating an empty result (i.e. success with an empty ratio being stored )')

    ratio = ''  # TODO
    store_ratio(sql_session, txt_id, ratio)

    return {}
    # cleaned_text = clean_text(request.txtId)
    # summarized_ratio = create_ratio(cleaned_text)
    # Assuming there's a store_ratio() function to store the summarized ratio in a database


def get_txt_id_and_job_results(txt_ids):
    txt_id_result_list = list()
    [txt_id_result_list.append(TxtIdResult(txtId=tid, status=StatusEnum.started, message='for now everything ok')) for
     tid in txt_ids]
    job_result = JobResult(text_ids_results=txt_id_result_list, message='', txt_ids_with_null_ntoken='',
                           txt_ids_not_saved_in_aimetadata=[],
                           ids_with_pinecone_metadata_limit_error=[], chunks_amount=0,
                           retrieval_time=0, chunks_time=0, cohear_time=0, pincone_time=0,
                           sql_time=0, sql_session_execute_in_get_full_row_time=0, chunks_spliting_time=0,
                           cleaned_chunks_time=0, prepare_metadata_time=0, populate_text_metadata_time=0,
                           store_procedure_time=0,
                           fix_hebrew_encoding_time=0, time_token=0, total_time=0, total_api_time=0, status=200)
    return txt_id_result_list, job_result


def get_txt_id_and_ratio_results(txt_ids):
    txt_id_result_list = list()
    [txt_id_result_list.append(
        TxtIdRatioResult(txtId=tid, status=RatioStatusEnum.started, message='for now everything ok')) for tid in
     txt_ids]
    ratio_result = RatioResult(text_ids_results=txt_id_result_list, message='', status=200)
    return txt_id_result_list, ratio_result


def get_txt_id_and_master_chunk_results(txt_ids):
    txt_id_result_list = list()
    [txt_id_result_list.append(
        TxtIdMasterChunkResult(txtId=tid, status=MasterChunkStatusEnum.started, message='for now everything ok')) for
     tid in txt_ids]
    master_chunk_result = MasterChunkResult(text_ids_results=txt_id_result_list, message='', status=200)
    return txt_id_result_list, master_chunk_result

def get_txt_id_and_master_chunk_results_dict(txt_ids):
    txt_id_result_list = {}

    for tid in txt_ids:
        txt_id_result_list[tid] = TxtIdMasterChunkResult(txtId=tid, status=MasterChunkStatusEnum.started,
                                                         message='for now everything ok')
    master_chunk_result = MasterChunkResultBatch(text_ids_results=txt_id_result_list, message='', status=200)
    return txt_id_result_list, master_chunk_result


def get_txt_id_and_law_questions_results(txt_ids):
    txt_id_result_list = list()
    [txt_id_result_list.append(
        TxtIdLawQuestionsResult(txtId=tid, status=LawQuestionsStatusEnum.started, message='for now everything ok')) for
     tid in txt_ids]
    law_questions_result = LawQuestionsResult(text_ids_results=txt_id_result_list, message='', status=200)
    return txt_id_result_list, law_questions_result


def update_txtid_result_by_id(txt_id, txt_id_result_list, status, message):
    for txt_id_result in txt_id_result_list:
        if txt_id_result.txtId == txt_id:
            txt_id_result.status = status
            txt_id_result.message = message


def update_job_result_status_after_all_updates_in_txt_result(txt_id_result_list, job_result, txt_ids):
    ids_not_finish = [txt_id_result.txtId for txt_id_result in txt_id_result_list if
                      txt_id_result.status != StatusEnum.saved_in_sql]
    number_of_succeeded_txtIds = len(txt_ids) - len(ids_not_finish)
    if len(ids_not_finish) == 0:
        job_result.message = {
            'message_info': 'OK, the job succeeded',
            'received': len(txt_ids),
            'succeeded': number_of_succeeded_txtIds
        }
    else:
        mess = {
            'message_info': 'Oops, there are errors. You can follow them in the attached dataset.',
            'received': len(txt_ids),
            'succeeded': number_of_succeeded_txtIds
        }
        job_result.message = mess
        job_result.status = 500


def update_master_chunk_result_status_after_all_updates_in_txt_result(txt_id_result_list, job_result, txt_ids):
    ids_not_finish = [txt_id_result.txtId for txt_id_result in txt_id_result_list if
                      txt_id_result.status != MasterChunkStatusEnum.saved_in_sql]
    number_of_succeeded_txtIds = len(txt_ids) - len(ids_not_finish)
    if len(ids_not_finish) == 0:
        job_result.message = {
            'message_info': 'OK, the job succeeded',
            'received': len(txt_ids),
            'succeeded': number_of_succeeded_txtIds
        }
    else:
        mess = {
            'message_info': 'Oops, there are errors. You can follow them in the attached dataset.',
            'received': len(txt_ids),
            'succeeded': number_of_succeeded_txtIds
        }
        job_result.message = mess
        job_result.status = 500


def update_law_questions_result_status_after_all_updates_in_txt_result(txt_id_result_list, job_result, txt_ids):
    ids_not_finish = [txt_id_result.txtId for txt_id_result in txt_id_result_list if
                      txt_id_result.status != LawQuestionsStatusEnum.saved_in_sql]
    number_of_succeeded_txtIds = len(txt_ids) - len(ids_not_finish)
    if len(ids_not_finish) == 0:
        job_result.message = {
            'message_info': 'OK, the job succeeded',
            'received': len(txt_ids),
            'succeeded': number_of_succeeded_txtIds
        }
    else:
        mess = {
            'message_info': 'Oops, there are errors. You can follow them in the attached dataset.',
            'received': len(txt_ids),
            'succeeded': number_of_succeeded_txtIds
        }
        job_result.message = mess
        job_result.status = 500


def update_ratio_result_status_after_all_updates_in_txt_result(txt_id_result_list, job_result, txt_ids):
    ids_not_finish = [txt_id_result.txtId for txt_id_result in txt_id_result_list if
                      txt_id_result.status != RatioStatusEnum.save_in_airatio]
    number_of_succeeded_txtIds = len(txt_ids) - len(ids_not_finish)
    if len(ids_not_finish) == 0:
        job_result.message = {
            'message_info': 'OK, the job succeeded',
            'received': len(txt_ids),
            'succeeded': number_of_succeeded_txtIds
        }
    else:
        mess = {
            'message_info': 'Oops, there are errors. You can follow them in the attached dataset.',
            'received': len(txt_ids),
            'succeeded': number_of_succeeded_txtIds
        }
        job_result.message = mess
        job_result.status = 500


def update_job_result_chunks_time(job_result, chunks_time):
    job_result.chunks_time = round(chunks_time, 3)


def update_job_result_retrieval_time(job_result, retrieval_time):
    job_result.retrieval_time = round(retrieval_time, 3)


def update_job_result_cohear_time(job_result, cohear_time):
    job_result.cohear_time = round(cohear_time, 3)


def update_job_result_pincone_time(job_result, pincone_time):
    job_result.pincone_time = round(pincone_time, 3)


def update_job_result_sql_time(job_result, sql_time):
    job_result.sql_time = round(sql_time, 3)


def update_job_result_total_time(job_result, total_time):
    job_result.total_time = round(total_time, 3)


def update_job_result_chunks_amount(job_result, chunks_amount):
    job_result.chunks_amount = chunks_amount


def update_job_result_total_api_time(job_result, total_api_time):
    job_result.total_api_time = round(total_api_time, 3)


def update_job_result_sql_session_execute_in_get_full_row_time(job_result, sql_session_execute_in_get_full_row_time):
    job_result.sql_session_execute_in_get_full_row_time = round(sql_session_execute_in_get_full_row_time, 3)


def update_job_result_chunks_spliting_time(job_result, chunks_spliting_time):
    job_result.chunks_spliting_time = round(chunks_spliting_time, 3)


def update_job_result_cleaned_chunks_time(job_result, cleaned_chunks_time):
    job_result.cleaned_chunks_time = round(cleaned_chunks_time, 3)


def update_job_result_prepare_metadata_time(job_result, prepare_metadata_time):
    job_result.prepare_metadata_time = round(prepare_metadata_time, 3)


def update_job_result_populate_text_metadata_time(job_result, populate_text_metadata_time):
    job_result.populate_text_metadata_time = round(populate_text_metadata_time, 3)


def update_job_result_store_procedure_time(job_result, store_procedure_time):
    job_result.store_procedure_time = job_result.store_procedure_time + round(store_procedure_time, 3)


def update_job_result_time_token(job_result, time_token):
    job_result.time_token = job_result.time_token + round(time_token, 3)


def update_txt_ids_with_null_ntoken(job_result, txt_ids_with_null_ntoken):
    job_result.txt_ids_with_null_ntoken = txt_ids_with_null_ntoken


def update_txt_ids_not_saved_in_aimetadata(job_result, txt_ids_not_saved_in_aimetadata):
    job_result.txt_ids_not_saved_in_aimetadata = txt_ids_not_saved_in_aimetadata


def update_ids_with_pinecone_metadata_limit_error(job_result, ids_with_pinecone_metadata_limit_error):
    job_result.ids_with_pinecone_metadata_limit_error = ids_with_pinecone_metadata_limit_error


def update_job_resulte_with_error(error, txt_id_result_list, job_result, txt_ids):
    for txt_id_result in txt_id_result_list:
        txt_id_result.message = 'error'
    mess = {
        'message_info': f'{error}',
        'received': len(txt_ids),
        'succeeded': 0
    }
    job_result.message = mess
    job_result.status = 500



def process_txt_id(txt_id, ai_provider) -> Tuple[TxtIdMasterChunkResult, int, str]:
    master_chunk_result=TxtIdMasterChunkResult(txtId=txt_id)
    prompt=""

    ### Step 1: get total tokens
    try:
        magger_id, total_tokens = get_maagar_id_and_total_tokens_for_txt_id(txt_id)
        if not total_tokens:
            master_chunk_result.status,master_chunk_result.message = MasterChunkStatusEnum.started , f'Failed to get total tokens - {total_tokens}'
            return master_chunk_result,txt_id, prompt

        master_chunk_result.status,master_chunk_result.message = MasterChunkStatusEnum.get_total_tokens , f'get total token successfully - {total_tokens}'

    except Exception as e:
        master_chunk_result.status,master_chunk_result.message = MasterChunkStatusEnum.started , f'Failed to get total tokens - {e}'
        return master_chunk_result,txt_id, prompt


    ### Step 2: get text only
    try:
        if total_tokens > MASTER_CHUNK_MAX_TOTAL_TOKENS:
            child_text = get_verdict_child_text(txt_id)
            if isinstance(child_text, tuple):
                child_text = child_text[0]
            if child_text and child_text.strip():
                text_only = child_text
                master_chunk_result.status, master_chunk_result.message = MasterChunkStatusEnum.get_text_only, 'get text only successfully - from child text'

            else:
                text_only_and_full_text = get_text_only_and_full_text([txt_id])
                text_only = text_only_and_full_text[txt_id]['textOnly'][-180000:]
                logger.info(
                    f'txt_id - {txt_id} with total_tokens > {MASTER_CHUNK_MAX_TOTAL_TOKENS}: '
                    'child_text not found or empty, retrieved last 180K tokens from textOnly'
                )
        else:
            text_only_and_full_text = get_text_only_and_full_text([txt_id])
            text_only = text_only_and_full_text[txt_id]['textOnly']
            master_chunk_result.status, master_chunk_result.message = MasterChunkStatusEnum.get_text_only, 'get text only'

        if isinstance(text_only, tuple):
            text_only = text_only[0]
    except Exception as e:
        master_chunk_result.status, master_chunk_result.message = MasterChunkStatusEnum.get_total_tokens, f'Failed to get text only - {e}'

        return master_chunk_result,txt_id, prompt

    ### Step 3: create prompt
    try:
        prompt = ai_provider.provider_choose('master_chunk_haiku').prompt_template(text_only)
        master_chunk_result.status, master_chunk_result.message = MasterChunkStatusEnum.create_prompt, 'prompt created successfully'

    except Exception as e:
        master_chunk_result.status, master_chunk_result.message = MasterChunkStatusEnum.get_text_only, f'Failed to create prompt - {e}'


        return master_chunk_result,txt_id, prompt 

    return master_chunk_result,txt_id, prompt

def batch_update_txtid_result_by_id(txt_id ,txt_id_result_list, status, message):
        if txt_id == txt_id_result_list.keys():
            txt_id_result_list[txt_id].status = status
            txt_id_result_list[txt_id].message = message
        else:
            txt_id_result_list[txt_id] = TxtIdMasterChunkResult(txtId=txt_id, status=status, message=message)