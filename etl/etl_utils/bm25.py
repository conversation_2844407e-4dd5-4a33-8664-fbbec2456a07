from itertools import islice
import json

from pinecone.grpc import PineconeGRPC as Pinecone

from data_ops.pinecone_func import get_sparse_embedding, prep_line_from_law_metadata, prep_line_from_verdict_metadata
from app.middlewares.exceptions import verify_developers_choice
from db_utils.sql_utils import get_all_cids, insert_to_bm25_with_fifo, get_last_bm25_entry_and_save_in_local_file
from middlewares.logging_utils import app_logger as logger

from configs import app_config as config
from pinecone_text.sparse import BM25Encoder
import pinecone
import threading
import queue
from time import sleep
from configs.app_config import PINECONE_ENVIRONMENT
import datetime
from typing import List
from collections import Counter

def datetime_serializer(obj):
    if isinstance(obj, datetime.datetime):
        return obj.isoformat()

    elif isinstance(obj, datetime.date):
        return obj.isoformat()

    raise TypeError(f"Type {type(obj)} not serializable")

def get_dump_name(index_name, ext='jsonl'):
    return f'{index_name}_dump.{ext}'

def create_corpus_from_index_dump(index_name):
    prep_line = prep_line_from_law_metadata if index_name == config.LAW_INDEX_NAME else prep_line_from_verdict_metadata

    # Reading from the jsonl file
    with open(get_dump_name(index_name), 'r', encoding='utf-8') as jsonl_file:
        # Writing/appending to the txt file
        with open(get_dump_name(index_name, ext='txt'), 'a', encoding='utf-8') as txt_file:
            for line in jsonl_file:
                data = json.loads(line)
                assert len(data) == 1
                data = list(data.values())[0]
                # Performing operation on the data
                result = prep_line(data).replace('\n', ' ')  + '\n'  # to be sure each line is a new document in the corpus
                # Dumping the result into the txt file
                txt_file.write(result)

def fit_bm25_encoder_from_corpus(index_name, bm25_name):

    with open(get_dump_name(index_name, ext='txt'), 'r', encoding='utf-8') as txt_file:
        corpus = txt_file.readlines()

    # Initialize BM25 and fit the corpus
    bm25 = BM25Encoder()
    bm25.fit(corpus)
    # store BM25 params as json
    bm25.dump(bm25_name)

def read_n_lines(file, n):
    return list(islice(file, n))

def update_pinecone_with_sparse_encodings(index_name, n_consumers=20):
    MAX_QUEUE_SIZE = 1000  # block more puts until the the queue have room
    MAX_ATTEMPTS = 3
    assert n_consumers * 2 < MAX_QUEUE_SIZE
    is_law = index_name == config.LAW_INDEX_NAME
    # pinecone.init(api_key=os.environ['PINECONE_API_KEY_1'], environment=PINECONE_ENVIRONMENT)
    pinecone= Pinecone(api_key=os.environ['PINECONE_API_KEY_1'])

    index = pinecone.Index(index_name=index_name)
    embedding_queue = queue.Queue(maxsize=MAX_QUEUE_SIZE)
    update_queue = queue.Queue()

    # Producer Function
    def producer():
        with open(get_dump_name(index_name), 'r', encoding='utf-8') as jsonl_file:
            while True:
                jsonl_lines = read_n_lines(jsonl_file, 100)

                if not jsonl_lines:
                    break

                for line in jsonl_lines:
                    item = json.loads(line)
                    assert len(item) == 1
                    for key, val in item.items():
                        embedding = get_sparse_embedding(is_law, val, enc_type='document')
                        if embedding is None:
                            continue
                        while embedding_queue.qsize() >= MAX_QUEUE_SIZE - n_consumers-1: # to avoid deadlocks
                            sleep(0.5)
                        embedding_queue.put((key, embedding, 0))
        # Sentinel value to notify consumers to exit
        embedding_queue.put(None)

    # Consumer Function
    def consumer():
        while True:
            item = embedding_queue.get()
            if item is None:  # Sentinel value to exit
                break
            key, embedding, attempts = item
            try:
                # logger.info(f'Trying to update key: {key}')
                index.update(id=key, sparse_values=embedding)
                # logger.info(f'Done updating key: {key}')
            except Exception as e:
                # If update fails, requeue the item for another attempt
                logger.error(f"Error updating {key}: {e}")
                if attempts == MAX_ATTEMPTS:
                    logger.warning(f"----> {key} has reached max attempts so it will not be attempted again")
                embedding_queue.put((key, embedding, attempts+1))

        # each consumer will signal the next consumer to finish, so that if some of them failed and added a failed key again, they will not exit by encountering a stop signal before reaching the re-attempt. We will end up with one extra signal in the queue when we finish but that's ok
        embedding_queue.put(None)
        logger.info('Consumer is done!')

    # Start Producer Thread
    producer_thread = threading.Thread(target=producer)
    producer_thread.start()
    # Start Consumer Threads
    consumer_threads = []

    for _ in range(n_consumers):
        t = threading.Thread(target=consumer)
        t.start()
        consumer_threads.append(t)
    # Join all threads
    producer_thread.join()
    logger.info('Finished joining the producer')

    for t in consumer_threads:
        t.join()
        logger.info('Finished joining a consumer')
    
    logger.info('Done updating pinecone')

def train_bm25_encoder_update(index_name, new_documents=None, update_bm25=False, update_pinecone=True):
    bm25_name = config.BM25_LAW_PARAMS if index_name == config.LAW_INDEX_NAME else config.BM25_VERDICT_PARAMS
    bm25 = bm25_encoder_update() 
    if os.path.exists(bm25_name):
        bm25.load(bm25_name)

    else:
        raise FileNotFoundError("BM25 model file not found. Initial training might be required.")

    if new_documents and update_bm25:
        prep_line = prep_line_from_law_metadata if index_name == config.LAW_INDEX_NAME else prep_line_from_verdict_metadata
        preprocessed_documents = [prep_line(doc) for doc in new_documents]
        bm25.update(preprocessed_documents)
        # Save the updated BM25 model
        bm25.dump(bm25_name)

    if update_pinecone:
        # Ensure the user confirms the action
        verify_developers_choice()
        logger.info(f'Updating sparse vectors for the {index_name} index')
        update_pinecone_with_sparse_encodings(index_name)
        logger.info('Done updating sparse vectors.')

class bm25_encoder_update(BM25Encoder):

    def update_add(self, new_corpus: List[str]) -> "BM25Encoder":
        new_doc_freq_counter: Counter = Counter()
        sum_new_doc_len = 0
        new_docs_count = 0

        for doc in new_corpus:
            if not isinstance(doc, str):
                raise ValueError("new_corpus must be a list of strings")
            
            indices, tf = self._tf(doc)
            if len(indices) == 0:
                continue

            new_docs_count += 1
            sum_new_doc_len += sum(tf)
            # Count the number of new documents that contain each token
            new_doc_freq_counter.update(indices)

        # Update existing document frequency with new counts
        for idx, freq in new_doc_freq_counter.items():
            self.doc_freq[idx] = self.doc_freq.get(idx, 0) + freq
        
        # Update total documents count and average document length
        self.n_docs += new_docs_count
        total_doc_len = self.avgdl * (self.n_docs - new_docs_count) + sum_new_doc_len
        self.avgdl = total_doc_len / self.n_docs
        return self   

    def update_remove(self, removed_corpus: List[str]) -> "BM25Encoder":
        """Remove documents from the BM25 model, updating token frequencies and document count."""

        removed_doc_freq_counter = Counter()

        sum_removed_doc_len = 0
        removed_docs_count = 0

        for doc in removed_corpus:
            if not isinstance(doc, str):
                raise ValueError("removed_corpus must be a list of strings")

            indices, tf = self._tf(doc)

            if len(indices) == 0:
                continue

            removed_docs_count += 1
            sum_removed_doc_len += sum(tf)

            # Count the number of documents being removed containing each token
            removed_doc_freq_counter.update(indices)

        # Update existing document frequency by subtracting removed counts

        for idx, freq in removed_doc_freq_counter.items():

            if idx in self.doc_freq:
                self.doc_freq[idx] -= freq

                # Ensure document frequency doesn't become negative

                if self.doc_freq[idx] <= 0:
                    del self.doc_freq[idx]

        # Update total documents count and average document length
        if self.n_docs > removed_docs_count:
            self.n_docs -= removed_docs_count
            self.avgdl = ((self.avgdl * (self.n_docs + removed_docs_count)) - sum_removed_doc_len) / self.n_docs if self.n_docs > 0 else 0

        return self


async def train_bm25_encoder_add(maagar_id, run_id, new_documents=None, update_bm25=False, update_pinecone=True):
    from utils.cache_db import get_redis

    redis_client = await get_redis()
    bm25_name = get_last_bm25_entry_and_save_in_local_file(maagar_id)
    bm25 = bm25_encoder_update()  # Assuming bm25_encoder_update supports incremental updates
    bm25.load(bm25_name)

    if new_documents and update_bm25:
        # Select the appropriate preprocessing function based on the index name
        prep_line = prep_line_from_law_metadata if maagar_id == 1 else prep_line_from_verdict_metadata
        # Directly use the document dictionaries for preprocessing
        preprocessed_documents = [prep_line(doc) for doc in new_documents]
        # Update the BM25 model with preprocessed new documents
        bm25.update_add(preprocessed_documents)
        # Save the updated BM25 model
        bm25.dump(bm25_name)
        logger.info(bm25_name)

        with open(bm25_name, 'r') as file:
            json_data = json.load(file)        
        json_string = json.dumps(json_data)

        new_row_data =  {'runId': run_id, 'bm25': json_string, 'updatedAt' : datetime.date.today()}
        insert_to_bm25_with_fifo(maagar_id, new_row_data)

        # bm25_queue = Queue(connection=redis)

        message = {'run_id': run_id, 'maagarId' : maagar_id}
        await redis_client.publish('update_bm25', json.dumps(message))

        
async def train_bm25_encoder_remove(maagar_id, run_id, new_documents=None, update_bm25=False, update_pinecone=True):
    from utils.cache_db import get_redis

    redis_client = await get_redis()
    bm25_name = get_last_bm25_entry_and_save_in_local_file(maagar_id)
    bm25 = bm25_encoder_update()  # Assuming bm25_encoder_update supports incremental updates
    bm25.load(bm25_name)

    if new_documents and update_bm25:
        # Select the appropriate preprocessing function based on the index name
        prep_line = prep_line_from_law_metadata if maagar_id == 1 else prep_line_from_verdict_metadata
        # Directly use the document dictionaries for preprocessing
        preprocessed_documents = [prep_line(doc) for doc in new_documents]
        # Update the BM25 model with preprocessed new documents
        bm25.update_remove(preprocessed_documents)
        # Save the updated BM25 model
        bm25.dump(bm25_name)
        logger.info(bm25_name)

        with open(bm25_name, 'r') as file:
            json_data = json.load(file)        
        json_string = json.dumps(json_data)

        new_row_data =  {'runId': run_id, 'bm25': json_string, 'updatedAt' : datetime.date.today()}
        insert_to_bm25_with_fifo(maagar_id, new_row_data)

        # bm25_queue = Queue(connection=redis)

        message = {'run_id': run_id, 'maagarId' : maagar_id}
        await redis_client.publish('update_bm25', json.dumps(message))

async def train_bm25_encoder_update(maagar_id, run_id, new_documents=None, update_bm25=False, update_pinecone=True):
    from utils.cache_db import get_redis

    redis_client = await get_redis()
    bm25_name = get_last_bm25_entry_and_save_in_local_file(maagar_id)
    bm25 = bm25_encoder_update()  # Assuming bm25_encoder_update supports incremental updates
    bm25.load(bm25_name)
    
    if new_documents and update_bm25:
        # Select the appropriate preprocessing function based on the index name
        prep_line = prep_line_from_law_metadata if maagar_id == 1 else prep_line_from_verdict_metadata
        # Directly use the document dictionaries for preprocessing
        preprocessed_documents = [prep_line(doc) for doc in new_documents]
        # Update the BM25 model with preprocessed new documents
        bm25.update_remove(preprocessed_documents)
        bm25.update_add(preprocessed_documents)
        # Save the updated BM25 model
        bm25.dump(bm25_name)
        logger.info(bm25_name)

        with open(bm25_name, 'r') as file:
            json_data = json.load(file)        
        json_string = json.dumps(json_data)

        new_row_data =  {'runId': run_id, 'bm25': json_string, 'updatedAt' : datetime.date.today()}
        insert_to_bm25_with_fifo(maagar_id, new_row_data)

        # bm25_queue = Queue(connection=redis)

        message = {'run_id': run_id, 'maagarId' : maagar_id}
        await redis_client.publish('update_bm25', json.dumps(message))

def verdict_doc(chunk_metadata, txt_id):
    # This is a simplified parser. You need to replace this etl_utils with actual parsing based on your data format.
    return {
        'txt_id': txt_id,
        'cid': f'{txt_id}-0',
        'maagarId': chunk_metadata.maagarId,
        'total_tokens': chunk_metadata.total_tokens,
        'prosecutors': chunk_metadata.prosecutors.split(', '),
        'defenders': chunk_metadata.defenders.split(', '),
        'judges': chunk_metadata.judges.split(', '), 
        'representatives': chunk_metadata.representatives.split(', '),
        'title': chunk_metadata.title if chunk_metadata.title else '',
        'court_name': chunk_metadata.court_name,
        'procedures': chunk_metadata.procedures.split(', '), 
        'show_date': chunk_metadata.show_date,
        'location': chunk_metadata.location,
        'procedure_type': chunk_metadata.procedure_type.split(', '), 
        'pages': chunk_metadata.pages,
        'decision_name': chunk_metadata.decision_name,
        'ref_count': chunk_metadata.referrer_count
    }


def law_doc(chunk_metadata, txt_id):
    # This is a simplified parser. You need to replace this etl_utils with actual parsing based on your data format.
    return {
        'txt_id': txt_id,
        'cid': f'{txt_id}-0',
        'legislation_id': chunk_metadata.legislationId,
        'chapter_title': chunk_metadata.title if chunk_metadata.title else '',
        'provision_text': chunk_metadata.provisionTitle,
        'provision_title': chunk_metadata.provisionTitle, 
        'amendment_information': chunk_metadata.amendmentInformation,
        'section_numbers': chunk_metadata.sectionNumbers
    }
        
def parse_document(doc_str):
    # This is a simplified parser. You need to replace this etl_utils with actual parsing based on your data format.
    return {
        'procedures': [doc_str.split(' ')[0]],  # Example: Extract first word as a procedure
        'prosecutors': ['גויל בית חרשת לנייר ישראלי בעמ'],  # Manually specifying, replace with actual parsing etl_utils
        'defenders': ['ריקל בנין בעמ'],  # Manually specifying, replace with actual parsing etl_utils
        'representatives': ['א שטרוזמן'],  # Manually specifying, replace with actual parsing etl_utils
        'judges': [''],  # Assuming empty for the example
        'court_name': 'בית המשפט המחוזי',  # Manually specifying, replace with actual parsing etl_utils
        'title': ''  # Assuming empty for the example
    }


if __name__ == '__main__':
    import os
    from dotenv import load_dotenv

    load_dotenv(os.path.join('configs', '.env'))

    #train_bm25_encoder(config.LAW_INDEX_NAME, dump=False, create_corpus=False, fit_bm25=False, update_pinecone=False)

    #train_bm25_encoder(config.VERDICTS_INDEX_NAME, dump=True, create_corpus=True, fit_bm25=True, update_pinecone=True)
    #Update BM25
    new_documents = [
        "2511/93 המ 272/93 תא ריקל בנין בעמ נגד גויל בית חרשת לנייר ישראלי בעמ חב א.ד.ח.נ לבניה והנדסה בעמ  א שטרוזמן, בית המשפט המחוזי",
        "2511/93 המ 272/93 תא ריקל בנין בעמ נגד גויל בית חרשת לנייר ישראלי בעמ חב א.ד.ח.נ לבניה והנדסה בעמ  א שטרוזמן, בית המשפט המחוזי"
    ]
    # Convert document strings to dictionaries using parse_document
    new_document_dicts = [parse_document(doc) for doc in new_documents]
    # Pass the parsed documents (dictionaries) to the update function
    train_bm25_encoder_update(config.BM25_VERDICT_PARAMS, new_documents=new_document_dicts, update_bm25=True, update_pinecone=False)