import asyncio
import datetime
import json
import re
from collections import defaultdict
from typing import List, Dict

import nltk
import pandas as pd
from anthropic import HUMAN_PROMPT, AI_PROMPT
from sqlalchemy import text, delete

import configs.app_config as config
from etl.etl_dto import RatioStatusEnum, RatioResult
from utils.regex_util import clean_verdict_text, clean_text_aggressive
from etl.etl_utils.etl import update_txtid_result_by_id
from models import AiRatio
from middlewares.logging_utils import app_logger as logger
from db_utils.sql_utils import is_running_in_docker
from db_utils.sql_helper import fix_hebrew_encoding
from api.dependencies.relational_db import get_sql_session


def extract_case_from_procedure(original_procedure):
    # Apply the clean_verdict_text function to the original_procedure
    cleaned_procedure = clean_verdict_text(original_procedure)
    logger.info(f"Cleaned Procedure: {cleaned_procedure}")

    # Implement your etl_utils to extract case numbers from the cleaned procedure text
    # This regular expression looks for a sequence of digits, a slash, and then another sequence of digits
    match = re.search(r'\b\d{2,8}[-/]\d{2}\b', cleaned_procedure)
    if match:
        case_number = match.group()  # Assign the matched case number to the variable
        return case_number
    else:
        return ""  # Return an empty string if no case number is found


def create_dictionary_from_dataframe(df):
    dictionary = defaultdict(dict)

    for index, row in df.iterrows():
        case_number = extract_case_from_procedure(row["original_procedure"])
        referrer_id = row["referenced_id"]
        text = row["referenced_text"]
        # Clean the text
        cleaned_text = clean_text_aggressive(fix_hebrew_encoding(text))

        #print(f"Case Number: {case_number}, Referrer ID: {referrer_id}, Text: {cleaned_text}")

        dictionary[case_number][referrer_id] = cleaned_text

    return dictionary


# Define the CustomEncoder class for custom JSON encoding
class CustomEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, bytes):
            return obj.decode('utf-8')  # Handle bytes objects
        return super().default(obj)


def call_stored_procedure(documentId, count):
    df = None  # Initialize df to handle the UnboundLocalError

    if not isinstance(documentId, list):
        documentId = [documentId]

    documentId_str = ', '.join([str(num) for num in documentId])

    procedure_name = "[cmsHashavim].[dbo].[ai_sp_GetReferencedDocuments]"
    sql_expression = text(f"EXEC {procedure_name} @documentIds=:documentId, @count=:count")

    with get_sql_session() as sql_session:
        results = sql_session.execute(sql_expression, {"documentId": documentId_str, "count": count}).fetchall()

    df = pd.DataFrame(results, columns=list(results[0]._asdict().keys()))

    return df if df is not None else pd.DataFrame()


# Function to extract case numbers from the "referenced_text" column
def extract_case_from_referenced_text(referenced_text):
    # Clean the text
    referenced_text = clean_verdict_text(referenced_text)
    #print(f"Cleaned Referenced Text: {referenced_text}")

    # Implement your etl_utils to extract case numbers from the cleaned "referenced_text"
    # Assuming the case number format is "רעא 5070/13" where you want to consider only the numeric part "5070/13":
    match = re.search(r'\b\d{2,8}[-/]\d{2}\b', referenced_text)
    if match:
        return match.group()  # Return the matched case number
    else:
        return ""  # Return an empty string if no case number is found


# Function to replace hyphens with slashes in text
def replace_hyphen(text):
    return re.sub(r'\b\d{3,6}-\d{2}\b', lambda x: x.group().replace('-', '/'), text)

# Function to tokenize text into sentences
def find_sentences(text):
    return nltk.sent_tokenize(text)

#extract 2 sentences from each side
def extract_context_around_key(text, keyword):
    sentences = find_sentences(text)
    results = []
    for i, sent in enumerate(sentences):
        if keyword in sent:
            # Adjust the range to get two sentences before and two after the current one
            context = sentences[max(i - 2, 0):min(i + 3, len(sentences))]
            results.append(' '.join(context).strip())
    return results

#extract  1 sentences from each side

# # Function to extract context around a keyword in text
# def extract_context_around_key(text, keyword):
#     sentences = find_sentences(text)
#     results = []
#     for i, sent in enumerate(sentences):
#         if keyword in sent:
#             context = sentences[i - 1:i + 2]
#             results.append(' '.join(context).strip())
#     return results

def get_contexts_json(txt_id_result_list, ratio_result, documentId, count=100):
    logger.info(f"Document ID: {documentId}, Count: {count}")

    # Create the dictionary from the stored procedure call
    try:
        df = call_stored_procedure(documentId, count)
        [update_txtid_result_by_id(txt_id=id_, txt_id_result_list=txt_id_result_list, status=RatioStatusEnum.stored_procedure, message='call_stored_procedure') for id_ in documentId]
    except Exception as e:
        mess = f'Error in call_stored_procedure. The error message is {e}'
        [update_txtid_result_by_id(txt_id=id_, txt_id_result_list=txt_id_result_list, status=RatioStatusEnum.stored_procedure, message=mess[:450]) for id_ in documentId]
        raise RatioResult(text_ids_results=txt_id_result_list, message=mess, status_code=500)

    # print(df.head())
    try:
        # Create the dictionary from the DataFrame
        dictionary = create_dictionary_from_dataframe(df)
        [update_txtid_result_by_id(txt_id=id_, txt_id_result_list=txt_id_result_list, status=RatioStatusEnum.create_dictionary_from_dataframe, message='create_dictionary_from_dataframe') for id_ in documentId]
    except Exception as e:
        mess = f'Error in create_dictionary_from_dataframe. The error message is {e}'
        [update_txtid_result_by_id(txt_id=id_, txt_id_result_list=txt_id_result_list, status=RatioStatusEnum.create_dictionary_from_dataframe, message=mess[:450]) for id_ in documentId]
        raise RatioResult(text_ids_results=txt_id_result_list, message=mess, status_code=500)

    # Create a new dictionary with fixed encoding for values
    try:
        fixed_dictionary = {}
        for key, value in dictionary.items():
            if isinstance(value, str) and not is_running_in_docker():
                fixed_value = fix_hebrew_encoding(value)
            else:
                fixed_value = value  # If not a string, keep the original value
            fixed_dictionary[key] = fixed_value

        # Save the dictionary as a JSON file with 'utf-8' encoding
        # file_path = f'referring_updated_dic_{documentId}.json'
        # with open(file_path, 'w', encoding='utf-8') as json_file:
        #     json.dump(fixed_dictionary, json_file, ensure_ascii=False, cls=CustomEncoder)  # Use the CustomEncoder here

        # print(f"Dictionary saved to {file_path}")

        # Extract case numbers and fix texts
        case_to_referellas = {}
        for key, value in dictionary.items():
            case_number = extract_case_from_referenced_text(key)
            if case_number:
                case_number_replaced = replace_hyphen(case_number)
                case_to_referellas[case_number_replaced] = value

        # Create a dictionary to store context sentences
        contexts = defaultdict(dict)

        # Extract context around case numbers
        for case_num, referals in case_to_referellas.items():
            for ref_key, ref_text in referals.items():
                context_sentences = extract_context_around_key(ref_text, case_num)
                contexts[case_num][ref_key] = context_sentences

        # Save the contexts as a JSON file with UTF-8 encoding
        # contexts_file_path = f'context_updated_{documentId}.json'
        # with open(contexts_file_path, 'w', encoding='utf-8') as f:
        #     json.dump(contexts, f, ensure_ascii=False, cls=CustomEncoder)  # Use the CustomEncoder here

        # print(f"Contexts saved to {contexts_file_path}")
        [update_txtid_result_by_id(txt_id=id_, txt_id_result_list=txt_id_result_list, status=RatioStatusEnum.create_context, message='context created') for id_ in documentId]
    except Exception as e:
        mess = f'Error in create context from dictionary. The error message is {e}'
        [update_txtid_result_by_id(txt_id=id_, txt_id_result_list=txt_id_result_list, status=RatioStatusEnum.create_context, message=mess[:450]) for id_ in documentId]
        raise RatioResult(text_ids_results=txt_id_result_list, message=mess, status_code=500)

    return contexts

async def process_documents(document_ids):
    for document_id in document_ids:
        await asyncio.gather(get_contexts_json(document_id, count=300))

async def get_document_ids(document_ids):
    batch_size = 10
    tasks = []

    for i in range(0, len(document_ids), batch_size):
        batch = document_ids[i:i+batch_size]
        tasks.append(process_documents(batch))

    await asyncio.gather(*tasks)

def get_completion(client, prompt, max_tokens=1200, model=config.CHAT_COMPLETION_RATIO_MODEL, temperature=0, top_p=config.CLAUDE_ANSWER_TOP_P):
    return client.completions.create(
        prompt=prompt, max_tokens_to_sample=max_tokens, model=model, temperature=temperature, stop_sequences=['\n\nHuman:', '\n\nAssistant:'], top_p=top_p
    ).completion

def get_completion_and_save_in_airatio(txt_ids, ai_provider, CHAT_COMPLETION_RATIO_MODEL, CLAUDE_ANSWER_TOP_P, txt_id_result_list, ratio_result, run_id):
    insert_list = []
    json_file = get_contexts_json(txt_id_result_list, ratio_result, txt_ids)
    for index, txt_id in enumerate(txt_ids):

        try:
            root_verdict = list(json_file.keys())[index]
            [update_txtid_result_by_id(txt_id=id_, txt_id_result_list=txt_id_result_list, status=RatioStatusEnum.root_verdict, message='root_verdict') for id_ in txt_ids]
        except Exception as e:
            mess = f'Error in root_verdict. The error message is {e}'
            [update_txtid_result_by_id(txt_id=id_, txt_id_result_list=txt_id_result_list, status=RatioStatusEnum.root_verdict, message=mess[:450]) for id_ in txt_ids]
            raise RatioResult(text_ids_results=txt_id_result_list, message=mess, status_code=500)
        try:
            prompt = generate_prompt(root_verdict, json_file.get(root_verdict))
            [update_txtid_result_by_id(txt_id=id_, txt_id_result_list=txt_id_result_list, status=RatioStatusEnum.generate_prompt, message='generate_prompt') for id_ in txt_ids]
        except Exception as e:
            mess = f'Error in generate_prompt. The error message is {e}'
            [update_txtid_result_by_id(txt_id=id_, txt_id_result_list=txt_id_result_list, status=RatioStatusEnum.generate_prompt, message=mess[:450]) for id_ in txt_ids]
            raise RatioResult(text_ids_results=txt_id_result_list, message=mess, status_code=500)

        try:
            # summary = get_completion(anthropic_client, prompt, max_tokens=1200, model=CHAT_COMPLETION_RATIO_MODEL, temperature=0, top_p=CLAUDE_ANSWER_TOP_P)
            summary=ai_provider.provider_choose('ratio_haiku').generate_message(prompt)
            [update_txtid_result_by_id(txt_id=id_, txt_id_result_list=txt_id_result_list, status=RatioStatusEnum.get_completion, message='get_completion') for id_ in txt_ids]
        except Exception as e:
            mess = f'Error in get_completion. The error message is {e}'
            [update_txtid_result_by_id(txt_id=id_, txt_id_result_list=txt_id_result_list, status=RatioStatusEnum.get_completion, message=mess[:450]) for id_ in txt_ids]
            raise RatioResult(text_ids_results=txt_id_result_list, message=mess, status_code=500)

        try:
            item_to_insert =  {
                'txtId': txt_id,
                'summary': summary,
                'updatedAt' : datetime.date.today(),
                'version': CHAT_COMPLETION_RATIO_MODEL,
                'contexts': json.dumps(json_file.get(root_verdict), ensure_ascii=False),
                'runId': run_id
            }
            insert_list.append(item_to_insert)
            [update_txtid_result_by_id(txt_id=id_, txt_id_result_list=txt_id_result_list, status=RatioStatusEnum.create_insert_list, message='create_insert_list') for id_ in txt_ids]
        except Exception as e:
            mess = f'Error in create_insert_list. The error message is {e}'
            [update_txtid_result_by_id(txt_id=id_, txt_id_result_list=txt_id_result_list, status=RatioStatusEnum.create_insert_list, message=mess[:450]) for id_ in txt_ids]
            raise RatioResult(text_ids_results=txt_id_result_list, message=mess, status_code=500)

    try:
        with get_sql_session() as sql_session:
            stmt = (
                delete(AiRatio).
                where(AiRatio.txtId.in_(txt_ids))  # Match multiple values for deletion
            )
            sql_session.execute(stmt)
            sql_session.bulk_insert_mappings(AiRatio, insert_list)
            sql_session.commit()
        [update_txtid_result_by_id(txt_id=id_, txt_id_result_list=txt_id_result_list, status=RatioStatusEnum.save_in_airatio, message='save_in_airatio') for id_ in txt_ids]
    except Exception as e:
            mess = f'Error in save_in_airatio. The error message is {e}'
            [update_txtid_result_by_id(txt_id=id_, txt_id_result_list=txt_id_result_list, status=RatioStatusEnum.save_in_airatio, message=mess[:450]) for id_ in txt_ids]
            raise RatioResult(text_ids_results=txt_id_result_list, message=mess, status_code=500)


# def generate_prompt(root_verdict: str, records: Dict[str, List[str]]):
#     """
#     Generates a prompt for an AI model to summarize legal verdicts.

#     Parameters:
#     root_verdict (str): The root verdict ID.
#     records (Dict[str, List[str]]): A dictionary with referring verdict IDs and their texts.

#     Returns:
#     str: A formatted prompt for the AI.
#     """
#     # records_text = "\n".join([f"Referring Verdict ID: {str(verdict_id)}, Text: {', '.join(verdict_text)}" 
#     #                         for verdict_id, verdict_text in records.items()])
#     records_text = json.dumps(records.get(root_verdict), ensure_ascii=False)

#     prompt = f"""{HUMAN_PROMPT} As an AI legal assistant with expertise in legal texts, your task is to analyze a root verdict and its context in referring verdicts. 
#         The root verdict, mentioned in multiple referring verdicts, is accompanied by specific text excerpts. Your analysis should:
#         - Extract key points from the root verdict, using its context in the referring verdicts.
#         - Examine the text from these referring verdicts to understand how they relate to and contextualize the root verdict.
#         - Synthesize this information into a concise summary of the root verdict, integrating insights from its citations.
#         Aim for precision and impartiality to aid in clarifying the verdict's core aspects and its implications in later cases.
#         Format your response as follows: 
#         <root_verdict></root_verdict>
#         <summary></summary>
#         Note: Respond in Hebrew only.
#         {AI_PROMPT}
#         Context provided:
#         <context>
#         {records_text}
#         </context>
#     """

#     return prompt


# def generate_prompt(root_verdict: str, records: Dict[str, List[str]]):
#     """
#     Generates a prompt for an AI model to summarize legal verdicts, incorporating a system message for overarching guidance.

#     Parameters:
#     root_verdict (str): The root verdict ID.
#     records (Dict[str, List[str]]): A dictionary with referring verdict IDs and their texts.
#     system_message (str): A system message providing overarching instructions or context for the AI.

#     Returns:
#     str: A formatted prompt for the AI.
#     """
#     records_text = json.dumps(records.get(root_verdict), ensure_ascii=False)

#     prompt = (
#         "As an AI legal assistant, you specialize in analyzing and summarizing legal verdicts in Hebrew. "
#         "Your task is to focus on a specific root verdict, identified by its ID, and its context within referring verdicts. "
#         "Please follow these instructions:\n"
#         "- Analyze the root verdict by the referring verdicts to extract key legal points.\n"
#         "- Evaluate how the referring verdicts relate to and provide context for the root verdict.\n"
#         "- Synthesize your findings into a concise, impartial short summary, emphasizing the key aspects and implications of the root verdict. with focus on how other verdicts implement the desisions and principales of the main root verdict\n"
#         "Format your response in the following structure:\n"
#         f"<root_verdict>{root_verdict}</root_verdict>\n"
#         "<summary>Your Hebrew summary here.</summary>\n\n"
#         "Note: The response must be in Hebrew.\n\n"
#         f"{HUMAN_PROMPT}"
#         "Please write a clear summary in Hebrew for the root verdict below:\n\n"
#         "Context for Analysis:\n"
#         f"<context>\n{records_text}\n</context>\n\n"
#         f"{AI_PROMPT}"
#         f"[Following this sentence, I will continue in Hebrew.] Here are the main points and analysis in Hebrew for the root verdict {root_verdict}:\n"
#     )

#     return prompt

# def generate_prompt(root_verdict: str, records: Dict[str, List[str]]):
#     """
#     Generates a prompt for an AI model to summarize legal verdicts, incorporating a system message for overarching guidance.

#     Parameters:
#     root_verdict (str): The root verdict ID.
#     records (Dict[str, List[str]]): A dictionary with referring verdict IDs and their texts.
#     system_message (str): A system message providing overarching instructions or context for the AI.

#     Returns:
#     str: A formatted prompt for the AI.
#     """
#     records_text = json.dumps(records.get(root_verdict), ensure_ascii=False)

#     prompt = (
#                 """
#                 Your role as an AI legal assistant includes a specialized task of analyzing and summarizing Hebrew legal verdicts, with a unique format for receiving context. You will be provided with a structured context format containing a central root verdict, identified by its ID, alongside excerpts from referring verdicts. These excerpts are directly related to and provide context for the root verdict.
#                 write a Concise, Impartial Summarization: Synthesize your findings into a concise, impartial summary. incloude key aspects and implications of the root verdict, particularly focusing on how it is implemented and interpreted in the referring verdicts. Highlight the common themes, legal principles, and significant divergences or developments that emerge from the analysis.

#                 Format your response in the following structure:
#                 <root_verdict>7942/99</root_verdict>
#                 <summary>Your Hebrew summary here.</summary>

#                 Note: The response must be in Hebrew
#                 f"{HUMAN_PROMPT}"
#                 "Please write a clear summary in Hebrew for the root verdict below:\n\n"
#                 "Context for Analysis:\n"
#                 f"<context>\n{records_text}\n</context>\n\n"
#                 f"{AI_PROMPT}"
#                 f"[Following this sentence, I will continue in Hebrew.] Here are the main points and analysis in Hebrew for the root verdict {root_verdict}:\n"
#                 """
#     )

#     return prompt

def generate_prompt(root_verdict: str, records: Dict[str, List[str]]):
    """
    Generates a prompt for an AI model to summarize legal verdicts, incorporating a system message for overarching guidance.
    
    Parameters:
    root_verdict (str): The root verdict ID.
    records (Dict[str, List[str]]): A dictionary with referring verdict IDs and their texts.
    system_message (str): A system message providing overarching instructions or context for the AI.
    
    Returns:
    str: A formatted prompt for the AI.
    """
    records_text = json.dumps(records, ensure_ascii=False)

    prompt =f"""{HUMAN_PROMPT} Your task as an AI legal assistant involves analyzing and summarizing in Hebrew only,an israeli legal verdict. You will receive a specific format of context, which includes multiple excerpts from related verdicts that cite and refer to a central root verdict case number: {root_verdict}. Your role is to:
    1. understand the main points of the root verdict and Write a concise summary that extracts key aspects, legal principles, main points and implications of case number {root_verdict}.
    2.ignore the case facts
    3. explain how this case ({root_verdict}) is implemented and interpreted in the referring verdicts, Highlight common themes, legal principles, and significant divergences or developments that emerge from it
    4.The response must be in Hebrew.
    Here is the root verdict: {root_verdict}
    <context>\n{records_text}\n</context>
    {AI_PROMPT} [Following this sentence, I will continue in Hebrew.] Here are the main points and analysis in Hebrew for the root verdict: בתיק מספר  {root_verdict} נקבע:
            """
    return prompt

    # prompt =f"""{HUMAN_PROMPT} 

    #     מטרות:\n
    #     ניתוח משפטי: סקירה וזיהוי העקרונות המשפטיים המרכזיים בפסק הדין 
    #     {root_verdict} 
    #     ופסקי הדין המפנים אליו.
    #     על סמך פסקי דין שמתייחסים ,מצטטים ומתייחסים לפסק הדין
    #     {root_verdict} 
    #     תמצות וניתוח השפעה: כתיבת סיכום קצר בעברית המנתח כיצד פסק הדין 
    #     {root_verdict} 
    #     מיושם ומתפרש בפסקי הדין המפנים אליו, תוך הדגשת:
    #     - נושאים משותפים
    #     - עקרונות משפטיים
    #     - שינויים או התפתחויות משמעותיות הנובעות ממנו

    #     \n נתונים:
    #     להלן מאגר ציטוטים מפסקי דין ישראליים עם הפניות רבות לפסק דין 
    #     {root_verdict}
    #     \n
    #     {records_text}

    #     \n הנחיות:\
    #     - התמקד בעקרונות המשפטיים החשובים, ללא עובדות רקע.
    #     - הצג את העקרונות בעברית ברורה ותמציתית.
    #     - ציין מקורות לדוגמאות וטענותיך.
    #     \n
    #     שפה: עברית
    #     תוצאה רצויה:
    #     סיכום משפטי קצר המנתח את פסק הדין 
    #     {root_verdict} 
    #     והשפעתו על פסקי הדין המפנים אליו

    #     {AI_PROMPT} [Following this sentence, I will continue in Hebrew.] Here are the main points and analysis in Hebrew for the root verdict: בתיק מספר  {root_verdict} נקבע:
    #         """
    # return prompt




#    3. Highlight common themes, legal principles, and significant divergences or developments that emerge from this analysis.
# extract the main points of the root verdict and how later verdicts relate to and implement its principles.
#    2. Focus on how this case ({root_verdict}) is implemented and interpreted in the referring verdicts, Highlight common themes, legal principles, and significant divergences or developments that emerge from it.


# #    Context for Analysis enclose in <context></context> xml tags:

#     prompt =f"""{HUMAN_PROMPT} Your task as an AI legal assistant involves analyzing and summarizing in Hebrew only,an israeli legal verdict. You will receive a specific format of context, which includes multiple excerpts from related verdicts that cite and refer to a central root verdict case number: {root_verdict}. Your role is to:
#     1. understand the main points of the root verdict and Write a concise summary that extracts key aspects, legal principles, main points and implications of case number {root_verdict}.
#     2. explain how this case ({root_verdict}) is implemented and interpreted in the referring verdicts, Highlight common themes, legal principles, and significant divergences or developments that emerge from it
#     3.The response must be in Hebrew.
#     Here is the root verdict: {root_verdict}
#     <context>\n{records_text}\n</context>






#     # ###Your role as an AI legal assistant includes a specialized task of analyzing and summarizing Hebrew legal verdicts, with a unique format for receiving context. You will be provided with a structured context format containing a central root verdict, identified by its ID, alongside excerpts from referring verdicts. These excerpts are directly related to and provide context for the root verdict.
#     #             write a Concise, extract key aspects and implications of the root verdict, particularly focusing on how it is implemented and interpreted in the referring verdicts. Highlight the common themes, legal principles, and significant divergences or developments that emerge from the analysis.
#     #             Format your response in the following structure:
#     #             Note: The response must be in Hebrew and must contain short summary of how later verdicts relates and implements its prinsapels
