import json
import re

from pymongo.errors import BulkWriteError

from api.dependencies.mongo_db import get_mongo_client
from configs.app_config import MONGO_DB_AUTOCOMPLETE, MONGO_PROCEDURE_COLLECTION
from db_utils.sql_utils import get_chunk_metadata_rows
from models import AiMetadata
from api.dependencies.relational_db import get_sql_session
from app.middlewares.logging_utils import app_logger as logger


async def insert_new_txt_ids_to_mongodb(new_documents, mongo_client):
    """
    Insert only new txtId records into MongoDB, avoiding duplicates.
    """

    new_txt_ids = {}
    for txt_id_object in new_documents:
        new_txt_ids[txt_id_object["_id"]] = {"title": txt_id_object["title"],
                                             "procedures_objects": txt_id_object["procedures_objects"],
                                             "tribunal_id": txt_id_object["tribunal_id"],
                                             "message": "",
                                             "updated": True}

    try:
        collection = mongo_client[MONGO_DB_AUTOCOMPLETE][MONGO_PROCEDURE_COLLECTION]
        await collection.insert_many(new_documents, ordered=False)
        print("Inserted all non-duplicate records.")
    except BulkWriteError as e:
        for error in e.details["writeErrors"]:
            pro_id = error["op"]["_id"]
            new_txt_ids[pro_id]["updated"] = False
            new_txt_ids[pro_id]["message"] = error["errmsg"]

    except Exception as e:
        print(f"Error inserting records: {e}")
        for txt_id_object in new_txt_ids:
            new_txt_ids[txt_id_object["_id"]]["updated"] = False
            new_txt_ids[txt_id_object["_id"]]["message"] = str(e)


    return new_txt_ids


def extract_procedures_from_db_row(txt_ids_raw):
    new_data = []
    error = []

    for txt_id_number, key in txt_ids_raw.items():

        try:
            txt_id_raw = key
            decision_name = txt_id_raw.decision_name
            # show_date = txt_id_raw.show_date
            title = txt_id_raw.title
            procedures_objects = txt_id_raw.procedures_objects
            procedures_list = json.loads(procedures_objects)
            tribunal_id = txt_id_raw.tribunal_id

            new_txt_id_object = {"_id": str(txt_id_number), "title": title, "procedures_objects": [],
                                 "tribunal_id": tribunal_id}
            for procedure in procedures_list:
                procedure_type, procedure_number = procedure["pt"], procedure["pn"]
                procedure_type = re.sub(r'[^א-ת]', '', procedure_type)
                pn_clean = re.sub(r'[-/]', '', procedure_number)
                if not procedure_type or not procedure_number:
                    procedure_type, procedure_number = procedure["pn"], procedure["pt"]
                    procedure_type = re.sub(r'[^א-ת]', '', procedure_type)
                    procedure_number = re.sub(r'[^0-9]', '', procedure_number)
                    pn_clean = re.sub(r'[-/]', '', procedure_number)
                if not procedure_type or not procedure_number:
                    continue


                new_txt_id_object["procedures_objects"].append(
                    {"pt": procedure_type, "pn": procedure_number, "pn_normalized": pn_clean,
                               "dn": decision_name})


            new_data.append(new_txt_id_object)

        except Exception as e:
            print(f"Error processing txtId {txt_id_number}: {e}")
            error.append({"txt_id": str(txt_id_number), "error": str(e)})

    return new_data, error


async def upload_new_txtIds(txt_ids, mongo_connection):

    txt_ids_raw = get_chunk_metadata_rows(txt_ids)

    ##extract procedures from metadata
    new_data, error = extract_procedures_from_db_row(txt_ids_raw)
    ##insert new procedures to MongoDB
    result = await insert_new_txt_ids_to_mongodb(new_data, mongo_connection)
    return {"txt_ids": result, "error": error}


async def extract_procedure_to_mongo(session, mongo_connection, max_records=10000000,
                                     last_txt_id=0, batch_size=20000):
    """
    Fetch all txtId and procedures_objects from AiMetadata where maagarId = 2 in batches
    and save to separate JSON files in the given bucket_folder every `file_limit` records.

    :param session: SQLAlchemy session object
    :param bucket_folder: Path to the folder where JSON files will be stored
    :param batch_size: Number of rows to fetch per batch
    :param file_limit: Number of records per JSON file
    """
    logger.info("Starting extraction of procedures to MongoDB")
    record_success_mongo = 0
    record_count_sql = 0
    record_count_sql_distinict = 0
    duplicate_keys=0
    error = []
    try:
        while True and record_count_sql <= max_records:
            records = (
                session.query(AiMetadata.txtId, AiMetadata.procedures_objects, AiMetadata.decision_name,
                              AiMetadata.show_date,AiMetadata.title, AiMetadata.tribunal_id)
                .filter(AiMetadata.maagarId == 2)
                .filter(AiMetadata.txtId > last_txt_id)  # סינון אחרי txtId האחרון
                .order_by(AiMetadata.txtId)  # מיון לפי txtId
                .limit(batch_size)
                .all()
            )

            if not records:
                logger.info("No more records to process.")
                break
            records_dict = {}
            for row in records:
                records_dict[row[0]] = AiMetadata(txtId=row[0], procedures_objects=row[1], decision_name=row[2],
                                                  show_date=row[3],title=row[4],tribunal_id=row[5])

            record_count_sql += len(records)
            new_records, new_error = extract_procedures_from_db_row(records_dict)
            record_count_sql_distinict += len(new_records)

            error.extend(new_error)

            result = await insert_new_txt_ids_to_mongodb(new_records, mongo_connection)
            last_txt_id = records[-1].txtId
            duplicate_keys+= sum(1 for key, value in result.items() if not value["updated"])
            record_success_mongo += sum(1 for key, value in result.items() if value["updated"])

            print("Exported", record_count_sql_distinict, "records to MongoDB", record_success_mongo)
        return {"record_count_sql": record_count_sql,
                "exported_records_from_sql_distinct": record_count_sql_distinict, "last_processed_id": last_txt_id,
                "record_upload_to_mongo": record_success_mongo, "duplicate_keys_mongodb": duplicate_keys,
                "error": error}
    except Exception as e:
        print(e)
        logger.error("Error during extraction to MongoDB: %s", str(e))
        return {"error": str(e), "last_processed_id": last_txt_id,
                "record_success_mongo": record_success_mongo, "exported_records_from_Sql": record_count_sql}


async def delete_documents():
    mongo_connection = await get_mongo_client()
    collection = mongo_connection[MONGO_DB_AUTOCOMPLETE][MONGO_PROCEDURE_COLLECTION]
    # הקוד שלך שמבצע את המחיקה
    await collection.delete_many({"dn": "החלטה"})


if __name__ == '__main__':
    import asyncio

    session = get_sql_session()
    # client= await get_mongo_client()
    # asyncio.run(upload_new_txtIds(session, mongo_connection="", max_records=10, last_txt_id=0,
    #                                        batch_size=10))
    asyncio.run(delete_documents())
