import datetime
import os
import re
import traceback
from time import time
from typing import List, <PERSON>, Dict, Any, Tu<PERSON>

from anthropic import Anthropic
from sqlalchemy import delete, Row, text

import models
from utils.dto import ETLRequestStatusEnum
import app.models

from configs import app_config as conf, app_config as config
from configs.app_config import NAME_PREFIX_BAN_LIST
from db_utils.sql_helper import fix_hebrew_encoding
from middlewares.logging_utils import app_logger as logger
from api.dependencies.relational_db import get_sql_session
from utils.constants_util import HEBREW_KEYS

NAME_PREFIX_BAN_LIST_PATTERN = re.compile('(?:' + '|'.join(map(re.escape, NAME_PREFIX_BAN_LIST)) + ')[:,-]?(?:\s|$)')


def parse_table_name(table_name):
    # table name as DB_NAME/TABLE_NAME to executable query name
    return '[' + table_name.replace('/', '].[dbo].[') + ']'


def is_running_in_docker():
    return os.environ.get('RUNNING_IN_DOCKER') == 'True'


def remove_nikud(input_text):
    # text = "שָׁלוֹם"  # Hebrew for "Shalom" with Nikud
    # Unicode range of Hebrew Nikud: from 05B0 to 05C4.
    return ''.join([char for char in input_text if not 0x05B0 <= ord(char) <= 0x05C4])


def clean_single_name(name: str) -> str:
    try:
        # clean prefixes
        cleaned_name = re.sub(NAME_PREFIX_BAN_LIST_PATTERN, ' ', name.strip()).strip()
        # print(f"After removing prefixes: {cleaned_name}")  # Print after removing prefixes

        # clean suffixes (ad-hoc)
        cleaned_name = cleaned_name.replace('- נשיאה', '').replace('- נשיא', '') \
            .replace('- ס. נשיאה', '').replace('- ס. נשיא', '').replace('- אב"ד', '') \
            .replace('סגנית הנשיאה', '').replace('- הנשיאה', '').replace('- הנשיא', '').replace("בדימ'", '').replace(
            'יו"ר', '').replace('(בדימוס)', '').replace('‑ חבר', '')

        # clean punctuations and redundant spaces
        cleaned_name = cleaned_name.replace('.', " ").replace('"', '').replace('-', ' ').strip()
        cleaned_name = cleaned_name.replace("' ", ' ').replace("'", '').replace(')', '').replace('(', '')

        # remove left-over Dr. prefix
        if cleaned_name.startswith(' דר') and cleaned_name.count(' ') > 1:
            cleaned_name = cleaned_name[3:].strip()  # this is probably a doctor prefix that didn't use "

        # cleanup extra whitespaces and return
        cleaned_name = re.sub('\s+', ' ', cleaned_name)
        # print(f"Final cleaned name: {cleaned_name}")  # Print the final cleaned name

        # cleanup extra whitespaces and return
        return re.sub('\s+', ' ', cleaned_name)

    except Exception as e:
        logger.error(f'Failed in clean_single_name with name {name}, error: {e}')
        raise Exception(str(e) if str(e) in conf.ERROR_KEYS else 'CLEAN_SINGLE_NAME_ERROR')


def parse_names_list(names: str) -> List[str]:
    """
    Parse a names string as coming from the sql into a list of names (for judges, lawyers and sides).
    Currently NOT stripping the first name  to be only the first letter, 
    so exact matches could work when this data is present.

    Cleanups needed that we've seen (and dealing with):
    1. removing prefixes of names (עורך דין, עו"ד, פרופ, כבוד השופט...)
    2. first letter of the first name is without an apostrophe (')
    3. first letter of the first name is with a period (.) instead of an apostrophe (')
    4. First and last names are reversed with a comma (e.g. ברק, אהרון)
    5. names with abbreviations (e.g. עמיקם חרל"פ)
    6. sometimes the prefix כבוד השופט is missing a space before it ("כבוד המשנה לנשיאה א' ריבליןכבוד השופט א' גרוניס , כבוד השופט ח' מלצר , ")

    Cases we've seen (and dealing with):
    1. comma-separated list of judges (with full name or with first letter only in the first name)
    2. ; separated list of names

    Not dealt with (if it happens):
    0. list of names with ו in between each other (looks like it happens only in sides, not judges, so if we extend the use of this function to these cases, need to support)
    1. Comma separated with a final ו (e.g. אהרון ברק, בן גוזלן ורמי אנאף)
    2. we will fail in cases that it is a comma separated list of names that are also reversed with a comma (if such case ever happens)
    3. if the name here contains a middle name (e.g א א לוי) but the user forgot to use the middle name


    """  # TODO - should probably run this only once per verdict, and not for every verdict chunk..
    try:
        if len(names.strip()) == 0:
            return []  # empty names string

        orig_names = names

        # Remove Nikud
        names = remove_nikud(names)

        # Add spaces before 'כבוד השופט' just in case
        names = names.replace('כבוד השופט', ' כבוד השופט').strip()

        if ';' in names:
            clean_names = [clean_single_name(name) for name in names.split(';')]
        elif '|' in names:
            clean_names = [clean_single_name(name) for name in names.split('|')]
        elif ',' in names:
            clean_names = None
            if names.count(',') == 1:
                # Check if it is only one name with first and last names reversed by the comma
                clean_name = clean_single_name(names).split(',')
                if len(clean_name) <= 3:
                    # Best guess is that it is a single name
                    clean_name = ' '.join(clean_name[::-1])  # reverse it back
                    clean_name = clean_single_name(clean_name)
                    clean_names = [clean_name]
            if clean_names is None:  # didn't parse it before
                # Most likely two or more names, comma separated
                clean_names = [clean_single_name(name) for name in names.split(',')]
        elif ' ' in names:
            # Replace prefixes with a space and split on two or more spaces
            names_with_spaces = re.sub(NAME_PREFIX_BAN_LIST_PATTERN, ' ', names)
            split_names = re.split(r'\s{2,}', names_with_spaces)  # Split on two or more spaces
            clean_names = [clean_single_name(name) for name in split_names]
        else:
            # Most likely only a single name
            clean_names = [clean_single_name(names)]

        # Filter out empty names
        clean_names = [name for name in clean_names if len(name)]

        if any('כבוד השופט' in name for name in clean_names):
            # Handling non-separated judges
            new_clean_names = []
            for name in clean_names:
                if 'כבוד השופט' in name:
                    for n in name.split(' כבוד '):
                        new_clean_names.extend(parse_names_list(n))
                else:
                    new_clean_names.append(name)
            clean_names = new_clean_names

        # Additional warnings for potential parsing issues
        if any(name.count(' ') == 0 for name in clean_names):
            logger.warning(
                f'Warning: When parsing the given names ({orig_names}), found names that are missing first/last name. suggested parsing: {clean_names}')

        if any(name.count(' ') > 2 for name in clean_names):
            logger.warning(
                f'Warning: When parsing the given names ({orig_names}), found names that are more than 3 words long, which probably means it was not parsed correctly. suggested parsing: {clean_names}')

        return clean_names
    except Exception as e:
        logger.error(f'Failed in parse_names_list with names {names}, error: {e}')
        raise Exception(str(e) if str(e) in conf.ERROR_KEYS else 'PARSE_NAMES_LIST_ERROR')


def populate_text_verdicts_metadata(status, results_list, txt_ids, ai_provider, runId=''):
    from utils.regex_util import clean_text_aggressive
    from etl.etl_utils.etl_helpers import extract_lawyer_names
    from etl.etl_utils.etl_helpers import extract_side_names
    from etl.etl_utils.etl_helpers import clean_side_names
    import arrow
    import json

    to_remove = status == ETLRequestStatusEnum.remove
    client = Anthropic()
    insert_list = []
    for row in results_list:
        full_text = row['text']
        text = clean_text_aggressive(full_text)
        # token_count = client.count_tokens(text)
        token_count = ai_provider.provider_choose('master_chunk_haiku').tokenize(text)

        court_name = fix_hebrew_encoding(row.get('courtTypeName', '')) if not is_running_in_docker() else row.get(
            'courtTypeName', '')
        location = row.get('location', '')  # Replace 'Unknown' with a more appropriate default if needed
        location = 'בית המשפט העליון' if court_name == 'עליון' else location

        item_to_insert = {
            'txtId': row.get('txtId', None),
            'maagarId': row.get('maagarId', None),
            'runId': runId,
            'total_tokens': token_count,
            'prosecutors': json.dumps(
                extract_side_names(clean_text_aggressive(clean_side_names(row.get('prosecutors', '')))),
                ensure_ascii=False),
            'defenders': json.dumps(
                extract_side_names(clean_text_aggressive(clean_side_names(row.get('defenders', '')))),
                ensure_ascii=False),
            'judges': json.dumps(parse_names_list(row.get('judges', '')), ensure_ascii=False),
            'representatives': json.dumps(extract_lawyer_names(
                clean_text_aggressive(clean_side_names(row.get('representatives', '').replace('-', ' ')))),
                                          ensure_ascii=False),  # update it to be LIST type in table and here
            'title': clean_text_aggressive(row.get('Title', '')),
            'court_name': court_name,
            'procedures': json.dumps(row.get('Procedures', ''), ensure_ascii=False),
            'show_date': int(abs(arrow.get(row.get('showDate', '')).timestamp()) * 1000),
            'location': location,
            'procedure_type': json.dumps(row.get('procedureType', ''), ensure_ascii=False),
            'pages': row.get('Pages', ''),
            'decision_name': row.get('decisionName', ''),
            'referrer_count': row.get('referrerCount', 0),
            'side_names': row.get('side_names', '')
        }
        insert_list.append(item_to_insert)

    with get_sql_session() as sql_session:
        stmt = (
            delete(models.VerdictsAiMetadata).
            where(models.VerdictsAiMetadata.txtId.in_(txt_ids))  # Match multiple values for deletion
        )
        sql_session.execute(stmt)
        if not to_remove:
            sql_session.bulk_insert_mappings(models.VerdictsAiMetadata, insert_list)
        sql_session.commit()


def is_resource_a_law(row: Union[int, Dict[str, Any]]) -> bool:
    mid = int(row) if isinstance(row, int) else row.maagarId if isinstance(row, Row) else int(row.get("maagarId"))
    return mid == config.LAW_DOC_TYPE


def update_job_result_sql_session_execute_in_get_full_row_time(job_result, sql_session_execute_in_get_full_row_time):
    job_result.sql_session_execute_in_get_full_row_time = round(sql_session_execute_in_get_full_row_time, 3)


def update_job_result_fix_hebrew_encoding_time(job_result, fix_hebrew_encoding_time):
    job_result.fix_hebrew_encoding_time = round(fix_hebrew_encoding_time, 3)


def update_job_result_store_procedure_time(job_result, store_procedure_time):
    job_result.store_procedure_time = round(store_procedure_time, 3)


def get_full_row(job_result, txt_ids: List[int], raise_on_error=False) -> List[Dict[str, str]]:
    """
    Fetches rows from two joined tables based on the provided list of txt_ids.

    The function performs a JOIN operation on two tables: RESOURCES_MAIN_TABLE and RESOURCES_TEXT_TABLE,
    determined by the configuration. The result is filtered to fetch specific columns based on the `maagarId`:
    - When `maagarId` is 1: 'fullText' column from RESOURCES_TEXT_TABLE (typically used for laws).
    - When `maagarId` is 2: 'textOnly' column from RESOURCES_TEXT_TABLE (typically used for verdicts).

    Additional information is fetched for rows identified as 'verdicts'. The Hebrew text in the results
    is fixed for encoding issues.

    Parameters:
    - sql_session (session): The active SQLAlchemy session for executing the query.
    - txt_ids (List[int]): A list of txtId values for which rows are to be fetched.

    Returns:
    - List[Dict[str, str]]: A list of dictionaries, each representing a row from the result set.
      Each dictionary contains column names as keys and respective values as values.

    Note:
    For optimization, consider using the SQL IN syntax for fetching the extra verdict information
    to minimize multiple database calls.
    """
    # Create a query to join Table1 and Table2 based on txt_id across databases
    # while laws need the fullText to get the xml, verdicts need textOnly
    table1 = parse_table_name(config.RESOURCES_MAIN_TABLE)
    table2 = parse_table_name(config.RESOURCES_TEXT_TABLE)
    # select textOnly for verdicts (maagarId is 2) and fullText (xml) for laws
    # We don't retrieve both to save IO
    query = text(f"""
    SELECT 
        {table1}.*,
        CASE 
            WHEN ({table1}.[maagarId] = 1) THEN {table2}.[fullText]
            WHEN ({table1}.[maagarId] = 2) THEN {table2}.[textOnly]
            WHEN ({table1}.[maagarId] = 45) THEN {table2}.[textOnly]

            ELSE NULL  -- This is a default case, in case maagarId has other values.
        END AS text
    FROM {table1}
    JOIN {table2} ON {table1}.txtId = {table2}.txtId
    WHERE {table1}.txtId IN :target_txt_id_list
    """)
    # Execute the query and fetch the result
    # logger.info(f"in get_full_row function, Execute the query and fetch the result")
    t_in_get_full_row = time()
    with get_sql_session() as sql_session:
        results = sql_session.execute(query, {'target_txt_id_list': txt_ids}).fetchall()
    if not results:
        RESOURCES_TEXT_TABLE = 'cmsTakdin_FullText/cms_mainTxt'
        table1 = parse_table_name(config.RESOURCES_MAIN_TABLE)
        table2 = parse_table_name(RESOURCES_TEXT_TABLE)
        # select textOnly for verdicts (maagarId is 2) and fullText (xml) for laws
        # We don't retrieve both to save IO
        query = text(f"""
        SELECT 
            {table1}.*,
            CASE 
                WHEN ({table1}.[maagarId] = 1) THEN {table2}.[fullText]
                WHEN ({table1}.[maagarId] = 2) THEN {table2}.[textOnly]
                ELSE NULL  -- This is a default case, in case maagarId has other values.
            END AS text
        FROM {table1}
        JOIN {table2} ON {table1}.txtId = {table2}.txtId
        WHERE {table1}.txtId IN :target_txt_id_list
        """)
        t_in_get_full_row = time()
        with get_sql_session() as sql_session:
            results = sql_session.execute(query, {'target_txt_id_list': txt_ids}).fetchall()
    finish_t_in_get_full_row = time()
    sql_session_execute_in_get_full_row_time = finish_t_in_get_full_row - t_in_get_full_row
    logger.info(f'sql_session_execute_in_get_full_row {sql_session_execute_in_get_full_row_time}')
    update_job_result_sql_session_execute_in_get_full_row_time(job_result, sql_session_execute_in_get_full_row_time)
    results_list = [row._asdict() for row in results]

    results_dicts_verdic = [results_dict for results_dict in results_list if not is_resource_a_law(results_dict)]
    verdic_ids = [result_dict['txtId'] for result_dict in results_dicts_verdic]
    string_numbers = [str(num) for num in verdic_ids]
    txt_ids_str = ', '.join(string_numbers)
    ti = time()
    extra_verdict_info = get_verdict_extra_info(results_dicts_verdic, verdic_ids, txt_ids_str)
    store_procedure_time = time() - ti
    update_job_result_store_procedure_time(job_result, store_procedure_time)
    for dic in extra_verdict_info:
        for result_dict in results_list:
            if result_dict.get('txt_id') == dic.get('txt_id'):
                result_dict.update(dic)

    # TODO - the following can also be merged to using sql IN syntax to avoid multiple calls to the DB
    if not is_running_in_docker():
        errors = set()
        time_start = time()
        for i, results_dict in enumerate(results_list):
            for k in HEBREW_KEYS:
                if k in results_dict:
                    # if not is_running_in_docker():
                    try:
                        results_dict[k] = fix_hebrew_encoding(results_dict[k])
                    except UnicodeDecodeError as e:
                        # Log more detailed error information
                        txt_id = results_dict.get("txtId", "Unknown txt_id")
                        logger.error(f'Failed to fix the hebrew in txt_id: {txt_id}')
                        logger.error(f'Exception Type: {type(e).__name__}')
                        logger.error(f'Exception Message: {e}')
                        logger.error(f'Stack Trace: {traceback.format_exc()}')
                        errors.add(i)
        fix_hebrew_encoding_time = time() - time_start
        update_job_result_fix_hebrew_encoding_time(job_result, fix_hebrew_encoding_time)

        if len(errors) > 0:
            results_list = [r for i, r in enumerate(results_list) if i not in errors]

    return results_list


def get_verdict_extra_info(results_dicts_verdict, verdic_ids, txt_ids_str):
    procedure_name = "[cmsHashavim].[dbo].[ai_sp_GetDocumentsMetaData]"
    sql_expression = text(f"EXEC {procedure_name} @documentIds = :documentIds")

    with get_sql_session() as sql_session:
        results = sql_session.execute(sql_expression, {"documentIds": txt_ids_str}).fetchall()
    procedure_results_list = [row._asdict() for row in results]

    for txtId in verdic_ids:
        procedure_type_list, procedure_numbers_list, procedure_list = [], [], []
        data_per_txt_id = [result for result in procedure_results_list if result.get('txt_id') == txtId]
        for result in data_per_txt_id:
            procedure_type_name = result.get('procedureTypeName')
            procedure_number = result.get('procedureNumber')
            procedure_year = result.get('procedureYear')
            procedure_month = result.get('procedureMonth')

            procedure_type_list, procedure_numbers_list, procedure_list = get_procedure_lists(result,
                                                                                              procedure_type_name,
                                                                                              procedure_number,
                                                                                              procedure_year,
                                                                                              procedure_month, txtId,
                                                                                              procedure_type_list,
                                                                                              procedure_numbers_list,
                                                                                              procedure_list)

        verdict_data = {
            'txt_id': data_per_txt_id[0].get('txt_id', 0) if data_per_txt_id[0].get('txt_id') is not None else 0,
            'maagarName': data_per_txt_id[0].get('maagarName', '') if data_per_txt_id[0].get(
                'maagarName') is not None else '',
            'Title': data_per_txt_id[0].get('Title', '') if data_per_txt_id[0].get('Title') is not None else '',
            'brief': data_per_txt_id[0].get('brief', '') if data_per_txt_id[0].get('brief') is not None else '',
            'decisionName': data_per_txt_id[0].get('decisionName', '') if data_per_txt_id[0].get(
                'decisionName') is not None else '',
            'showDate': data_per_txt_id[0].get('showDate', '') if data_per_txt_id[0].get(
                'showDate') is not None else '',
            'referrerCount': data_per_txt_id[0].get('referrerCount', 0) if data_per_txt_id[0].get(
                'referrerCount') is not None else 0,
            'Pages': data_per_txt_id[0].get('Pages', 0) if data_per_txt_id[0].get('Pages') is not None else 0,
            'Procedures': procedure_list,
            'courtTypeName': data_per_txt_id[0].get('courtTypeName', '') if data_per_txt_id[0].get(
                'courtTypeName') is not None else '',
            'location': data_per_txt_id[0].get('location', '') if data_per_txt_id[0].get(
                'location') is not None else '',
            'judges': data_per_txt_id[0].get('judges', '') if data_per_txt_id[0].get('judges') is not None else '',
            'prosecutors': data_per_txt_id[0].get('prosecutors', '') if data_per_txt_id[0].get(
                'prosecutors') is not None else '',
            'defenders': data_per_txt_id[0].get('defenders', '') if data_per_txt_id[0].get(
                'defenders') is not None else '',
            'representatives': data_per_txt_id[0].get('representatives', '') if data_per_txt_id[0].get(
                'representatives') is not None else '',
            'procedureType': procedure_type_list,
            "procedure_numbers": procedure_numbers_list
        }
        missing_data_list = [data for data in verdict_data if
                             verdict_data.get(data) == '' or verdict_data.get(data) == 0 and verdict_data[
                                 data] != 'referrerCount']
        verdict_data.update({'missingDataList': missing_data_list})

        [result_dict.update(verdict_data) for result_dict in results_dicts_verdict if result_dict['txtId'] == txtId]

    return results_dicts_verdict

    # TODO res_dict already have referrerCount, however, until the bug in there is fixed and it becomes consistent, we know its value is not correct and we have to perform another query to get the actual number. Once it is fix, remove the following line as it is redundant and inefficient!
    # res_dict['referrerCount'] = len(get_resources_referrers_ids(sql_session, [txt_id])[txt_id])


def update_job_result_time_token(job_result, time_token):
    job_result.time_token = job_result.time_token + round(time_token, 3)



def is_court_name_or_procedure_type_is_in_list(court_name, procedure_object):
    if court_name in ["בית הדין המשמעתי הארצי של לשכת עורכי הדין", "בית הדין המשמעתי המחוזי של לשכת עורכי הדין"]:
        return 1
    for procedure in procedure_object:
        procedure_type, procedure_number = procedure["pt"], procedure["pn"]
        if procedure_type in ["בפ", "עלע", "עמלע"]:
            return 1

    return 0


def populate_text_metadata(results_list, chunks_per_txt_id, txt_ids, job_result, status, ai_provider, runId=''):
    to_remove = status == ETLRequestStatusEnum.remove
    from utils.regex_util import clean_text_aggressive
    from etl.etl_utils.etl_helpers import extract_lawyer_names
    from etl.etl_utils.etl_helpers import extract_side_names
    from etl.etl_utils.etl_helpers import clean_side_names
    from utils.regex_util import remove_unwanted_chars
    import arrow
    import json
    insert_list = []
    procedures_data_insert_list = []
    for row in results_list:
        for data in chunks_per_txt_id:
            if data.get('txtId') == row.get('txtId'):
                num_texts = len(data.get('chunks'))  # Count the number of cText entries
                aggregated_text = ' '.join(chunk for chunk in data.get('chunks'))
                time_token_start = time()
                token_count = ai_provider.provider_choose('master_chunk_haiku').tokenize(aggregated_text)

                time_token = time() - time_token_start
                update_job_result_time_token(job_result, time_token)

                # Adjust token_count
                token_count -= 100 * num_texts

                court_name = fix_hebrew_encoding(
                    row.get('courtTypeName', '')) if not is_running_in_docker() else row.get('courtTypeName', '')
                location = row.get('location', '')  # Replace 'Unknown' with a more appropriate default if needed
                location = 'בית המשפט העליון' if court_name == 'עליון' else location

                procedures_objects = []
                for procedure in row.get('Procedures', []):
                    pn, pt = procedure.split(' ', 1)
                    procedures_objects.append({'pt': pt, 'pn': pn})
                tribunal_id = is_court_name_or_procedure_type_is_in_list(court_name, procedures_objects)

                item_to_insert = {
                    'txtId': row.get('txtId') or row.get('txt_id', None),
                    'maagarId': row.get('maagarId', None),
                    'runId': runId,
                    'total_tokens': token_count,
                    'prosecutors': json.dumps(
                        extract_side_names(clean_text_aggressive(clean_side_names(row.get('prosecutors', '')))),
                        ensure_ascii=False),
                    'defenders': json.dumps(
                        extract_side_names(clean_text_aggressive(clean_side_names(row.get('defenders', '')))),
                        ensure_ascii=False),
                    'judges': json.dumps(parse_names_list(row.get('judges', '')), ensure_ascii=False),
                    'representatives': json.dumps(extract_lawyer_names(
                        clean_text_aggressive(clean_side_names(row.get('representatives', '').replace('-', ' ')))),
                                                  ensure_ascii=False),  # update it to be LIST type in table and here
                    'title': remove_unwanted_chars(row.get('Title') or row.get('txtTtl') or row.get('title', '')),
                    'court_name': court_name,
                    'procedures': json.dumps(row.get('Procedures', ''), ensure_ascii=False),
                    'show_date': int(abs(arrow.get(row.get('showDate', '')).timestamp()) * 1000),
                    'location': location,
                    'procedure_type': json.dumps(row.get('procedureType', ''), ensure_ascii=False),
                    'pages': row.get('Pages', ''),
                    'decision_name': row.get('decisionName', ''),
                    'referrer_count': row.get('referrerCount', 0),
                    'procedures_objects': json.dumps(procedures_objects, ensure_ascii=False),
                    'create_date': datetime.date.today(),
                    'tribunal_id': tribunal_id
                }
                item_to_insert['missing_data_list'] = json.dumps([data for data in item_to_insert if
                                                                  item_to_insert.get(data) == '' or item_to_insert.get(
                                                                      data) == 0 or item_to_insert.get(data) == [] and
                                                                  item_to_insert[
                                                                      data] != 'referrerCount' and data != 'missing_data_list'])

                insert_list.append(item_to_insert)

                # Populate procedures_data_insert_list with multiple rows for the same TxtId
                for procedure in procedures_objects:
                    procedures_data_insert_list.append({
                        'TxtId': row.get('txtId') or row.get('txt_id', None),
                        'Pt': procedure['pt'],
                        'Pn': procedure['pn']
                    })

    with get_sql_session() as sql_session:
        stmt = (
            delete(models.AiMetadata).
            where(models.AiMetadata.txtId.in_(txt_ids))  # Match multiple values for deletion
        )
        sql_session.execute(stmt)
        if not to_remove:
            sql_session.bulk_insert_mappings(models.AiMetadata, insert_list)

        # Delete existing rows in procedures_data table for the given txt_ids
        delete_stmt = text(f"DELETE FROM {config.AI_DATABASE_NAME}.dbo.procedures_data WHERE TxtId IN :txt_ids")
        sql_session.execute(delete_stmt, {"txt_ids": tuple(txt_ids)})

        # Insert data into procedures_data table using raw SQL
        if not to_remove and procedures_data_insert_list:
            insert_stmt = text(
                f"INSERT INTO {config.AI_DATABASE_NAME}.dbo.procedures_data (TxtId, Pt, Pn) VALUES (:TxtId, :Pt, :Pn)")
            sql_session.execute(insert_stmt, procedures_data_insert_list)

        sql_session.commit()


def get_procedure_lists(result, procedure_type_name, procedure_number, procedure_year, procedure_month, txtId,
                        procedure_type_list, procedure_numbers_list, procedure_list):
    from utils.regex_util import extract_procedure_from_query_text
    from utils.regex_util import normalize_case_nums

    if not procedure_type_name or not procedure_number or not procedure_year:
        procedures = result.get('Procedures', '')
        if procedures is not None:
            procedures_normalized = normalize_case_nums(procedures)
            if procedure_type_name and procedure_number:
                procedure_list.append(f'{procedure_number} {procedure_type_name}')
            elif procedure_type_name and not procedure_number:
                procedure_list.append(f'{procedures_normalized} {procedure_type_name}')
            else:
                procedure_list.append(procedures_normalized)
            procedure_list_in_split = procedures_normalized.split()
            procedure_numbers_list = [procedure_number] if procedure_number else [item for item in
                                                                                  procedure_list_in_split if
                                                                                  re.compile(r'\d+/').search(item)]
            procedure_type_list = [procedure_type_name] if procedure_type_name else [item for item in
                                                                                     procedure_list_in_split if
                                                                                     not re.compile(r'\d+/').search(
                                                                                         item)]
        else:
            title = result.get('Title', '')
            if title is not None:
                procedure_name_from_title = normalize_case_nums(extract_procedure_from_query_text(title))
                procedure_list.append(
                    f'{procedure_number} {procedure_type_name}') if procedure_type_name and procedure_number else procedure_list.append(
                    procedure_name_from_title)
                procedure_name_from_title_in_split = procedure_name_from_title.split()
                procedure_numbers_list = [procedure_number] if procedure_number else [item for item in
                                                                                      procedure_name_from_title_in_split
                                                                                      if
                                                                                      re.compile(r'\d+/').search(item)]
                procedure_type_list = [procedure_type_name] if procedure_type_name else [item for item in
                                                                                         procedure_name_from_title_in_split
                                                                                         if
                                                                                         not re.compile(r'\d+/').search(
                                                                                             item)]
            else:
                logger.error(f'Procedures and Title fileds are None for txt_id {txtId}')

    else:
        procedure_number_and_year = f'{procedure_number}/{procedure_year}' if not procedure_month else f'{procedure_number}-{procedure_month}-{procedure_year}'
        procedure = f'{procedure_number_and_year} {procedure_type_name}'
        procedure_numbers_list.append(procedure_number_and_year)
        procedure_list.append(procedure)
        procedure_type_list.append(procedure_type_name)

    return procedure_type_list, procedure_numbers_list, procedure_list


def get_verdict_child_text(txt_id):
    procedure_name = "[cmsHashavim].[dbo].[ai_sp_GetVerdictSummaryText]"
    sql_expression = text(f"EXEC {procedure_name} @documentId = :documentId")

    with get_sql_session() as sql_session:
        results = sql_session.execute(sql_expression, {"documentId": txt_id}).fetchall()

    if not results:
        logger.warning(f'No results found for txt_id {txt_id}')
        return None

    results_list = [row._asdict() for row in results]

    # Extract the 'ChildText' field from the first result
    child_text = fix_hebrew_encoding(results_list[0].get('ChildText')) if not is_running_in_docker() else \
    results_list[0]['ChildText'],

    return child_text


def populate_text_laws_metadata(status, results_list, txt_ids, runId=''):
    import json

    to_remove = status == ETLRequestStatusEnum.remove
    insert_list = []
    for row in results_list:
        item_to_insert = {
            'cId': row.get('cid', ''),
            'txtId': row.get('txt_id', 0),
            'runId': runId,
            'updatedAt': datetime.date.today(),
            'legislationId': row.get('legislation_id', 0),
            'title': row.get('title', ''),
            'provisionText': row.get('provision_text', ''),
            'chapterTitle': row.get('chapter_title', ''),
            'provisionTitle': row.get('provision_title', ''),
            'amendmentInformation': row.get('amendment_information', ''),
            'sectionNumbers': json.dumps(row.get('section_numbers', ''), ensure_ascii=False)
        }
        insert_list.append(item_to_insert)

    with get_sql_session() as sql_session:
        stmt = (
            delete(app.models.AiLawMetadata).
            where(app.models.AiLawMetadata.txtId.in_(txt_ids))  # Match multiple values for deletion
        )
        sql_session.execute(stmt)
        if not to_remove:
            sql_session.bulk_insert_mappings(app.models.AiLawMetadata, insert_list)
        sql_session.commit()


def populate_law_questions(txt_ids, questions_list):
    insert_list = []
    saved_in_sql = []
    for question in questions_list:
        item_to_insert = {
            'txtId': question,
            'questions': questions_list[question]
        }
        insert_list.append(item_to_insert)
        saved_in_sql.append(question)

    with get_sql_session() as sql_session:
        stmt = (
            delete(app.models.AiLawQuestion).
            where(app.models.AiLawQuestion.txtId.in_(txt_ids))
        )
        sql_session.execute(stmt)
        sql_session.bulk_insert_mappings(app.models.AiLawQuestion, insert_list)
        sql_session.commit()

    return saved_in_sql


def populate_master_chunk(txt_ids, chunk_list):
    from etl.etl_utils.etl_helpers import master_chunk_list_to_dict
    insert_list = []
    saved_in_sql_chunks = []

    for chunk in chunk_list:
        master_chunk_text = chunk_list[chunk].content[0].text
        # print("Original Text:", master_chunk_text)  # Debugging statement

        # Split the text into sections using regex
        sections = re.split(r'(?=\|\|(?:%%.+?%%|\w+%%))', master_chunk_text)
        # print("Sections after split:", sections)  # Debugging statement

        # Convert the list of sections into a dictionary
        master_chunk_text_dict = master_chunk_list_to_dict(sections)
        # print("Master Chunk Text Dictionary:", master_chunk_text_dict)  # Debugging statement

        output_tokens = chunk_list[chunk].usage.output_tokens

        # Fallback to original text if dictionary is empty
        if not master_chunk_text_dict:
            master_chunk_text_dict = {'raw_text': master_chunk_text}

        # Construct the item to insert
        item_to_insert = {
            'txtId': chunk,
            'row_text': str(master_chunk_text_dict),
            'facts': master_chunk_text_dict.get('summary_of_facts', ''),
            'legal_issues': master_chunk_text_dict.get('legal_questions', ''),
            'decsion': master_chunk_text_dict.get('court_final_decisions', ''),
            'court_ruling': master_chunk_text_dict.get('court_discussion', ''),
            'judges': master_chunk_text_dict.get('judges', ''),
            'opnion': master_chunk_text_dict.get('summarized_judges_opinions', ''),
            'phrases': master_chunk_text_dict.get('important_phrases', ''),
            'output_token': output_tokens
        }

        # print("Item to Insert:", item_to_insert)  # Debugging statement

        insert_list.append(item_to_insert)
        saved_in_sql_chunks.append(chunk)

    with get_sql_session() as sql_session:
        stmt = (
            delete(app.models.AiMasterChunk).
            where(app.models.AiMasterChunk.txtId.in_(txt_ids))
        )
        sql_session.execute(stmt)
        sql_session.bulk_insert_mappings(app.models.AiMasterChunk, insert_list)
        sql_session.commit()

    return saved_in_sql_chunks


def get_all_chunks_by_txt_id_sort_by_cid(txt_id):
    with get_sql_session() as sql_session:
        result = sql_session.query(app.models.AiEmbed.txtId, app.models.AiEmbed.cId, app.models.AiEmbed.cText,
                                   app.models.AiEmbed.nTokens).filter_by(txtId=txt_id).all()

    sorted_result = sorted(result, key=lambda row: int(row.cId.split('-')[1]))

    return sorted_result


def get_chunk_ids_for_doc(txt_ids: List[int]) -> Tuple[List, List[str]]:
    table = app.models.AiEmbed
    with get_sql_session() as sql_session:
        results = (sql_session.query(table)
                   .filter(table.txtId.in_(txt_ids))
                   .all())
    if len(results) == 0:
        return [], []  # None of the ids were not inserted before

    cids = [row.cId for row in results]
    logger.info(f'Given list of txt_ids have {len(cids)} old chunks')
    return results, cids
