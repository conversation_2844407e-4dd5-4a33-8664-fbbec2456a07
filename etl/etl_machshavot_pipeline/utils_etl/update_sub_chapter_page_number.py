import pandas as pd
import os
import re
import unicodedata
from collections import defaultdict

from etl.etl_machshavot_pipeline.load import AiBookSubChapterService, AiBookService
from etl.etl_machshavot_pipeline.load.load_book_json import AiBookJsonService
from api.dependencies.relational_db import get_sql_session


def normalize_text(text):
    """
    Normalize text for consistent formatting, retaining English, Hebrew, numbers, and specific punctuation.
    """
    text = unicodedata.normalize('NFKD', text)
    text = text.lower()
    text = re.sub(r'[^a-zA-Z0-9\u05D0-\u05EA\._\s\/\\]', '', text)
    text = text.replace('\n', ' ').replace('\r', ' ').replace('\t', ' ')
    text = re.sub(r'\s+', ' ', text)
    text = text.strip()
    return text


def get_chunks(text, chunk_size=8):
    """
    Divide the text into overlapping chunks of 'chunk_size' words for better matching.
    """
    words = text.split()
    for i in range(len(words) - chunk_size + 1):
        yield ' '.join(words[i:i + chunk_size])


def update_with_page_numbers(sub_chapters_rows, book_text, book_id):
    # Load the Excel file with subchapter text
    data = []
    for sub_chapter in sub_chapters_rows:
        row = sub_chapter.__dict__.copy()  # Copy to avoid modifying the original object directly
        row.pop('_sa_instance_state', None)  # Remove the SQLAlchemy internal state attribute
        data.append(row)
    ##sort subChapterNumber by the subChapterNumber
    book_rows = pd.DataFrame(data).sort_values('subChapterNumber').reset_index(drop=True)
    # Ensure 'startPageNumber' column exists and initialize to 0 if not found
    book_rows['startPageNumber'] = 0

    unmatched_subchapters = []

    normalized_content = normalize_text(book_text)

    # Identify page markers and their positions in the normalized content
    page_pattern = r'page[-_](\d+)\.png'
    page_markers = list(re.finditer(page_pattern, normalized_content))
    page_positions = [(match.start(), int(match.group(1))) for match in page_markers]

    last_position = 0  # Start position for searching within the content

    for index, row in book_rows.iterrows():
        sub_chapter_text = str(row['subChapterText']).strip()
        normalized_sub_chapter = normalize_text(sub_chapter_text)
        found = False

        # Search through chunks of normalized subChapterText in the book's content
        for chunk in get_chunks(normalized_sub_chapter, chunk_size=6):
            position = normalized_content.find(chunk, last_position)
            if position != -1:
                last_position = position + len(chunk)
                # Find the page number corresponding to this position
                page_number = next((pn for pos, pn in reversed(page_positions) if pos <= position), None)
                if page_number is not None:
                    # Update startPageNumber in the DataFrame for the current row
                    book_rows.at[index, 'startPageNumber'] = page_number
                    found = True
                    break
        if not found:
            # Set startPageNumber to 0 for unmatched subchapters
            book_rows.at[index, 'startPageNumber'] = 0
            unmatched_subchapters.append({
                'book_id': book_id,
                'sub_chapter_id': row['subChapterId'],
                'sub_chapter_text': normalized_sub_chapter,  # Use normalized text for unmatched output
                'assigned_page': 0
            })
            # print(
            #     f"No match found for normalized subchapter '{normalized_sub_chapter[:20]}', setting startPageNumber to 0")

    return book_rows, unmatched_subchapters


if __name__ == '__main__':
    book_id = 'dfa5f1ce-82df-4238-be57-e03f9d8e2524'
    # book_id = '60d9a07b-8311-4d5f-bb24-69cd9ff0d072'
    response_template = []
    BooksDoesntStartOnPage1 = {
        "422a0872-31bf-4244-8fe1-18329ab86b96": {"start_page": 247, "end_page": 340},
        "32501afc-72ef-4831-a94e-26b1abdd4b49": {"start_page": 223, "end_page": 286},
        "87bc806a-92f5-4d51-90c7-538076d4a65e": {"start_page": 175, "end_page": 239},
        "dfa5f1ce-82df-4238-be57-e03f9d8e2524": {"start_page": 197, "end_page": 296},
        "5055abd2-a55e-4155-bca8-5b3c7b247e51": {"start_page": 297, "end_page": 310},
        "e3372213-a917-4b06-b666-897293631367": {"start_page": 311, "end_page": 318}
    }
    books_that_not_start_with_page_number_1 = {

    }
    db = get_sql_session()
    book_list=AiBookService(db).get_ai_book(0, 0)
    for book in book_list:
        book_id = book.bookId
        sub_chapters_text = AiBookSubChapterService(db).get_sub_chapters_by_book_id(book_id)
        book_text = AiBookJsonService(db).get_book_data_by_id(book_id).bookText
        book_rows, unmatched_subchapters = update_with_page_numbers(sub_chapters_text, book_text, book_id)
        print(book_id, len(unmatched_subchapters))
        AiBookSubChapterService(db).bulk_update_specific_columns(book_rows.to_dict(orient='records'), ['startPageNumber'])
