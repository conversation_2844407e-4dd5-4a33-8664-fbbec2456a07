import json

import anthropic
import requests
from anthropic.types.beta.message_create_params import MessageCreateParamsNonStreaming
from anthropic.types.beta.messages.batch_create_params import Request
from sqlalchemy import or_

from etl.etl_machshavot_pipeline.load import AiBookSubChapterService
from models import AiBookSubChapter, AiEntityRelations
from api.dependencies.relational_db import get_sql_session

ANTHROPIC_API_KEY= '************************************************************************************************************'

client = anthropic.Anthropic(
    api_key=ANTHROPIC_API_KEY)


def anthropic_batches_question(questions):
    requests_list = []
    for question in questions:
        temp_request = Request(
            custom_id=str(question["sub_chapter_id"] + "_1"),
            params=MessageCreateParamsNonStreaming(
                model="claude-3-5-sonnet-20241022",
                max_tokens=8192,
                temperature=0,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": f"""You are a specialized text formatting assistant tasked with converting unformatted law exam questions into well-structured Markdown. Your primary focus is on Hebrew text\n\nHere is the question text you need to format:\n<question_title>\n{question["title"]}\n</question_title>\n\n<question_text>\n{question["question_text"]}\n</question_text>\n\nPlease follow these steps to analyze and format the text:\n\n5. Apply the following Markdown formatting rules:\n   - Format the title as a first-level header (# Title)\n   - Format the question number as a first-level header (# [number])\n   - Use fourth-level headers (####) ONLY when explicitly present in the original text\n   - Separate options with newlines\n   - DO NOT add section labels, headers  that aren't in the original text\n   - Ensure the result is valid Markdown\n\n7. STRICT RULE: The output should contain ONLY text that appears in the original question. Do not add:\n   - Section labels (like \"תשובות\", \"אפשרויות\", etc.)\n   - Explanatory headers\n   - Navigation text\n   - Any additional words or characters\n\nThe output should follow this structure:\n\n# [Title]\n\n[First paragraph]\n\n[Second paragraph]\n\n**[The actual final question / instructions]** in Bold\n\n#### [Questions/Additional sections]\n\n- [Option / Question 1]\n- [Option / Question 2]\n- [Option / Question 3]\n\n[Additional paragraphs if needed]\n\n\nIMPORTANT: Do not add ANY words, labels, or text that don't appear in the original text, including section headers, additional numbering or labeling, explanatory text, or transition words.\n\nPlease proceed with analyzing and formatting the given question text."""

                            }
                        ]
                    }
                ]
            )
        )

        requests_list.append(temp_request)

    req = client.beta.messages.batches.create(requests=requests_list)
    print(req)
    return req


def extract_batch(batch_id):
    headers = {
        "anthropic-version": "2023-06-01",
        "x-api-key": ANTHROPIC_API_KEY,
        "anthropic-beta": "message-batches-2024-09-24",
    }
    results_url = f"https://api.anthropic.com/v1/messages/batches/{batch_id}/results"

    response = requests.get(results_url, headers=headers)
    if response.status_code == 200:
        with open("batch_results.jsonl", "wb") as file:
            file.write(response.content)
        return response.content


def extract_question():
    db = get_sql_session()
    from sqlalchemy.orm import aliased
    import json

    # Define the output structure
    question_list = []

    # Query setup
    relations_alias = aliased(AiEntityRelations)  # Alias for clarity if needed
    questions = (
        db.query(
            AiBookSubChapter.subChapterText.label("question_text"),
            AiBookSubChapter.subChapterId.label("sub_chapter_id"),
            AiBookSubChapter.subChapterTitle.label("sub_chapter_title"),
            relations_alias.relationMetadata.label("metadata")
        )
        .join(
            relations_alias,
            AiBookSubChapter.subChapterId == relations_alias.entityId1
        )
        .filter(
            or_(
                relations_alias.entityType1 == 2,
                relations_alias.entityType1 == 5
            )
        )
        .distinct(AiBookSubChapter.subChapterId)  # Ensure unique subChapterId

        .all()
    )

    # Process the results
    for question in questions:
        try:
            # Extract metadata
            metadata = json.loads(question.metadata)
            exam_number = metadata.get("exam_number", 0)
            question_number = metadata.get("question_number", 0)

            # Create the output dictionary
            question_dict = {
                "question_text": question.question_text,
                "sub_chapter_id": str(question.sub_chapter_id),
                "sub_chapter_title": question.sub_chapter_title,
                "exam_number": int(exam_number),
                "question_number": int(question_number),

            }
            if question_dict["exam_number"] == 0 or question_dict["question_number"] == 0:
                # שאלה מס 4 שאלות לתרגול
                question_dict["title"] = f"שאלה מס {question_dict['question_number']} שאלות לתרגול"
            else:
                # שאלה מס 4 מבחן 5

                question_dict[
                    "title"] = f"שאלה מס {question_dict['question_number']} מבחן {question_dict['exam_number']}"

            question_list.append(question_dict)
        except json.JSONDecodeError:
            # Handle invalid JSON in metadata
            continue

    # Output the result
    ##store to csv
    # with open('question_list.json', 'w') as f:
    #     json.dump(question_list, f, ensure_ascii=False, indent=4)
    return question_list

def upload_question_to_db(fp):
    #subChapterTextDisplay
    file_path = fp

    # Initialize a list to store the results
    db=get_sql_session()
    with open(file_path, "r") as file:
        data = json.load(file)
        data_rows = [{"subChapterId": item["custom_id"].split("_")[0], "subChapterTextDisplay": item["result"]["message"]["content"][0]["text"]} for item in data]
        print(data_rows)
        AiBookSubChapterService(db).bulk_update_specific_columns(data_rows, ['subChapterTextDisplay'])

def load_jsonl(fp,fp_out):
    import json

    # Path to your JSONL file
    jsonl_file_path = fp

    # Load JSONL into a JSON (list of dictionaries)
    with open(jsonl_file_path, "r") as file:
        json_data = [json.loads(line) for line in file]

    # Save as a single JSON file if needed
    json_file_path = fp_out
    with open(json_file_path, "w") as file:
        json.dump(json_data, file, indent=4, ensure_ascii=False)

    # Print the loaded JSON
    print(json_data)


def copy_html_pages(book_id_target,book_id_source,start_page,end_page):
    db = get_sql_session()
    from models import AiBooksHtmlPages
    # Query the source pages

    from sqlalchemy import insert, select

    # Step 1: Select pages from the source book
    source_pages = db.execute(
        select(AiBooksHtmlPages.page, AiBooksHtmlPages.html)
        .where(AiBooksHtmlPages.bookId == book_id_source)
        .where(AiBooksHtmlPages.page >= start_page)
        .where(AiBooksHtmlPages.page <= end_page)
    ).fetchall()

    # Step 2: Insert into the target book
    if source_pages:
        insert_data = [
            {"bookId": book_id_target, "page": record.page, "html": record.html}
            for record in source_pages
        ]

        db.execute(insert(AiBooksHtmlPages).values(insert_data))
        db.commit()

        print(f"Copied {len(insert_data)} pages from book {book_id_source} to {book_id_target}.")
    else:
        print("No pages found to copy.")

    return {"status": "success"}


if __name__ == '__main__':
    # questions_list = extract_question()
    # res = anthropic_batches_question(questions_list)
    # print(res)
    batch_id="msgbatch_01S1Z4XVbqihkJUYaqD8Ts9Y"
    # print(extract_batch(batch_id))
    fp=None
    # upload_question_to_db(fp)
    book_source_id = 'aa416f55-8b80-4ae2-b377-154a94af2098'
    book_target_id = '422a0872-31bf-4244-8fe1-18329ab86b96'
    start_page = 247
    end_page = 340
    print(copy_html_pages(book_target_id,book_source_id,start_page,end_page))
