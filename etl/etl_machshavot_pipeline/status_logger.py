import json
import sys
import time
import logging
from functools import wraps, cache
from io import StringIO

logger = logging.getLogger(__name__)

class StatusTracker:
    def __init__(self):
        self.status = {
            "start_time": time.time(),
            "functions": []
        }

    def add_status(self, func_name: str, state: str,time_duration=None, additional_info=None):
        """Add status of a function call to the tracker."""
        func_status = {
            "function_name": func_name,
            "state": state,
            "timestamp": time_duration,
            "additional_info": additional_info
        }

        
        self.status["functions"].append(func_status)

    def to_json(self):
        return json.dumps(self.status, indent=2)

    def get_status(self):
        return self.status

def log_status_with_tracker(func):
    """Decorator to log the start, end, and status using the shared StatusTracker."""
    @wraps(func)
    def wrapper(self, *args, **kwargs):
        func_name = func.__name__
        start_time = time.time()
        # self.status_tracker.add_status(func_name, "started")
        logger.info(f"Starting {func_name}...")


        try:
            result = func(self, *args, **kwargs)

            self.status_tracker.add_status(func_name, "completed", {
                "duration": time.time() - start_time
            },result)
            logger.info(f"Completed {func_name} in {time.time() - start_time:.2f} seconds")
            return result

        except Exception as e:
            self.status_tracker.add_status(func_name, "failed", {"error": str(e)})
            logger.error(f"Error in {func_name}: {e}")
            return []  # Return or handle error as needed

    return wrapper
