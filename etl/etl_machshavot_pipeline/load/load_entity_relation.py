import json

from datetime import datetime

from sqlalchemy.orm import Session

from utils.dto import AiLoadEntityRelationEnum, SubChapterEnum
from app.models import AiEntityRelations  # Import AiBookChapter

from api.dependencies.relational_db import get_sql_session


class AiLoadEntityRelation:
    def __init__(self, db: Session):
        self.db = db



    def bulk_create_ai_entity_relations(self, sub_chapters, book_id):
        relations = {}
        for sub_chapter in sub_chapters:
            key_num = sub_chapter['exam_number'] + "-" + sub_chapter["meta_data"]["question_number"]
            if key_num not in relations:
                relations[key_num] = {}

            relations[key_num][sub_chapter['sub_chapter_type']] = str(sub_chapter['sub_chapter_id'])
            relations[key_num]["meta_data"] = {"exam_number": sub_chapter['exam_number'],
                                               "question_number": sub_chapter["meta_data"]["question_number"]}

        entity_relations = []
        for exam in relations:
            if "question" in relations[exam] and "short_answer" in relations[exam]:
                entity_relations.append({"entityId1": relations[exam]["question"], "entityType1": SubChapterEnum.question,
                                         "entityId2": relations[exam]["short_answer"], "entityType2": SubChapterEnum.short_answer,
                                         "relationType": AiLoadEntityRelationEnum.question_short_answer,
                                         "relationMetadata": relations[exam]["meta_data"]})
            if "question" in relations[exam] and "long_answer" in relations[exam]:
                entity_relations.append({"entityId1": relations[exam]["question"], "entityType1": SubChapterEnum.question,
                                         "entityId2": relations[exam]["long_answer"], "entityType2": SubChapterEnum.long_answer,
                                         "relationType": AiLoadEntityRelationEnum.question_long_answer,
                                         "relationMetadata": relations[exam]["meta_data"]})
            if "question_mc" in relations[exam] and "short_answer" in relations[exam]:
                entity_relations.append({"entityId1": relations[exam]["question_mc"], "entityType1": SubChapterEnum.question_mc,
                                         "entityId2": relations[exam]["short_answer"], "entityType2": SubChapterEnum.short_answer,
                                         "relationType": AiLoadEntityRelationEnum.question_mc_short_answer,
                                         "relationMetadata": relations[exam]["meta_data"]})
            if "question_mc" in relations[exam] and "long_answer" in relations[exam]:
                entity_relations.append({"entityId1": relations[exam]["question_mc"], "entityType1": SubChapterEnum.question_mc,
                                         "entityId2": relations[exam]["long_answer"], "entityType2": SubChapterEnum.long_answer,
                                         "relationType": AiLoadEntityRelationEnum.question_mc_long_answer,
                                         "relationMetadata": relations[exam]["meta_data"]})

        db_relation = [AiEntityRelations(
            entityId1=relation.get('entityId1', None),  # Default to None if key is missing
            entityType1=relation.get('entityType1', None),
            entityId2=relation.get('entityId2', None),
            entityType2=relation.get('entityType2', None),
            relationType=relation.get('relationType', None),
            relationMetadata=json.dumps(relation.get('relationMetadata', {}), ensure_ascii=False),
            bookId=book_id,
            updatedAt=datetime.now()
        ) for relation in entity_relations]
        batch_size = 100
        for i in range(0, len(db_relation), batch_size):
            self.db.bulk_save_objects(db_relation[i:i + batch_size])
            self.db.commit()

        return entity_relations


    def delete_entity_relations(self, book_id):
        self.db.query(AiEntityRelations).filter(AiEntityRelations.book_id == book_id).delete()
        self.db.commit()


if __name__ == '__main__':
    db=get_sql_session()
    entity_relation=AiLoadEntityRelation(db)
    ##delete all the relations
