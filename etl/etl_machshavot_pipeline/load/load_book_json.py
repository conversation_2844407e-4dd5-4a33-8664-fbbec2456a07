import json

from sqlalchemy.orm import Session

from etl.etl_machshavot_pipeline.load import AiBookService
from models import AiBookJsonData
from api.dependencies.relational_db import get_sql_session


class AiBookJsonService:
    def __init__(self, db: Session):
        self.db=db

    def create_book_json(self, book_id, book_json):
        book_json = json.dumps(book_json, ensure_ascii=False)
        db_book = AiBookJsonData(
            bookId=book_id,
            bookJson=book_json,
            bookText=json.dumps({})
        )
        self.db.add(db_book)
        self.db.commit()
        self.db.refresh(db_book)
        return db_book

    def get_book_data_by_id(self, book_id):
        return self.db.query(AiBookJsonData).filter(AiBookJsonData.bookId == book_id).first()

    def update_book_json(self, book_id,book_json_update):
        db_book = self.db.query(AiBookJsonData).filter(AiBookJsonData.bookId == book_id).first()
        if db_book:
            # Iterate over the keys and values in the provided dictionary
            for key, value in book_json_update.items():
                # Set the attribute on the db_book object
                if key == 'bookJson':
                    value = json.dumps(value, ensure_ascii=False)
                setattr(db_book, key, value)

            # Commit the changes to the database
            self.db.commit()
            # Refresh the instance from the database to reflect the latest state
            self.db.refresh(db_book)
        else:
            db_book = self.create_book_json(book_id, book_json_update["bookJson"])

        return db_book



if __name__ == '__main__':
    import os
    db = get_sql_session()

