from typing import Union

from sqlalchemy.orm import Session
from sqlalchemy.sql import text

from models import AiBookJsonData, AiBook


def get_id_by_name(db: Session, name: str, model):
    """
    Retrieve the id from the given table model where the name matches.

    Args:
        session (Session): SQLAlchemy session object.
        name (str): Name to search for.
        model (Base): SQLAlchemy model class (e.g., AiBookType or AiBookLegalField).

    Returns:
        int or None: Returns the id if found, otherwise None.
    """
    # result = session.execute(select(model.id).where(model.name == name)).first()
    #            oldest_row = sql_session.query(table).order_by(table.Id).first()
    result = db.query(model).filter_by(nameEng=name).first()
    if result:
        return result.id, result.name, result.nameEng
    return None , None, None


def get_book_json_by_id(db, book_id):
    """
    Retrieve the book json by the given book id.

    Args:
        session (Session): SQLAlchemy session object.
        book_id (str): Book id to search for.

    Returns:
        dict: Returns the book json if found, otherwise None.
    """

    result = db.query(AiBookJsonData).filter(AiBookJsonData.bookId == book_id).first()
    if result:
        return result.bookJson
    return None


def get_ai_book_data(db, book_id):
    """
    Retrieve the book data by the given book id.

    Args:
        session (Session): SQLAlchemy session object.
        book_id (str): Book id to search for.

    Returns:
        dict: Returns the book data if found, otherwise None.
    """
    result = db.query(AiBook).filter(AiBook.bookId == book_id).first()
    if result:
        return result.__dict__

    return None
