import os
import re
import json
from docx import Document



def is_question_start(text):
    pattern = r'שאלה(?:\s+מספר|\s+מס\'?|\s+)\s*\d+'
    return bool(re.match(pattern, text))


def is_multiple_choice_question(text):
    """Check if text is a multiple choice question"""
    pattern = r'^\s*\d+\s*\.'
    return bool(re.match(pattern, text))


def is_multiple_choice_option(text):
    """Check if text is a multiple choice option"""
    pattern = r'^\s*[א-ת]\s*\.'
    return bool(re.match(pattern, text))


def process_multiple_choice_question(text, current_text):
    """Process multiple choice question text and its choices"""
    # If this is a new question (starts with number)
    if re.match(r'^\s*\d+\s*\.', text):
        return text
    # If this is a choice (starts with א., ב., ג., ד.)
    elif re.match(r'^\s*[א-ת]\s*\.', text):
        return f"\n{text}"  # Add choice on new line
    # If this is continuation text
    else:
        return f"\n\t{text}"  # Add continuation with indentation


def is_new_answer_start(text):
    """Check if this line starts a new answer"""
    patterns = [
        r'^\s*(\d+)\s*\.\s*\t\s*[א-ת]\s*-',  # Matches "51.\tב -"
        r'^\s*(\d+)\s*\.\s*[א-ת]\s*-'  # Matches "49. ג -"
    ]
    return any(re.match(pattern, text) for pattern in patterns)


def extract_multiple_choice_answer_number(text):
    """Extract the answer number from multiple choice answer text"""
    patterns = [
        r'^\s*(\d+)\s*\.\s*\t\s*[א-ת]\s*-',  # Matches "51.\tב -"
        r'^\s*(\d+)\s*\.\s*[א-ת]\s*-'  # Matches "49. ג -"
    ]
    for pattern in patterns:
        match = re.match(pattern, text)
        if match:
            return match.group(1)
    return None


def is_internal_numbering(text):
    """Check if text is an internal numbered point within an answer"""
    # Make sure it's not a new answer start
    if is_new_answer_start(text):
        return False
    return bool(re.match(r'^\s*\d+\s*\.\s*[^א-ת]', text))


def extract_question_number(text):
    match = re.search(r'שאלה(?:\s+מספר|\s+מס\'?|\s+)\s*(\d+)', text)
    return match.group(1) if match else None


def extract_multiple_choice_number(text):
    """Extract question number from multiple choice question"""
    match = re.match(r'^\s*(\d+)\s*\.', text)
    return match.group(1) if match else None


def extract_subject_list(text, paragraph_style=None):
    """
    Extract subject list using either style-based or text-based method
    Args:
        text: The paragraph text
        paragraph_style: The style name of the paragraph
    """
    # First try style-based extraction
    if paragraph_style and paragraph_style == "נושאים המופיעים בשאלה":
        return text.strip()

    # Fall back to text-based extraction
    if isinstance(text, str) and "הנושאים המופיעים" in text:
        match = re.search(r"הנושאים המופיעים(?: בשאלה)?:\s*(.*)", text, re.DOTALL)
        if match:
            subjects = match.group(1).strip()
            return subjects.replace("\n", " ")  # Replace newlines with spaces for clean output
    return None


def is_answer_start(text):
    """Check if text starts an answer section"""
    patterns = [
        r'פתרון שאלה מספר \d+\s*\(\d+%\)',  # Pattern for "פתרון שאלה מספר 1 (30%)"
        r'פתרון נקודתי',
        r'פתרון מורחב',
        r'פתרון מבחן'
    ]
    return any(re.match(pattern, text) for pattern in patterns)


def extract_question_number_from_answer(text):
    """Extract question number from answer text"""
    patterns = [
        r'פתרון שאלה מספר (\d+)',  # For "פתרון שאלה מספר 1 (30%)"
        r'שאלה\s*(\d+)\s*:',  # For "שאלה 1:"
        r'שאלה\s*(?:מספר\s*)?(\d+)'  # For other formats
    ]

    for pattern in patterns:
        match = re.search(pattern, text)
        if match:
            return match.group(1)
    return None


def determine_answer_type(text, current_type):
    """Helper function to determine answer type with fallback"""
    if "נקודתי" in text:
        return "short_answer"
    elif "מורחב" in text:
        return "long_answer"
    elif current_type:  # Keep current type if it exists
        return current_type
    else:  # Fallback to long_answer
        print(f"DEBUG - No explicit answer type found, defaulting to long_answer for: {text}")
        return "long_answer"


def create_document_structure(doc):

    document_structure = {
        "chapters": []
    }

    current_chapter = None
    current_sub_chapter = None
    current_text = ""
    current_answer_type = None
    in_answer_section = False
    is_multiple_choice = False  # Flag for multiple choice sections

    for paragraph in doc.paragraphs:
        style_name = paragraph.style.name
        text = paragraph.text.strip()

        if not text:
            continue

        # print(f"DEBUG - Processing text: {text[:50]}...")

        # Check for new chapter
        if style_name == "פרקים":
            is_new_chapter = False

            # Check for regular exam
            if re.search(r"מבחן(?:\s+מספר|\s+מס\'?)\s*", text):
                is_new_chapter = True
                exam_number_match = re.search(r"מבחן(?:\s+מספר|\s+מס\'?)\s*(\d+)", text)
                exam_number = exam_number_match.group(1) if exam_number_match else None
                is_answer_chapter = any(keyword in text for keyword in ["פתרון", "פתרונות"])
                is_multiple_choice = False  # Reset flag for regular exam
                if is_answer_chapter:
                    in_answer_section = True
                    current_answer_type = determine_answer_type(text, None)
                else:
                    in_answer_section = False

            # Check for multiple choice questionnaire
            elif "שאלון רב ברירה" in text:
                is_new_chapter = True
                exam_number = "0"
                is_answer_chapter = "תשובות" in text
                is_multiple_choice = True  # Set flag for multiple choice

            if is_new_chapter:
                # Save previous chapter if exists
                if current_chapter is not None:
                    if current_sub_chapter is not None and current_text:
                        current_sub_chapter["text"] = current_text.strip()
                        current_chapter["sub_chapters"].append(current_sub_chapter)
                    document_structure["chapters"].append(current_chapter)

                current_chapter = {
                    "chapter_name": text,
                    "exam_number": exam_number,
                    "chapter_type": "answer" if is_answer_chapter else "exam",
                    "sub_chapters": []
                }
                current_sub_chapter = None
                current_text = ""
                continue

        # Only process other parts if we have a current chapter
        if current_chapter is None:
            # print("DEBUG - Skipping text as no chapter is currently active")
            continue

        # Different processing based on chapter type
        if is_multiple_choice:
            # Process multiple choice questions and answers
            if current_chapter["chapter_type"] == "exam":
                if is_multiple_choice_question(text):
                    # Save previous question if exists
                    if current_sub_chapter is not None and current_text:
                        current_sub_chapter["text"] = current_text.strip()
                        current_chapter["sub_chapters"].append(current_sub_chapter)

                    question_number = extract_multiple_choice_number(text)
                    print(f"DEBUG - Processing multiple choice question {question_number}")

                    current_sub_chapter = {
                        "sub_chapter_name": f"שאלה {question_number}",
                        "sub_chapter_type": "question",
                        "question_number": question_number,
                        "sub_chapter_additoinal": text
                    }
                    current_text = text
                else:
                    # Add choices and continuation text to current question
                    if current_sub_chapter is not None:
                        formatted_text = process_multiple_choice_question(text, current_text)
                        if current_text:
                            current_text += formatted_text
                        else:
                            current_text = formatted_text

            elif current_chapter["chapter_type"] == "answer":
                # Check if this is a new answer
                if is_new_answer_start(text):
                    # Save previous answer if exists
                    if current_sub_chapter is not None and current_text:
                        current_sub_chapter["text"] = current_text.strip()
                        current_chapter["sub_chapters"].append(current_sub_chapter)

                    answer_number = extract_multiple_choice_answer_number(text)
                    # print(f"DEBUG - Processing multiple choice answer {answer_number}")

                    current_sub_chapter = {
                        "sub_chapter_name": f"שאלה {answer_number}",
                        "sub_chapter_type": determine_answer_type(text, current_answer_type),
                        "question_number": answer_number,
                        "sub_chapter_additoinal": text
                    }
                    current_text = text
                else:
                    # Add to current answer
                    if current_sub_chapter is not None:
                        if current_text:
                            current_text += "\n"
                        # Check if this is an internal numbered point
                        if is_internal_numbering(text):
                            current_text += text  # Add numbered point as is
                        else:
                            current_text += text  # Add regular text

        else:
            # Original processing for regular exams
            if is_answer_start(text) or (in_answer_section and text.startswith("שאלה")):
                current_answer_type = determine_answer_type(text, current_answer_type)
                # print(f"DEBUG - Found answer section/question: {text}")

                if current_sub_chapter is not None and current_text:
                    current_sub_chapter["text"] = current_text.strip()
                    if len(current_chapter["sub_chapters"])>0 and current_chapter["sub_chapters"][-1]["question_number"]==current_sub_chapter["question_number"] and current_chapter["sub_chapters"][-1]["sub_chapter_type"]==current_sub_chapter["sub_chapter_type"]:
                        current_chapter["sub_chapters"][-1]["text"] += current_sub_chapter["text"]
                        current_chapter["sub_chapters"][-1]["subject_list"] += current_sub_chapter["subject_list"]
                        print(f"DEBUG - Found subjects for question {current_chapter['sub_chapters'][-1]['question_number']}: {current_chapter['sub_chapters'][-1]['subject_list']}")
                    else:
                        current_chapter["sub_chapters"].append(current_sub_chapter)

                question_number = extract_question_number_from_answer(text)
                if question_number:
                    current_sub_chapter = {
                        "sub_chapter_name": f"שאלה {question_number}",
                        "sub_chapter_type": current_answer_type,
                        "question_number": question_number,
                        "sub_chapter_additoinal": text
                    }
                    current_text = ""
                else:
                    current_sub_chapter = None
                    current_text = ""

            elif is_question_start(text):
                if current_sub_chapter is not None and current_text:
                    current_sub_chapter["text"] = current_text.strip()
                    current_chapter["sub_chapters"].append(current_sub_chapter)

                question_number = extract_question_number(text)
                # print(f"DEBUG - Processing question {question_number}")

                current_sub_chapter = {
                    "sub_chapter_name": f"שאלה {question_number}",
                    "sub_chapter_type": "question",
                    "question_number": question_number,
                    "sub_chapter_additoinal": text
                }
                current_text = text

            else:
                if current_sub_chapter is not None:
                    subjects = extract_subject_list(text, style_name)
                    if subjects:

                        current_sub_chapter["subject_list"] = subjects
                        # print(
                        #     f"DEBUG - Found subjects for question {current_sub_chapter['question_number']}: {subjects}")

                    if current_text:
                        current_text += "\n"
                    current_text += text

    # Save final chapter and sub-chapter
    if current_chapter is not None:
        if current_sub_chapter is not None and current_text:
            current_sub_chapter["text"] = current_text.strip()
            if len(current_chapter["sub_chapters"]) > 0 and current_chapter["sub_chapters"][-1]["question_number"] == \
                    current_sub_chapter["question_number"] and current_chapter["sub_chapters"][-1][
                "sub_chapter_type"] == current_sub_chapter["sub_chapter_type"]:
                current_chapter["sub_chapters"][-1]["text"] += current_sub_chapter["text"]
                current_chapter["sub_chapters"][-1]["subject_list"] += current_sub_chapter["subject_list"]

                print(
                    f"DEBUG - Found subjects for question {current_chapter['sub_chapters'][-1]['question_number']}: {current_chapter['sub_chapters'][-1]['subject_list']}")

            else:
                current_chapter["sub_chapters"].append(current_sub_chapter)

        ###concat between sub_chapters that have the same question number and the same type on the same chapter

        document_structure["chapters"].append(current_chapter)
    # for chapter in document_structure["chapters"]:
    #     for sub_chapter in chapter["sub_chapters"]:
    #         clean_subject_list = re.sub(r"\s*הנושאים המופיעים בשאלה:\s*", "", sub_chapter["subject_list"]) if "subject_list" in sub_chapter else ""
    #         sub_chapter["subject_list"] = clean_subject_list.split(",")
    #         if sub_chapter["sub_chapter_type"] == "question":
    #             sub_chapter["question_percentage"]=re.search(r'\((\d+)(?:%| נק\')\)',sub_chapter["sub_chapter_additoinal"]).group(1)



    return document_structure["chapters"]


def process_documents(directory_path):
    """Process all documents in the directory and save to JSON"""
    all_documents = []
    for filename in os.listdir(directory_path):
        if filename.endswith(".docx"):
            print(f"\nDEBUG - Processing file: {filename}")
            doc_path = os.path.join(directory_path, filename)
            doc = Document(doc_path)
            document_structure = create_document_structure(doc)

    # all_documents.append(document_structure)
    # doc_path = directory_path
    # document_structure = create_document_structure(doc_path)


            output_path = os.path.splitext(doc_path)[0] + "_structure.json"
            # Save to JSON file
            with open(output_path, "w", encoding="utf-8") as f:
                json.dump(document_structure, f, ensure_ascii=False, indent=2)
            print(f"DEBUG - Saved document structure to: {output_path}")

            break
    # Print example of the structure
    # print("\nExample of the document structure:")
    # print(json.dumps(all_documents[0], ensure_ascii=False, indent=2))


# Main execution
if __name__ == "__main__":
    directory_path = "/etl_machshavot_pipeline/extract"
    # output_path = "/content/exams2025_structure.json"
    process_documents(directory_path)


