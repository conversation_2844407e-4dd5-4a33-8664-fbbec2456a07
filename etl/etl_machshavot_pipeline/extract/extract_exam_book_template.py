from click import style
from docx import Document
from collections import defaultdict
import json
import re


def clean_title(title):
    return re.sub(r'\d+\.|\t|\n', '', title).strip()

def is_matching_structure(text):
    """
    בודקת אם המשפט תואם את המבנה:
    מספר. אות - תוכן
    """
    pattern = r'^\d+\.\s*[א-ת]\s*-\s+'
    return bool(re.match(pattern, text))


def is_answer_format(text):
    """
    מזהה אם הטקסט תואם למבנה:
    מספר. אות - טקסט
    """
    pattern = r'^\d+\.\s*[א-ת]\s*-\s+.*'
    return bool(re.match(pattern, text))
class BookConfig:
    def __init__(self):
        self.config = {
            'exam': {
                'chapter_style': 'פרקים',
                'question_style': 'question',
                'toc_style': 'toc',
                'answer_part': 'כותרת מספר שאלה',
                'long_answer': 'long_answer',
                'short_answer': 'short_answer',
                'subject_list_style': 'נושאים המופיעים בשאלה',
                'mc_question': 'questions-mc', ## תת פרק
                'questions-mc': 'questions-mc', ## פרק מסוג שאלות אמרקיאיות
                'answers-mc': 'answers-mc', ## פרק מסוג פתרונות אמרקיאיות
                'answers_mc': 'answers_mc', ## פרק מסוג פתרונות שאלות ותשובות
                'questions_mc': 'questions_mc', ## פרק מסוג שאלות ותשובות


            }
        }

    def get_config(self, book_type):
        return self.config.get(book_type, {})




def is_numbered_question(text):
    """
    מזהה אם הטקסט תואם למבנה של:
    מספר. טקסט
    """
    pattern = r'^\d+\.\s+.*'
    return bool(re.match(pattern, text))


## This class is responsible for extracting the chapters and sub-chapters from the exam book but is template
## every exam book has a different styles so before running this class you need to check the styles of the exam book
class BookExam:
    def __init__(self, file, book_name, book_type):
        self.doc = file
        self.chapters_list = []
        self.book_name = book_name
        self.book_type = book_type
        # Initialize BookConfig and get the configuration
        self.config = BookConfig().get_config(book_type)

        self.current_chapter = {}
        self.current_sub_chapter = {}
        self.text, self.sub_chapter_title, self.chapter_title = '', '', ''
        self.sub_chapter_style = ''
        self.chapter_style = ''
        self.mode_question_answer =  False

        self.chapter_style = self.config.get('chapter_style', '')
        self.question_style = self.config.get('question_style', '')
        self.toc_style = self.config.get('toc_style', '')
        self.answer_part = self.config.get('answer_part', '')
        self.long_answer = self.config.get('long_answer', '')
        self.short_answer = self.config.get('short_answer', '')
        self.subject_list_style = self.config.get('subject_list_style', '')
        self.mc_question = self.config.get('mc_question', '')

        self.question_number = None
        self.question_percentage = None
        self.subject_list = ""
        self.exam_number = None
        self.sub_chapter_type = None
        self.chapter_type = None

    def create_new_chapter(self, text, style):

        exam_number_match = re.search(r"מבחן(?:\s+מספר|\s+מס\'?)\s*(\d+)", text)
        self.exam_number = exam_number_match.group(1) if exam_number_match else None
        if not self.exam_number:
            self.exam_number = 0
        self.chapter_type = None
        self.current_chapter = {'chapter_name': clean_title(text), 'sub_chapters': [], 'exam_number': str(self.exam_number)}

        self.chapter_title = text
        if 'פתרו' in text or 'תשובות' in text:
            self.chapter_type = 'answer'
        else:
            self.chapter_type = 'question'

    def decide_sub_chapter_type(self):
        exist_question_number = None
        for sub_chapter in self.current_chapter['sub_chapters']:
            if self.question_number and self.question_number == sub_chapter['question_number']:
                exist_question_number = True
                break
        if self.question_number and exist_question_number and self.sub_chapter_type == "short_answer":
            self.sub_chapter_type = "long_answer"
            self.sub_chapter_title=f"פתרון מורחב שאלה {self.question_number}"
            self.current_chapter["chapter_type"] = "answer"


    def build_new_sub_chapters(self):
        if self.text and len(re.sub(r'\W+', '', self.text)) > 4 and self.sub_chapter_title:


            self.extract_question_number(self.sub_chapter_title)
            self.decide_sub_chapter_type()
            clean_subject_list = re.sub(r"\s*הנושאים המופיעים בשאלה:\s*", "", self.subject_list)
            self.subject_list = clean_subject_list.split(",") if clean_subject_list else []
            ##remove השיבו על אחת מתוך שתי השאלות הבאות: support also : השיבו על אחת מתוך שתי השאלות הבאות
            self.text=re.sub(r'\s*השיבו על אחת מתוך שתי השאלות הבאות:\s*', '', self.text)
            self.current_sub_chapter = {
                'sub_chapter_name': self.sub_chapter_title,
                'text': self.text.strip(),
                'sub_chapter_type': self.sub_chapter_type,
                'subject_list': self.subject_list,
                'question_number': str(self.question_number),
                'question_percentage': self.question_percentage,

            }
            self.text = ''
            self.sub_chapter_title = ''
            self.question_number = None
            self.question_percentage = None
            self.subject_list = ""

            self.current_chapter['sub_chapters'].append(self.current_sub_chapter)

    def extract_question_number(self, text):
        if text:
            try:
                match = re.search(r"שאלה(?:\s+מספר|\s+מס\'|\s+)\s*(\d+)", text)
                self.question_number = str(match.group(1))
            except:
                print(f"Error extracting question number from {text}")
                self.question_number = str(len(self.current_chapter['sub_chapters']) // 2+1)
            if "חלק" in text:
                self.question_number = str(len(self.current_chapter['sub_chapters'])+1)

    def extract_chapters(self):
        not_start= False
        for paragraph in self.doc.paragraphs:
            text = paragraph.text
            style = paragraph.style.name

            if paragraph.style.name is not None and paragraph.style.name.startswith('toc'):
                continue

            elif (style == self.chapter_style and 'מבחן' in text) or style == "questions_mc" or style == "answers_mc":
                if self.text and len(re.sub(r'\W+', '', self.text)) > 10:
                    self.build_new_sub_chapters()

                if self.current_chapter and len(self.current_chapter['sub_chapters']) > 0 and self.current_chapter[
                    'chapter_name'] != "טיפים ללימוד למבחן ופתרונו":
                    self.current_chapter["chapter_type"] = self.chapter_type
                    self.chapters_list.append(self.current_chapter)
                self.text = ''
                self.create_new_chapter(text, style)
            elif style == self.answer_part:
                self.chapter_type = 'answer'

            elif text and style == self.question_style or style == self.mc_question:
                self.build_new_sub_chapters()
                self.sub_chapter_type = 'question' if style == self.question_style else 'question_mc'

                if text:
                    temp_title = clean_title(text)
                    self.sub_chapter_title = temp_title if temp_title else self.sub_chapter_title

                    if (is_numbered_question(text) and self.current_chapter["chapter_name"] == "שאלות לתרגול"):
                        self.text += text
                        self.question_number = str(len(self.current_chapter['sub_chapters']) + 1)
                        self.sub_chapter_title = f"שאלה {self.question_number}"

                    try:
                        match = re.search(r'\((\d+)(?:%| נק\')\)', self.sub_chapter_title)
                        if match:
                            self.question_percentage = match.group(1) or match.group(2)
                    except:
                        self.question_percentage = None

            elif text and style == self.long_answer or style == self.short_answer or style == 'התשובה' or (is_answer_format(text) and self.current_chapter["chapter_name"]=="תשובות לשאלות תרגול"):
                if not self.chapter_type:
                    self.chapter_type = 'answer'
                if not self.mode_question_answer:
                    self.build_new_sub_chapters()
                self.mode_question_answer = True
                self.sub_chapter_type = 'short_answer' if style == self.short_answer else 'long_answer'



                if text:
                    temp_title = clean_title(text)
                    self.sub_chapter_title = temp_title if temp_title else self.sub_chapter_title
                    if style == 'התשובה' or (is_answer_format(text) and self.current_chapter["chapter_name"]=="תשובות לשאלות תרגול"):
                        self.sub_chapter_type = 'long_answer'
                        self.question_number = str(len(self.current_chapter['sub_chapters']) + 1)
                        self.sub_chapter_title = f"פתרון שאלה {self.question_number}"
                        self.text = text
                    try:
                        self.question_percentage = None
                        match = re.search(r'\((\d+)(?:%| נק\')\)', self.sub_chapter_title)
                        if match:
                            self.question_percentage = match.group(1) or match.group(2)
                    except:
                        self.question_percentage = None
            elif style == 'אירועון' or style =='אירועון-שאלה' or is_matching_structure(text):
                if not self.mode_question_answer:
                    self.build_new_sub_chapters()
                self.mode_question_answer = True
                self.sub_chapter_type = 'question_mc' if style == 'אירועון' else 'question_mc'
                if text:
                    self.text += text
                    self.question_number= str(len(self.current_chapter['sub_chapters'])+1)
                    self.sub_chapter_title = f"שאלה {self.question_number}"



            elif style == self.subject_list_style and text:
                self.subject_list += text

            else:
                self.text += text
                self.mode_question_answer = False

        if self.sub_chapter_title and len(self.current_chapter['sub_chapters']) > 0 and self.sub_chapter_title != \
                self.current_chapter['sub_chapters'][-1][
                    'sub_chapter_name'] and len(self.text) > 10:
            self.build_new_sub_chapters()

        elif self.sub_chapter_title and self.chapters_list[-1]['chapter_name'] != self.sub_chapter_title and len(
                self.text) > 10:
            self.build_new_sub_chapters()

        if self.current_chapter and len(self.current_chapter['sub_chapters']) > 0:
            self.current_chapter["chapter_type"] = self.chapter_type

            self.chapters_list.append(self.current_chapter)

    def run(self):
        self.extract_chapters()
        with open(f'{self.book_name}_{self.book_type}.json', 'w') as f:
            json.dump({'chapters': self.chapters_list}, f, ensure_ascii=False)
        return self.chapters_list

if __name__ == '__main__':
    doc = None
    book = BookExam(doc, 'exam', 'exam').run()
