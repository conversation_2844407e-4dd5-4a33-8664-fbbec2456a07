from etl.etl_machshavot_pipeline.transformer.embedding_transformers import EmbeddingTransformerFactory
from abc import ABC, abstractmethod
import pandas as pd

from etl.etl_machshavot_pipeline.status_logger import StatusTracker, log_status_with_tracker
from etl.etl_machshavot_pipeline.transformer.book_splitter import Book
from etl.etl_machshavot_pipeline.transformer.clean_pipeline import AggressiveCleanerPipeline
from etl.etl_machshavot_pipeline.transformer.embedding_transformers import EmbeddingTransformerFactory


# Define the abstract base class for TransformLayer
class AbstractTransformLayer(ABC):
    def __init__(self, input_data, status_tracker: StatusTracker):
        self.input_data = input_data  # Accept any type of input data
        self.status_tracker = status_tracker

    @abstractmethod
    def clean_text(self) -> str:
        """Clean the input data."""
        pass

    @abstractmethod
    def split_text(self) -> list:
        """Split the cleaned text into chunks."""
        pass

    @abstractmethod
    def embeddings_chunks(self) -> list:
        """Transform the text chunks into embeddings."""
        pass


# Concrete implementation for a BookTransformLayer
class BookTransformLayer(AbstractTransformLayer):
    def __init__(self, json_object, book_id, legal_field_id, book_type_id, status_tracker: StatusTracker, book_df,
                 run_id, legal_field, book_type):
        super().__init__(json_object, status_tracker)
        self.chunks_df = pd.DataFrame()
        self.chapter_list = []
        self.sub_chapter_list = []
        self.book_id = book_id
        self.legal_field_id = legal_field_id
        self.book_type_id = book_type_id
        self.book_df = book_df
        self.run_id = run_id
        self.legal_field = legal_field
        self.book_type = book_type

    # @log_status_with_tracker
    def clean_text(self):
        """Clean the input data using the BaseCleanerPipeline."""
        # for index,sub_chapter in self.book_df.iterrows():
        #     clean_sub_chapter_text = BaseCleanerPipeline(sub_chapter["text"]).run()
        #     self.book_df.at[index,"text"]=clean_sub_chapter_text

    # @log_status_with_tracker
    def split_text(self):
        """Split the cleaned text into chunks."""
        book_data = Book(self.book_df, self.book_id, self.legal_field_id, self.book_type_id)

        book_data.process_chapters(self.run_id)
        self.chunks_df = book_data.chunks_df
        self.chunks_df["book_type"] = self.book_type
        self.chunks_df["legal_field"] = self.legal_field
        self.chapter_list = book_data.chapters_list
        self.sub_chapter_list = book_data.sub_chapters_list

    # @log_status_with_tracker
    def embeddings_chunks(self):
        """Transform each chunk into embeddings."""

        self.chunks_df["clean_text"] = self.chunks_df.apply(
            lambda row: row["sub_chapter_title"] + AggressiveCleanerPipeline(row["text"]).run(),
            axis=1
        )
        factory = EmbeddingTransformerFactory()
        embeddings_transformer = factory.get_transformer(transform_type="cohere_embedding",
                                                         cohere_adapter="cohere_adapter")
        embeddings = embeddings_transformer.transform(self.chunks_df["clean_text"])
        self.chunks_df["embedded_vector"] = embeddings

    def run(self):
        """Run the entire transformation pipeline."""
        self.clean_text()
        self.split_text()
        self.embeddings_chunks()
