import json

import numpy as np
import pandas as pd
from datetime import datetime
import logging
import uuid

from ai_models import get_ai_provider_factory
from utils.regex_util import extract_procedure_from_query_text

logger = logging.getLogger(__name__)

counter_chunks=0
counter_sub_chapters=0
counter_chapters=0
MAX_TOKENS_PER_CHUNK = 512  # Max tokens per chunk
_tokenizer = None
COHERE_HF_TOKENIZER_NAME = "Cohere/Cohere-embed-multilingual-light-v3.0"

def get_embedded_tokenizer():
    try:
        global _tokenizer
        if _tokenizer is None:
            from transformers import AutoTokenizer
            _tokenizer = AutoTokenizer.from_pretrained(COHERE_HF_TOKENIZER_NAME)
        return _tokenizer
    except Exception as e:
        logger.error(f'Failed in get_tokenizer with COHERE_HF_TOKENIZER_NAME - {COHERE_HF_TOKENIZER_NAME}, error: {e}')

def get_rag_tokenizer():
    ai_provider = get_ai_provider_factory()
    return ai_provider.provider_choose('master_chunk_haiku')

### i have diffent type of books that every type of book have diffrent metadata and little diffrent structure
##for example the exam book have the subject list and the question number , exam_number and the sub_chapter_type, the study book have the facts, discussion, legal_issues, decision, laws and references
## the verdicts have the facts, discussion, legal_issues, decision, laws and references
## the mikudeit have the facts, discussion, legal_issues, decision, laws and references

class Chunk:
    def __init__(self, text, chapter_number, chapter_title,sub_chapter_title ,sub_chapter_number, run_id, book_id, chapter_id, sub_chapter_id,meta_data, page_number=None):
        global counter_chunks
        self.cid = f"{book_id}_{counter_chunks}"
        self.text = text
        self.chapter_title = chapter_title
        self.sub_chapter_title = sub_chapter_title
        self.chapter_id = chapter_id
        self.sub_chapter_id = sub_chapter_id
        self.chapter_number = chapter_number
        self.sub_chapter_number = sub_chapter_number
        self.page_number = page_number
        self.run_id = run_id
        self.book_id = book_id
        self.meta_data = meta_data
        self.updated_at = datetime.now()
        self.token_count = get_rag_tokenizer().tokenize(text)
        counter_chunks += 1
        embedded_tokenizer = get_embedded_tokenizer()
        embedded_tokens = embedded_tokenizer.tokenize(self.text)
        self.embedded_tokens = len(embedded_tokens)


    def to_dict(self):
        return {
            'cid': self.cid,
            'text': self.text,
            'book_id': self.book_id,
            'chapter_id': self.chapter_id,
            'sub_chapter_id': self.sub_chapter_id,
            'chapter_number': self.chapter_number,
            'sub_chapter_number': self.sub_chapter_number,
            'meta_data': self.meta_data,
            'page_number': 1,
            'run_id': self.run_id,
            'updated_at': self.updated_at,
            'token_count': self.token_count,
            'embedded_tokens': self.embedded_tokens,
            'chapter_title': self.chapter_title,
            'sub_chapter_title': self.sub_chapter_title
        }


import re
from nltk.tokenize import sent_tokenize


class SubChapter:
    def __init__(self, book_id, chapter_id, sub_chapter_title,chapter_title, sub_chapter_number, sub_chapter_text, chapter_number, meta_data, page_number,book_type_id,sub_chapter_type="general",exam_number=0):

            self.sub_chapter_title = sub_chapter_title
            self.chapter_title = chapter_title
            self.sub_chapter_number = sub_chapter_number
            self.chapter_id = chapter_id
            self.sub_chapter_id = uuid.uuid4()
            self.chapter_number = chapter_number
            self.sub_chapter_text = sub_chapter_text
            self.book_type_id=book_type_id
            self.page_number = page_number
            self.meta_data = meta_data
            self.book_id = book_id
            self.total_token = 0
            self.procedures = []
            self.sub_chapter_type=sub_chapter_type
            self.exam_number=exam_number
            self.chunks_df = pd.DataFrame()

    def split_into_chunks(self, run_id):
        embedded_tokenizer = get_embedded_tokenizer()

        # Split the text into paragraphs using multiple consecutive newlines
        paragraphs = re.split(r'\n{3,}', self.sub_chapter_text.strip())

        current_chunk = ""
        current_token_count = 0

        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            paragraph_tokens = embedded_tokenizer.tokenize(paragraph)

            paragraph_token_count = len(paragraph_tokens)

            if current_token_count + paragraph_token_count > MAX_TOKENS_PER_CHUNK:
                if current_chunk:
                    chunk = Chunk(text=current_chunk.strip(), chapter_number=self.chapter_number,sub_chapter_title=self.sub_chapter_title,
                                  chapter_title=self.chapter_title,
                                  sub_chapter_number=self.sub_chapter_number, run_id=run_id, book_id=self.book_id,
                                  chapter_id=self.chapter_id, sub_chapter_id=self.sub_chapter_id,
                                  page_number=self.page_number,meta_data=self.meta_data)
                    self.total_token += chunk.token_count
                    self.chunks_df = pd.concat([self.chunks_df, pd.DataFrame([chunk.to_dict()])])
                    current_chunk = ""
                    current_token_count = 0

            if paragraph_token_count > MAX_TOKENS_PER_CHUNK:
                # If a single paragraph is too long, split it into sentences
                sentences = sent_tokenize(paragraph)
                for sentence in sentences:
                    sentence_tokens = embedded_tokenizer.tokenize(sentence)
                    sentence_token_count = len(sentence_tokens)

                    if current_token_count + sentence_token_count > MAX_TOKENS_PER_CHUNK:
                        if current_chunk:
                            chunk = Chunk(text=current_chunk.strip(), chapter_number=self.chapter_number,
                                          sub_chapter_title=self.sub_chapter_title,
                                          chapter_title=self.chapter_title,
                                          sub_chapter_number=self.sub_chapter_number, run_id=run_id,
                                          book_id=self.book_id,
                                          chapter_id=self.chapter_id, sub_chapter_id=self.sub_chapter_id,
                                          page_number=self.page_number, meta_data=self.meta_data)
                            self.total_token += chunk.token_count

                            self.chunks_df = pd.concat([self.chunks_df, pd.DataFrame([chunk.to_dict()])])
                            current_chunk = ""
                            current_token_count = 0

                    current_chunk += sentence + " "
                    current_token_count += sentence_token_count
            else:
                current_chunk += paragraph + "\n\n"
                current_token_count += paragraph_token_count

        # Add the last chunk if there's any remaining text
        if current_chunk:
            chunk = Chunk(text=current_chunk.strip(), chapter_number=self.chapter_number,sub_chapter_title=self.sub_chapter_title,
                                  chapter_title=self.chapter_title,
                                  sub_chapter_number=self.sub_chapter_number, run_id=run_id, book_id=self.book_id,
                                  chapter_id=self.chapter_id, sub_chapter_id=self.sub_chapter_id,
                                  page_number=self.page_number,meta_data=self.meta_data)
            self.total_token += chunk.token_count

            self.chunks_df = pd.concat([self.chunks_df, pd.DataFrame([chunk.to_dict()])])

    def sub_chapter_table(self):
        total_token = int(self.chunks_df['token_count'].sum())
        if self.book_type_id in [1,2]:
            if "references" in self.meta_data:
                temp = [extract_procedure_from_query_text(x) for x in self.meta_data["references"]]
                self.procedures = [item for item in temp if item]
        elif self.book_type_id in [3]:
            self.procedures = extract_procedure_from_query_text(self.sub_chapter_title)
        if "page_number" in self.chunks_df:
            page_number = self.chunks_df.iloc[0]["page_number"]

            if isinstance(page_number, float) and np.isnan(page_number):
                start_page = 0
            else:
                start_page = int(page_number)
        else:
            start_page = 0
        self.procedures=json.dumps(self.procedures,ensure_ascii=False)
        return {
            "sub_chapter_id": self.sub_chapter_id,
            "sub_chapter_title": self.sub_chapter_title,
            "book_id": self.book_id,
            "chapter_id": self.chapter_id,
            "sub_chapter_number": self.sub_chapter_number,
            "sub_chapter_text": self.sub_chapter_text,
            "page_number": start_page,
            "total_token": total_token,
            "meta_data": self.meta_data,
            "procedures": self.procedures,
            "exam_number":self.exam_number,
            "sub_chapter_type":self.sub_chapter_type
        }




class Chapter:
    def __init__(self, chapter_title, sub_chapters_df, chapter_number, book_id,book_type_id,chapter_summary,chapter_type,exam_number):
        self.chapter_title = chapter_title
        self.chapter_id = uuid.uuid4()
        self.book_id = book_id
        self.book_type_id=book_type_id
        self.sub_chapters_df = sub_chapters_df
        self.sub_chapters_list = []
        self.chapter_number = chapter_number
        self.chapter_summary = chapter_summary
        self.chapter_type = chapter_type
        self.exam_number = exam_number
        self.chunks_df = pd.DataFrame()
        self.start_page=0

    def split_into_sub_chapter(self, run_id,book_type_id):
        global counter_sub_chapters
        for _, sub_chapter_row in self.sub_chapters_df.iterrows():
            metadata={}
            try:
                facts=sub_chapter_row.get("facts",[])
                discussion=sub_chapter_row.get("discussion",[])
                legal_issues=sub_chapter_row.get("legal_issues",[])
                decision=sub_chapter_row.get("decision",[])
                laws=sub_chapter_row.get("laws",[])
                references=sub_chapter_row.get("references",[])

                ## Extract metadata from the Exam Book
                if book_type_id==4:
                    subject_list=sub_chapter_row.get("subject_list",[])
                    question_number=sub_chapter_row.get("question_number",[])
                    sub_chapter_type=sub_chapter_row.get("sub_chapter_type",[])
                    if subject_list and subject_list!= ['']:
                        metadata["subject_list"]=subject_list
                    metadata["question_number"]=question_number
                    metadata["sub_chapter_type"]=sub_chapter_type

                # Extract metadata from the Study Book , Verdicts and Mikudeit
                else:
                    if facts:
                        metadata["facts"]=facts
                    if discussion:
                        metadata["discussion"]=discussion
                    if legal_issues:
                        metadata["legal_issues"]=legal_issues
                    if decision:
                        metadata["decision"]=decision
                    if laws:
                        metadata["laws"]=laws
                    if references:
                        metadata["references"]=references



            except:
                metadata={"laws": [], "references": []}

            sub_chapter = SubChapter(
                book_id=self.book_id,
                chapter_id=self.chapter_id,
                book_type_id=self.book_type_id,
                sub_chapter_title=sub_chapter_row["sub_chapter_name"],
                chapter_title=self.chapter_title,
                sub_chapter_number=counter_sub_chapters,
                sub_chapter_text=sub_chapter_row["text"],
                meta_data=metadata,
                chapter_number=self.chapter_number,
                page_number=sub_chapter_row.get("page_number",0),
                sub_chapter_type=sub_chapter_row.get("sub_chapter_type","general"),
                exam_number=sub_chapter_row.get("exam_number",0)
            )

            sub_chapter.split_into_chunks(run_id)
            sub_chapter.total_token = sub_chapter.chunks_df['token_count'].sum()
            counter_sub_chapters += 1
            self.chunks_df = pd.concat([self.chunks_df, sub_chapter.chunks_df])
            # self.chunks_df["sub_chapter_title"]=sub_chapter_row["sub_chapter_title"]
            self.sub_chapters_list.append(sub_chapter.sub_chapter_table())


    def chapter_table(self):
        total_token = int(self.chunks_df['token_count'].sum())
        if "page_number" in self.sub_chapters_list[0]:
            page_number = self.sub_chapters_list[0]["page_number"]

            if isinstance(page_number, float) and np.isnan(page_number):
                start_page = 0  # או כל ערך ברירת מחדל אחר
            else:
                start_page = int(page_number)
        else:
            start_page = 0  # או כל ערך ברירת מחדל אחר אם המפתח לא קיים
        return {
            "chapter_id": self.chapter_id,
            "book_id": self.book_id,
            "chapter_number": self.chapter_number,
            "chapter_title": self.chapter_title,
            "chapter_summary": str(self.chapter_summary) or "",
            "total_token": total_token,
            "start_page": start_page,
            "exam_number": self.exam_number,
            "chapter_type": self.chapter_type,
            "chapter_text": self.sub_chapters_df["text"].str.cat(sep='\n\n\n')
        }

class Book:
    def __init__(self, book_data_df, book_id, legal_field_id, book_type_id):
        self.book_id = book_id
        self.legal_field_id = legal_field_id
        self.book_type_id = book_type_id
        self.chunks_df = pd.DataFrame()
        self.chapters_list=[]
        self.sub_chapters_list=[]
        # Group the data by chapter to keep track of sub_chapters per chapter
        self.book_data = book_data_df.groupby('chapter_name',sort=False)

    def process_chapters(self, run_id):
        global counter_chapters
        global counter_sub_chapters
        global counter_chunks
        counter_chapters=0
        counter_sub_chapters = 0
        counter_chunks = 0

        for chapter_number, chapter_df in self.book_data:
            chapter_title = chapter_df['chapter_name'].unique()[0]
            chapter_summary = chapter_df['chapter_summary'].unique()[0]
            chapter_type = chapter_df['chapter_type'].unique()[0]
            exam_number = chapter_df['exam_number'].unique()[0]
            chapter = Chapter(chapter_title=chapter_title, sub_chapters_df=chapter_df, chapter_number=counter_chapters, book_id=self.book_id,book_type_id=self.book_type_id,chapter_summary=chapter_summary,
                              chapter_type=chapter_type,exam_number=exam_number)
            chapter.split_into_sub_chapter(run_id,self.book_type_id)
            self.chapters_list.append(chapter.chapter_table())
            self.sub_chapters_list.extend(chapter.sub_chapters_list)
            self.chunks_df = pd.concat([self.chunks_df, chapter.chunks_df])
            counter_chapters+=1
