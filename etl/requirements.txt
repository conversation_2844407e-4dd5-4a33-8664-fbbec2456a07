fastapi==0.103.2
uvicorn==0.34.0
python-multipart==0.0.6
python-dotenv==1.0.0
pinecone[grpc]==5.4.0
cohere==5.9.0
anthropic==0.49.0
pandas==2.0.1
tqdm==4.66.1
sqlalchemy==2.0.21
gunicorn==21.2.0
redis==5.0.1
protobuf==4.25.6
beautifulsoup4==4.12.2
pymssql==2.3.2
lxml==4.9.3
transformers==4.42.3
pydantic==2.11.3
pydantic-ai-slim==0.0.31
pydantic_core==2.33.1
pydantic-graph==0.0.31
pinecone-text==0.6.0
chardet==5.2.0
arrow==1.3.0
firebase-admin==6.4.0
requests~=2.32.3
boto3~=1.35.24
botocore~=1.35.24
numpy~=1.26.4
nltk~=3.9.1
urllib3~=2.2.3
starlette~=0.27.0
mistune==3.1.0
async_timeout==5.0.1
python-docx==1.1.2 ##for the word extraction
contextvars==2.4 ### for add context to the application requests
Markdown==3.7.0 ## for creating markdown files from html
pymongo==4.11.2
motor==3.7.0 ## for async pymongo
cryptography==44.0.0 ## for encrypting the data for the database
pydantic-settings~=2.9.1