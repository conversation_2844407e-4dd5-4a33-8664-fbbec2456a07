from enum import Enum
from typing import List, Optional, Union, Dict

from pydantic import BaseModel

from utils.dto import ETLRequestStatusEnum


class RatioStatusEnum(int, Enum):
    started = 1
    stored_procedure = 2
    create_dictionary_from_dataframe = 3
    create_context = 4
    root_verdict = 5
    generate_prompt = 6
    get_completion = 7
    create_insert_list = 8
    save_in_airatio = 9

class TxtIdRatioResult(BaseModel):
    txtId: int
    status: RatioStatusEnum
    message: str

class StatusEnum(int, Enum):
    started = 1
    chunks_created = 2
    sent_to_embeddings = 3
    saved_in_pincone = 4
    saved_in_sql = 5
    saved_in_sql_with_null_ntokens = 6
    saved_in_aimetadata_sql_table = 7
    did_not_save_in_sql = 8


class TxtIdResult(BaseModel):
    txtId: int
    status: StatusEnum
    message: str


class JobResult(BaseModel):
    text_ids_results: List[TxtIdResult]
    message: str
    txt_ids_with_null_ntoken: str
    txt_ids_not_saved_in_aimetadata: list
    ids_with_pinecone_metadata_limit_error: list
    chunks_amount: int
    retrieval_time: float
    chunks_time: float
    cohear_time: float
    pincone_time: float
    sql_time: float
    sql_session_execute_in_get_full_row_time: float
    chunks_spliting_time: float
    cleaned_chunks_time: float
    prepare_metadata_time: float
    populate_text_metadata_time: float
    store_procedure_time: float
    fix_hebrew_encoding_time: float
    time_token: float
    total_time: float
    total_api_time: float
    status: int





class RatioResult(BaseModel):
    text_ids_results: List[TxtIdRatioResult]
    message: str
    status: int


class MasterChunkStatusEnum(int, Enum):
    started = 1
    get_total_tokens = 2
    get_text_only = 3
    create_prompt = 4
    get_master_chunk = 5
    saved_in_sql = 6

    failed_to_loads_master_chunk = 7


class EtlStatusEnum(str, Enum):
    Created = 0
    Processing = 1
    Completed = 2
    Failed = 3


class TxtIdMasterChunkResult(BaseModel):
    txtId: Optional[int] = None
    status: Optional[Union[MasterChunkStatusEnum, EtlStatusEnum]] = None
    message: Optional[str] = None


class ETLRequest(BaseModel):
    txtId: Union[int, List[int]]
    runId: Optional[str]
    maagarId: Optional[int]
    status: Optional[ETLRequestStatusEnum]


class ETLMetadataRequest(BaseModel):
    txtId: Union[int, List[int]]


class ETLResponse(BaseModel):
    message: Optional[str] = None


class LawQuestionsResult(BaseModel):
    text_ids_results: List[TxtIdResult]
    message: str
    status: int


class LawQuestionsStatusEnum(int, Enum):
    started = 1
    get_chunks_result = 2
    create_text_only = 3
    create_prompt = 4
    get_law_questions = 5
    split_and_dumps_law_questions = 6
    saved_in_sql = 7


class TxtIdLawQuestionsResult(BaseModel):
    txtId: int
    status: LawQuestionsStatusEnum
    message: str


class MasterChunkResult(BaseModel):
    text_ids_results: List[TxtIdMasterChunkResult]
    message: str
    status: int


class MasterChunkResultBatch(BaseModel):
    text_ids_results: Dict[int, TxtIdMasterChunkResult]
    message: str
    status: int


class BM25Request(BaseModel):
    runId: Optional[str]
    maagarId: Optional[int]


class TokenizerRequest(BaseModel):
    toTokenize: str
