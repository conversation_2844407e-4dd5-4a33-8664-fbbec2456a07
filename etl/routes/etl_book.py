from typing import Optional, Union
from uuid import UUID

from fastapi import APIRouter, Depends
from pydantic import BaseModel
import json
from api.dependencies.vector_db import get_books_summaries_index
from etl.etl_machshavot_pipeline import StudyBooksETL
from etl.etl_machshavot_pipeline.adsjument_books_db import update_book_page_number, fix_page_number_html, count_html_pages_by_book_id
from etl.etl_machshavot_pipeline.extract.wordToJson import convert_word_to_json, delete_book_related_records

from etl.etl_machshavot_pipeline.load import AiBookService
from etl.etl_machshavot_pipeline.run_job_etl import machshavot_etl_function
from etl.etl_machshavot_pipeline.utils_etl import chunks_page_by_sub_chapters, sub_chapter_page_number, update_chunks_page_number, \
    update_sub_chapter_page_number
from api.dependencies.relational_db import get_sql_session
from middlewares.authorization import get_api_key

etl_router = APIRouter(tags=["EtlBook"],dependencies=[Depends(get_api_key)])

class ExtractBook(BaseModel):
    version: str
    year: str
    bookType: str
    legalField: str

class AiBookRaw(BaseModel):
    book_id: UUID = None
    book_name: Optional[str] = ""
    start_page: Optional[int] = 0
    end_page: Optional[int] = 0

@etl_router.post("/extract_book")
async def extract_book(book: Optional[ExtractBook],word_to_json: Optional[bool] = False,
                       db_session=Depends(get_sql_session)):
    if book:
        if not book.version or not book.year or not book.bookType or not book.legalField:
            return {"message": "No book to extract", "book_name": f"{book.legalField}-{book.bookType}"}

        book_res = convert_word_to_json(db_session, book.year, book.version, book.bookType, book.legalField,word_to_json=word_to_json)
        if "message" in book_res:
            return {"message": "No book to extract", "book_name": f"{book.legalField}-{book.bookType}"}
        return {"book_id": book_res["book_id"], "book_name": book_res["book_name"],
                "book_name_eng": book_res["book_name_eng"], "book_type": book_res["book_type"],
                "legal_field": book_res["legal_field"]}

    return {"message": "No book to extract"}


@etl_router.post("/etl_book")
async def etl_book(book_id: str, db_session=Depends(get_sql_session), pinecone_session=Depends(get_books_summaries_index)):
    delete_book_related_records(db_session, book_id)
    etl_service = StudyBooksETL(file_path=None, book_id=book_id,
                                sql_storage=db_session,
                                pinecone_storage=pinecone_session)
    etl_service.run()

    return {"message": "etl process completed","data_report":{**etl_service.status_tracker.status}}


@etl_router.put("/sub_chapter_page")
async def sub_chapter_page(book_id: UUID, db_session=Depends(get_sql_session)):
    sub_chapters_data=sub_chapter_page_number(db_session, book_id)
    return {"message": "Sub chapter page updated", "book_id": book_id,"unmatched_subchapters": sub_chapters_data["unmatched_subchapters"]}


@etl_router.put("/chunk_page")
async def chunk_page(book_id: UUID, db_session=Depends(get_sql_session)):
    book_data=chunks_page_by_sub_chapters(db_session, book_id)
    return {"book_id": book_id, "unmatched_chunks": len(book_data["unmatched_chunks"]),
            "unmatched_chunks_after_complete_by_sub_chapters": len(book_data["unmatched_chunks_after_complete_by_sub_chapters"])}


@etl_router.post("/fix_pages_number_page")
async def fix_pages_number(book_request: Union[AiBookRaw, None]=None, db_session=Depends(get_sql_session)):
    """
     Fixes the page numbers for a specific book if `book_id` is provided;
     otherwise, it will fix the page numbers for all books.

     Parameters:
     - book_request (AiBookRaw): Request body containing the `book_id` to fix the page numbers or None to fix all books.
     - db_session (Session): SQLAlchemy database session.

     Returns:
     - dict: Success message with the `book_id` or "all" indicating if all books were processed.
     """
    books_list = AiBookService(get_sql_session()).get_ai_book(0, 0)
    BooksDoesntStartOnPage1 = {
        "dfa5f1ce-82df-4238-be57-e03f9d8e2524": {"start_page": 197, "end_page": 296},
        "5055abd2-a55e-4155-bca8-5b3c7b247e51": {"start_page": 297, "end_page": 310},
        "e3372213-a917-4b06-b666-897293631367": {"start_page": 311, "end_page": 318},
        "422a0872-31bf-4244-8fe1-18329ab86b96": {"start_page": 247, "end_page": 340},
        "32501afc-72ef-4831-a94e-26b1abdd4b49": {"start_page": 223, "end_page": 286},
        "87bc806a-92f5-4d51-90c7-538076d4a65e": {"start_page": 175, "end_page": 239}
    }

    response_template,book_id,ai_book_raw = [],"",{}

    if book_request and book_request.book_id:
        books_list = [book for book in books_list if str(book_request.book_id) == str(book.bookId)]

        # books_list = [book for book in books_list if str(book.bookId) in BooksDoesntStartOnPage1.keys()]

    for book in books_list:
        try:
            book_dict = book.__dict__
            book_id,start_page,end_page = str(book_dict['bookId']),book_dict['start_page'],book_dict['end_page']
            html_pages_count = count_html_pages_by_book_id(db_session, book_id)
            end_page = max(html_pages_count['total_pages'], end_page)

            if book_id in BooksDoesntStartOnPage1.keys():
                start_page = BooksDoesntStartOnPage1[book_id]['start_page']
                end_page = BooksDoesntStartOnPage1[book_id]['end_page']

            start_page = book_request.start_page if book_request and book_request.start_page else start_page
            end_page = book_request.end_page if book_request and book_request.end_page else end_page
            start_page=max(start_page,1)
            if start_page or end_page:
                ai_book_raw= update_book_page_number(db_session, book_id=book_id, start_page=start_page,
                                            end_page=end_page)
                update_chunks_page_number(db_session, book_id=book_id, start_page=start_page, end_page=end_page)
                update_sub_chapter_page_number(db_session, book_id=book_id, start_page=start_page, end_page=end_page)
                if ai_book_raw["status"]=="success" and html_pages_count['total_pages'] > 0:
                    htmls_count=fix_page_number_html(db_session, book_id=book_id, start_page=start_page, end_page=end_page)

                response_template.append(
                    {"book_id": book_id, "start_page": start_page, "end_page": end_page,
                     "total_pages": html_pages_count['total_pages'],
                     "status": "success", "book_name": ai_book_raw["book_name"]})

            elif not start_page or ai_book_raw["status"]=="failed":
                response_template.append({"book_id": book_id, "status": "failed"})

            print("book_id",book_id,"start_page",start_page,"end_page",end_page)

        except Exception as e:
            response_template.append({"book_id": book_id, "status": str(e)})

    return response_template


@etl_router.post("/machshavot_etl")
async def machshavot_etl(year: str="2025", version: str="1_0", main_folders: list = ["exam", "study_book", "verdicts", "mikudit"],
                         db_session=Depends(get_sql_session), pinecone_index=Depends(get_books_summaries_index)):
    try:
        data_report=machshavot_etl_function(db_session, pinecone_index, year, version, main_folders)
        return {"message": "etl process completed", "year": year, "version": version, "main_folders": main_folders,data_report:data_report}
    except Exception as e:
        return {"message": str(e)}
