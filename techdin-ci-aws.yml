# Agent dependencies - Docker, aws cli
# trigger:
# - develop

pool:
  name: Techdin<PERSON><PERSON>Pool
  # vmImage: ubuntu-latest

variables:
- group: AWS_Pilot_access
- name: image_name
  value: techdin_ml

steps:
- task: Bash@3
  inputs:
    targetType: 'inline'
    script: |
      # Write your commands here
      echo $(Build.BuildId)
      timestamp=$(date +%d-%m-%Y)
      branchOrTag=$(Build.SourceBranchName)
      echo "##vso[build.updatebuildnumber]$branchOrTag--$timestamp--$(Build.BuildId)"
  displayName: Echo tag

- script: |
    aws configure set aws_access_key_id $(AWS_ACCESS_KEY)
    aws configure set aws_secret_access_key $(AWS_SECRET_ACCESS_KEY)
    aws configure set default.region $(region)
  displayName: 'Configure AWS CLI'

- script: |
    sudo docker build -t $(image_name):$(Build.BuildId) -f Dockerfile .
  displayName: 'Build Docker Image'


- script: |
    aws ecr get-login-password --region $(region) | sudo docker login --username AWS --password-stdin $(aws_account_id).dkr.ecr.$(region).amazonaws.com/$(image_name)
    sudo docker tag $(image_name):$(Build.BuildId) $(aws_account_id).dkr.ecr.$(region).amazonaws.com/$(image_name):$(Build.BuildId)
    sudo docker push $(aws_account_id).dkr.ecr.$(region).amazonaws.com/$(image_name):$(Build.BuildId)
  displayName: 'Push Docker Image to ECR'
