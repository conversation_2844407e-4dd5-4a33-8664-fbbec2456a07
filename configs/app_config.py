import os
from typing import Set
from dotenv import load_dotenv
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic.functional_validators import BeforeValidator, field_validator

# Load .env only if not running in Docker
if os.environ.get("RUNNING_IN_DOCKER") != 'False':
    load_dotenv()


# Helper function for parsing the env variable
def parse_exclude_items(v):
    if isinstance(v, str):
        return set(v.split(",")) if v else set()
    return v if isinstance(v, set) else set()

class AppConfig(BaseSettings):
    exclude_items: Set[str] = Field(
        default_factory=set,
        validation_alias="EXCLUDE_ITEMS",
        json_schema_extra={"description": "Items to exclude"},
        validate_default=True,
    )

    # Register validator for parsing string into Set[str]
    @field_validator("exclude_items", mode="before")
    @classmethod
    def parse_env_var(cls, v):
        return parse_exclude_items(v)

    class Config:
        env_file = ".env"


COHERE_EMBED_MODEL = "embed-multilingual-light-v3.0"
COHERE_HF_TOKENIZER_NAME = os.environ.get('COHERE_HF_TOKENIZER_NAME')
EMBED_BATCH_SIZE = 96  # https://docs.cohere.com/reference/embed
SCORE_STARS_THRESHOLD = [140, 150, 160, 168, 173, 178]
TOP_TOKENS = 512

# Pinecone
PINECONE_API_KEY = os.getenv('PINECONE_API_KEY_1')
PINECONE_ENVIRONMENT = os.environ.get('PINECONE_ENVIRONMENT')
LAW_INDEX_NAME = os.environ.get(
    'LAW_INDEX_NAME')  # if you chnage those, you may need to also change the BM25 params file names (see below) - 'law-384-p1'
VERDICTS_INDEX_NAME = os.environ.get('VERDICTS_INDEX_NAME')  # to  'verdicts-384-p1'
MACHSHAVOT = os.environ.get('MACHSHAVOT')  # to  'verdicts-384-p1'
VERDICTS_SEARCH_TOP_K = 150
LAWS_SEARCH_TOP_K = 150
BOOKS_SUMMARIES_TOP_K = 15
UPSERT_BATCH_SIZE = 100  # https://docs.pinecone.io/docs/insert-data#batching-upserts
DELETE_BATCH_SIZE = 1000
EMBEDDING_SIZE = 384
PINECONE_N_THREADS = 10  # can go up to 30 if we have really large upsert batches (but should only do that for etl pods)
BM25_LAW_PARAMS = os.path.join('app', 'assets',
                               f'{LAW_INDEX_NAME}_bm25_params.json')  # assuming root is the working dir
BM25_VERDICT_PARAMS = os.path.join('app', 'assets', f'{VERDICTS_INDEX_NAME}_bm25_params.json')
USE_SPARSE_EMBEDDINGS = True  # Change to True to start using it in the search
# SEARCH_ALPHA = 0.8 # float between 0 and 1 where 0 == sparse only and 1 == dense only. Note that playing with it should also affect the scale defined in SCORE_STARS_THRESHOLD (the expected scores will be smaller roughly by a factor of alpha)
SEARCH_TOP_K = 250
QUERY_TOP_K = 200
NO_RESULTS_MESSAGE = "לא נמצא מסמך התואם את מספר ההליך {procedures} שהקלדת.\n אנא ודא שהקלדת את מספר ההליך נכון.\n באפשרותך להשתמש בדף החיפוש כדי לאתר אותו"  # why here?
RESULTS_NONE_MESSAGE = 'אין תוצאות'  # why here?

# Anthropic
# MAX_CHAT_COMPLETION_TOKENS = 500
MAX_ANSWER_TOKENS = 2500
CLAUDE_ANSWER_TEMPERATURE = 0.1
LAW_QUESTION_MAX_TOKENS = 1200
CLAUDE_ANSWER_TOP_P = 0.8
MASTER_CHUNK_MAX_ANSWER_TOKENS = 2000
MASTER_CHUNK_CLAUDE_ANSWER_TEMPERATURE = 0.3
CHAT_COMPLETION_MODEL = os.environ.get('CHAT_COMPLETION_MODEL')
CHAT_COMPLETION_RATIO_MODEL = os.environ.get('CHAT_COMPLETION_RATIO_MODEL')

NER_MODEL = "claude-3-5-haiku-20241022"
LAW_QUESTION_MODEL = "claude-3-5-haiku-20241022"

ANTHROPIC_MASTER_CHUNK_API = os.environ.get('ANTHROPIC_MASTER_CHUNK_API')
ANTHROPIC_MASTER_CHUNK_MODEL = "claude-3-5-haiku-20241022"  # "claude-3-haiku-20240307""claude-3-haiku-20240307"

##chat anthropic
CHAT_COMPLETION_PREMIUM_MODEL = "claude-3-5-sonnet-20241022"
CHAT_COMPLETION_STANDARD_MODEL = "claude-3-5-haiku-20241022"
CHAT_COMPLETION_MACHSHAVOT = "claude-3-5-sonnet-20241022"

GEMINI_RATIO_MODEL = os.environ.get('GEMINI_RATIO_MODEL')
GEMINI_ANSWER_TOP_P = 0.9

##Mongo DB

MONGO_USER_NAME = os.environ.get('MONGO_USER_NAME', '')
MONGO_PASSWORD = os.environ.get('MONGO_PASSWORD', '')
MONGO_URL = os.environ.get('MONGO_URL', '')

## AWS KMS Configuration for Chat Encryption
AWS_KMS_KEY_ID = os.environ.get('AWS_KMS_KEY_ID', 'd4b16786-f799-410f-bb38-68f4327ea043')
AWS_KMS_REGION = os.environ.get('AWS_KMS_REGION', 'eu-central-1')
USER_KEY_CACHE_TTL = int(os.environ.get('USER_KEY_CACHE_TTL', 1800))  # 30 minutes default

MONGO_DB_AUTOCOMPLETE = os.environ.get('MONGO_DB_AUTOCOMPLETE', "techdin-autocomplete")
MONGO_PROCEDURE_COLLECTION = os.environ.get('MONGO_PROCEDURE_COLLECTION', "procedure_collection")
MONGO_INDEX_AUTOCOMPLETE = os.environ.get('MONGO_INDEX_AUTOCOMPLETE', "case_number_autocomplete")

MONGO_DB_HISTORY = os.environ.get('MONGO_DB_HISTORY', "techdin-dev")
MONGO_CHAT_HISTORY_COLLECTION = os.environ.get('MONGO_CHAT_HISTORY_COLLECTION', "chat_history")
MONGO_USERS_COLLECTION = os.environ.get('MONGO_USERS_COLLECTION', "users")

## ETL Dummy Data
ETL_DUMMY_DATA = os.environ.get('ETL_DUMMY_DATA', True)  ### it's for testing the ETL process

# REDIS connection - TODO - redis server to the QA
REDIS_HOST = os.environ.get('REDIS_HOST')
REDIS_PORT = int(os.environ.get('REDIS_PORT'))
REDIS_DB = int(os.environ.get('REDIS_DB'))
REDIS_USE_SSL = os.environ.get('REDIS_USE_SSL').lower() in ("true", "yes", "1")
REDIS_PASSWORD = os.environ.get('REDIS_PASSWORD')
REDIS_USE_PASSWORD = os.environ.get('REDIS_USE_PASSWORD').lower() in ("true", "yes", "1")

# SQL
DATABASE_USERNAME = os.environ.get('DATABASE_USERNAME')
DATABASE_HOSTNAME = os.environ.get('DATABASE_HOSTNAME')
DATABASE_PORT = int(os.environ.get('DATABASE_PORT'))
DB_PASSWORD = os.environ.get('DB_PASSWORD')
DATABASE_DBNAME = os.environ.get('DATABASE_DBNAME')
AI_DATABASE_NAME = os.environ.get('AI_DATABASE_NAME')

DATABASE_URL_TEMPLATE = "mssql+pymssql://{username}:{password}@{hostname}:{port}?charset=utf8"
AI_BOOKS_SUMMARIES_TABLE = f'{AI_DATABASE_NAME}/AiBookSummaries'
AI_METADATA_TABLE = f'{AI_DATABASE_NAME}/AiMetadata'

AI_BOOK = f'{AI_DATABASE_NAME}/AiBook'
AI_Book_Type = f'{AI_DATABASE_NAME}/AiBookType'
AI_Book_Legal_Field = f'{AI_DATABASE_NAME}/AiBookLegalField'
AI_Book_Sub_Chapter = f'{AI_DATABASE_NAME}/AiBookSubChapter'
AI_Book_Chapter = f'{AI_DATABASE_NAME}/AiBookChapter'
AI_Book_Metadata = f'{AI_DATABASE_NAME}/AiBookMetadata'
AI_BOOK_Embed = f'{AI_DATABASE_NAME}/AiBookEmbed'
AI_Book_Json_Data = f'{AI_DATABASE_NAME}/AiBookJsonData'
AI_Book_Html_Pages = f'{AI_DATABASE_NAME}/AiBookHtmlPages'
AI_Entity_Relations = f'{AI_DATABASE_NAME}/AiEntityRelations'
AI_Chapter_Type = f'{AI_DATABASE_NAME}/AiChapterType'
AI_Sub_Chapter_Type = f'{AI_DATABASE_NAME}/AiSubChapterType'

PROCEDURES_DATA_TABLE = f'{AI_DATABASE_NAME}/procedures_data'
AI_LAW_METADATA_TABLE = f'{AI_DATABASE_NAME}/AiLawMetadata'
AI_LAW_QUESTION_TABLE = f'{AI_DATABASE_NAME}/AiLawQuestion'
AI_MASTER_CHUNK_TABLE = f'{AI_DATABASE_NAME}/AiMasterChunk'
AI_VERDICTS_METADATA_TABLE = f'{AI_DATABASE_NAME}/AiVerdictsMetadata'
AI_EMBED_TABLE = f'{AI_DATABASE_NAME}/AiEmbed'
AI_SUMMARY_TABLE = f'{AI_DATABASE_NAME}/AiSummary'
AI_RATIO_TABLE = f'{AI_DATABASE_NAME}/AiRatio'
AI_VERDICTS_BM25_PARAMS_TABLE = f'{AI_DATABASE_NAME}/VerdictsBM25ParamsJson'
AI_LAWS_BM25_PARAMS_TABLE = f'{AI_DATABASE_NAME}/LawsBM25ParamsJson'
AI_TAKDIN_FULL_TEXT_TABLE = f'{AI_DATABASE_NAME}/TakdinAiFullText'
RESOURCES_TEXT_TABLE = f'cmsKoldin_FullText/cms_mainTxt'
RESOURCES_MAIN_TABLE = f'{DATABASE_DBNAME}/cms_main'
TAKDIN_MAIN_TABLE = f'{DATABASE_DBNAME}/cms_mainTakdin'
DOCUMENTS_RELATED_TABLE = f'{DATABASE_DBNAME}/cms_DocumentRelated'
DOCUMENTS_TYPES_TABLE = f'{DATABASE_DBNAME}/cms_DocumentTypes'
DOCUMENTS_PROCESSING_TYPES_TABLE = f'{DATABASE_DBNAME}/cms_DocumentProcessingType'
DOCUMENTS_PD_TABLE = f'{DATABASE_DBNAME}/cms_DocumentPd'
DOCUMENTS_PD_PROCEDURE_TABLE = f'{DATABASE_DBNAME}/cms_DocumentPdProcedure'
DOCUMENTS_PD_DECISION_TYPE_TABLE = f'{DATABASE_DBNAME}/cms_DocumentPdDecisionType'
CRAWLER_HAS_DECISION_TABLE = f'{DATABASE_DBNAME}/crawler_temp_has_decisions'
LAW_DOC_TYPE = 1  # from MaagarId
VERDICT_DOC_TYPE = 2  # from MaagarId
Decision_DOC_TYPE = 45  # from MaagarId

REFERENCED_AND_RELATED_COUNT = 300
MAX_BM25_ROWS = 7

# Preprocess
MAX_WORDS_IN_LAW_CHUNK = 400
MAX_TOKEN_LENGTH_IN_VERDICT_CHUNK = 450
MIN_TOKEN_LENGTH_IN_VERDICT_CHUNK = 250
N_OVERLAP_SENTENCES_IN_VERDICT_CHUNK = 1
MAX_CHAR_COUNT_IN_VERDICT_PREFIX = 400
MAX_CHAR_COUNT_IN_VERDICT_SUFFIX = 600
MAX_TOKENS_PER_SENT = 200
DEFAULT_SENT_LEN_TOKENS = 100
DEFAULT_SENT_OVERLAP_TOKENS = 0  # no need for overlap since we add overlap in the sentence level

# JWT ValidateToken
# SHOULD_PREFORMED_JWT_VALIDATION_ON_CHAT_REQUESTS =False# os.environ.get('SHOULD_PREFORMED_JWT_VALIDATION_ON_CHAT_REQUESTS') 
# SHOULD_PREFORMED_JWT_VALIDATION_ON_SEARCH_REQUESTS =False #   os.environ.get('SHOULD_PREFORMED_JWT_VALIDATION_ON_SEARCH_REQUESTS') 
SHOULD_PREFORMED_JWT_VALIDATION_ON_CHAT_REQUESTS = os.environ.get(
    'SHOULD_PREFORMED_JWT_VALIDATION_ON_CHAT_REQUESTS').lower() in ("true", "yes",
                                                                    "1")  # os.environ.get('SHOULD_PREFORMED_JWT_VALIDATION_ON_CHAT_REQUESTS')
SHOULD_PREFORMED_JWT_VALIDATION_ON_SEARCH_REQUESTS = os.environ.get(
    'SHOULD_PREFORMED_JWT_VALIDATION_ON_SEARCH_REQUESTS').lower() in ("true", "yes",
                                                                      "1")  # os.environ.get('SHOULD_PREFORMED_JWT_VALIDATION_ON_SEARCH_REQUESTS')

# Sorted answers
SORTED_ANSWER = True

# JWT ValidateToken
VALIDATE_TOKEN_URL_TEMPLATE = os.environ.get('VALIDATE_TOKEN_URL_TEMPLATE')

# get-full-text from .Net
GET_FULL_TEXT_URL = os.environ.get('GET_FULL_TEXT_URL')  # fix the url to work with different env.
GET_PROCEDURE_TYPE_URL = os.environ.get('GET_PROCEDURE_TYPE_URL')

NAME_PREFIX_BAN_LIST = ['בדימוס', "בדימ'", 'הנשיאה', 'הנשיא', 'כבוד השופט', 'כבוד השופטת', 'כבוד הנשיא', 'כבוד הנשיאה',
                        "כב' השופט", "כב' השופטת", 'כבוד', "כב'", 'השופט', 'השופטת', 'והשופטים', 'אב"ד', '(אב"ד)',
                        'השופטים', 'אב בית הדין',
                        'אב בית-הדין', 'אב"דכב\'', 'בפני', 'לפני', 'בכירה', 'הבכירה', 'בכיר', 'הבכיר', 'הראשי',
                        'הראשית', 'נשיא', 'נשיאה', 'סגנית הנשיאה', 'סגן הנשיא', 'סגנית הנשיא', 'מ"מ הנשיא',
                        'ממלא מקום הנשיא', 'מ"מ הנשיאה',
                        'ממלאת מקום הנשיא', 'ממלאת מקום הנשיאה', 'סגן הנשיאה', 'סגן נשיא', 'סגן נשיאה', 'סגנית נשיאה',
                        'סגנית נשיא', 'הסגן נשיאה', 'הסגנית נשיאה', 'הסגן נשיא', 'הסגנית נשיא', "ס' נשיא", "ס' נשיאה",
                        'ס.נשיא', 'ס.נשיאה',
                        'ס נשיא', 'ס נשיאה', 'שופט', 'שופטת', 'בפועל', 'הרשם', 'הרשמת', 'המשנה לנשיא', 'המשנה לנשיאה',
                        'פרופסור', 'פרופ', 'פרופ.', "פרופ'", 'ד"ר', 'מר', 'גברת', 'בשם', 'המבקש', 'המבקשת', 'המשיב',
                        'המשיבה', 'מערער', 'מערערת',
                        'המערער', 'המערערת', 'העותר', 'העותרת', 'עו"ד', 'עורך דין', 'עורכת דין', 'נציג ציבור',
                        'נציגת ציבור', 'נציג הציבור', 'נציגת הציבור', '(עובדים)', '(מעסיק)', '(מעסיקים)', '(בדימוס)',
                        'בדימוס', 'נציג ציבור מעבידים',
                        'נציג ציבור עובדים', 'נציג.ציבור עובדים', 'נציג.ציבור מעבידים', 'סא"ל', 'סג"נ', 'סג"ן', "טור'",
                        'טר"ש', 'רב"ט', 'סמ"ר', 'רס"ל', 'רס"מ', 'רס"ם', 'רנ"מ', 'רנ"ם', 'רנ"ג', 'סג"מ', 'סג"ם', 'רס"נ',
                        'רס"ן', 'אל"מ', 'תא"ל',
                        'רא"ל', 'רב"צ', 'רב"ץ', 'קמ"ש', 'קר"פ', 'קר"ש', 'ממ"ק', 'סאל', 'סגנ']  # why here?

completion_list = ['\n\n', '\n', '<', '>', '\n</', '<summary', 'summary', '<context', '</context', '</', 'context',
                   '.</', 'summary>', '.summary']

MAX_TOTAL_TOKENS = 70000

VERSION = os.getenv('APP_VERSION', '0.0.37')

SEARCH_INDEX = 1

CHAT_CONTEXT_PREFIX = "_context"

# FIREBASE_CONF
# FIREBASE_CONF = json.loads(os.environ.get('FIREBASE_CONF'))
# TTL to redis
PRE_CHAT_CACHE_EXPIRATION = int(os.environ.get('PRE_CHAT_CACHE_EXPIRATION', 60 * 10))
POST_CHAT_CACHE_EXPIRATION = int(os.environ.get('POST_CHAT_CACHE_EXPIRATION', 60 * 60 * 24))

GET_STATISTICS = os.environ.get('GET_STATISTICS').lower() in ("true", "yes", "1")

ERROR_KEYS = []
PROCEDURE_LIST = []

MESSAGES_ERROR = {}

MIN_QUERY_LEN = 4

MAX_TOKENS_TO_LAW_QUESTIONS_MODEL = 10000

LOG_LEVEL = os.environ.get('LOG_LEVEL', 'WARNING')
##S3 bucket
S3_Bucket_Access_Key = os.environ.get('S3_Bucket_Access_Key')
S3_Bucket_Secret_Key = os.environ.get('S3_Bucket_Secret_Key')
S3_Region = os.environ.get('S3_Region')

##S3 bucket config
S3_BUCKET_CONFIG_NAME = os.environ.get('S3_BUCKET_CONFIG_NAME', "techdin-ml")

#
ENVIRONMENT = os.environ.get('ENVIRONMENT', 'dev')

PINECONE_PROTOCOL_HTTPS = True if os.environ.get('PINECONE_PROTOCOL_HTTPS', 'True') == 'True' else False
##etl
ETL_API = os.environ.get('ETL_API', "D9F77192-7C02-48DB-94D7-599A74233425")

## PineCone Filters
Book_Exam_Id = int(os.environ.get('BOOK_TYPE_EXAM', 4))
Book_Chapters_Ids_Filters = os.environ.get('BOOK_CHAPTERS_IDS_FILTERS', ['a54c3ded-850e-468b-94ad-69e3c9bb87b6',
                                                                         '10fe2d78-8087-4008-ad2b-e4dc114f5efe'])

# Define the Israel timezone
MAX_QUERY_LEN = os.getenv('MAX_QUERY_LEN', 20000)
