
## JWT POLICY
SHOULD_PREFORMED_JWT_VALIDATION_ON_CHAT_REQUESTS = True
SHOULD_PREFORMED_JWT_VALIDATION_ON_SEARCH_REQUESTS = True


#S3 BUCKET CONFIG
S3_Bucket_Access_Key = '********************'
S3_Bucket_Secret_Key = 'o4MUqQpDiC+v9ZL9nsouwdWmgGh8N24OxnZFhbwM'
S3_Region = 'il-central-1'

## Cohere Embedding
COHERE_API_KEY='tClcmdWf11JXK7I78bM44y7zUK3YMuGX0yYB0jEU'

# Claude key
ANTHROPIC_API_KEY= '************************************************************************************************************'
ANTHROPIC_RATIO_API_KEY = '************************************************************************************************************'
ANTHROPIC_NER_API_KEY = '************************************************************************************************************'
ANTHROPIC_MASTER_CHUNK_API = '************************************************************************************************************'

APP_VERSION = '0.0.37'

# Cohere API token
COHERE_EMBED_MODEL = 'embed-multilingual-light-v3.0'
COHERE_HF_TOKENIZER_NAME = "Cohere/Cohere-embed-multilingual-light-v3.0"

# PINCONE MAIN DB
PINECONE_ENVIRONMENT='eu-west1-gcp'
PINECONE_API_KEY_1='1c91ea02-4fe0-459e-a229-f5d9ed658117'
LAW_INDEX_NAME = 'law-qa'  # if you chnage those, you may need to also change the BM25 params file names (see below) - 'law-384-p1'
VERDICTS_INDEX_NAME = 'verdicts-qa'
MACHSHAVOT = 'machshavot2025-qa' #TODO update the env. to prod

# GEN
# Claude
CHAT_COMPLETION_MODEL =  "claude-3-5-sonnet-20240620" #"claude-3-haiku-20240307"
CHAT_COMPLETION_PREMIUM_MODEL = "claude-3-5-sonnet-20240620"
CHAT_COMPLETION_STANDARD_MODEL = "claude-3-5-haiku-20241022" # "claude-3-haiku-20240307"
CHAT_COMPLETION_RATIO_MODEL = "claude-3-5-haiku-20241022" # "claude-3-haiku-20240307""claude-3-haiku-20240307"
ANTHROPIC_MASTER_CHUNK_MODEL = "claude-3-5-haiku-20241022" # "claude-3-haiku-20240307""claude-3-haiku-20240307"

# Gemini
GEMINI_RATIO_MODEL = "gemini-1.5-flash-001"

# REDIS connection
REDIS_HOST =  "techdin-poc.dmt2tt.clustercfg.ilc1.cache.amazonaws.com"
REDIS_PORT = 6379
REDIS_DB = 0
REDIS_USE_SSL = False
REDIS_PASSWORD = ''
REDIS_USE_PASSWORD = False

# SQL
DATABASE_USERNAME = "app_service"
DATABASE_HOSTNAME = "***************"
DATABASE_PORT = 1433
DB_PASSWORD = 'Ai1234!ChangeMe2023'
DATABASE_DBNAME = "cmsHashavim"
AI_DATABASE_NAME = 'Techdin_QA'

# #JWT ValidateToken

VALIDATE_TOKEN_URL_TEMPLATE  = "https://web01-dev-devops-hashavim-co-il.azurewebsites.net/api/app/check-if-user-have-product/is-product-valid"

GET_FULL_TEXT_URL ="http://dnapi-dev-takdinai.devops.hashavim.co.il/get-full-text" #  dev
GET_PROCEDURE_TYPE_URL ="http://dnapi-qa-takdinai.devops.hashavim.co.il/get-procedure-types" #  dev



PRE_CHAT_CACHE_EXPIRATION = 600
POST_CHAT_CACHE_EXPIRATION = 86400

CHAT_CACHE_COMPRESS_LEVEL = 0

GET_STATISTICS = False

ENVIRONMENT='local'

LOG_LEVEL = 'INFO'

PINECONE_PROTOCOL_HTTPS = 'True'



EXCLUDE_ITEMS=["ethic-search","law-search"]

ETL_API='D9F77192-7C02-48DB-94D7-599A74233425'

###mongo varibles

MONGO_USER_NAME="app_service"
MONGO_PASSWORD="juX5w8GDFJfqONzB"
MONGO_URL=mongodb+srv://{0}:{1}@dev-cluster.dmwdv.mongodb.net/?retryWrites=true&w=majority&appName=dev-cluster

MONGO_DB_AUTOCOMPLETE="techdin-autocomplete"
MONGO_PROCEDURE_COLLECTION="procedure_collection"
MONGO_INDEX_AUTOCOMPLETE="case_number_autocomplete"

MONGO_DB_HISTORY="techdin-dev"
MONGO_CHAT_HISTORY_COLLECTION="chat_history"
MONGO_USERS_COLLECTION="users"
