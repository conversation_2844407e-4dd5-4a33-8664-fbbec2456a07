import datetime
import json
import queue
import threading
from itertools import islice
from time import sleep
from pinecone.grpc import PineconeGRPC as Pinecon<PERSON>

from pinecone_text.sparse import BM25Encoder
from sqlalchemy import create_engine
from sqlalchemy import text
from sqlalchemy.orm import Session
from sqlalchemy.orm import sessionmaker
from tqdm import tqdm

from app.middlewares.exceptions import verify_developers_choice
from data_ops.pinecone_func import get_sparse_embedding, prep_line_from_law_metadata, prep_line_from_verdict_metadata
from db_utils.sql_utils import get_all_cids
from configs import app_config as config
from configs.app_config import PINECONE_ENVIRONMENT
from middlewares.logging_utils import app_logger as logger

# Database connection configuration
DATABASE_URL_TEMPLATE = config.DATABASE_URL_TEMPLATE
DATABASE_NAME = config.AI_DATABASE_NAME  
DATABASE_HOSTNAME = config.DATABASE_HOSTNAME
DATABASE_PORT = config.DATABASE_PORT
DATABASE_USERNAME = config.DATABASE_USERNAME
DB_PASSWORD = config.DB_PASSWORD
sql_url = DATABASE_URL_TEMPLATE.format(
    username=DATABASE_USERNAME,
    password=DB_PASSWORD,
    hostname=DATABASE_HOSTNAME,
    port=DATABASE_PORT,
    dbname=DATABASE_NAME
)
engine = create_engine(sql_url)
SessionLocal = sessionmaker(bind=engine)

class HashvimSQLServer:
    def __init__(self, db: Session):
        self.db = db

    def execute(self, query, params=None):
        try:
            result = self.db.execute(text(query), params)
            # Add monitoring and management code here
            logger.info(f"Executed query: {query}")
            return result
        except Exception as e:
            logger.error(f"Error executing query: {query}")
            logger.error(f"Error details: {str(e)}")
            raise e

def get_record_ids_by_txt_id_sql(txt_id: str, db: Session):
    query = "SELECT [cId] FROM [cmsTakdinAI].[dbo].[AiEmbed] WHERE txtId = :txt_id"
    hashvim_sql_server = HashvimSQLServer(db)
    result = hashvim_sql_server.execute(query, {"txt_id": txt_id}).fetchall()
    return [row[0] for row in result]



def datetime_serializer(obj):
    if isinstance(obj, datetime.datetime):
        return obj.isoformat()
    elif isinstance(obj, datetime.date):
        return obj.isoformat()
    raise TypeError(f"Type {type(obj)} not serializable")

def get_dump_name(index_name, ext='jsonl'):
    return f'{index_name}_dump.{ext}'

def create_corpus_from_index_dump(index_name):
    prep_line = prep_line_from_law_metadata if index_name == config.LAW_INDEX_NAME else prep_line_from_verdict_metadata
    with open(get_dump_name(index_name), 'r', encoding='utf-8') as jsonl_file:
        with open(get_dump_name(index_name, ext='txt'), 'a', encoding='utf-8') as txt_file:
            for line in jsonl_file:
                data = json.loads(line)
                assert len(data) == 1
                data = list(data.values())[0]
                result = prep_line(data).replace('\n', ' ')  + '\n'
                txt_file.write(result)

def fit_bm25_encoder_from_corpus(index_name, bm25_name):
    with open(get_dump_name(index_name, ext='txt'), 'r', encoding='utf-8') as txt_file:
        corpus = txt_file.readlines()
    bm25 = BM25Encoder(k1=0, b=0)
    bm25.fit(corpus)
    bm25.dump(bm25_name)

def read_n_lines(file, n):
    return list(islice(file, n))

def update_pinecone_with_sparse_encodings(index_name, n_consumers=20):
    MAX_QUEUE_SIZE = 1000
    MAX_ATTEMPTS = 3
    assert n_consumers * 2 < MAX_QUEUE_SIZE
    is_law = index_name == config.LAW_INDEX_NAME
    # pinecone.init(api_key=os.environ['PINECONE_API_KEY_1'], environment=PINECONE_ENVIRONMENT)
    pinecone= Pinecone(api_key=os.environ['PINECONE_API_KEY_1'])
    index = pinecone.Index(index_name=index_name)
    embedding_queue = queue.Queue(maxsize=MAX_QUEUE_SIZE)
    update_queue = queue.Queue()

    def producer():
        with open(get_dump_name(index_name), 'r', encoding='utf-8') as jsonl_file:
            while True:
                jsonl_lines = read_n_lines(jsonl_file, 100)
                if not jsonl_lines:
                    break
                for line in jsonl_lines:
                    item = json.loads(line)
                    assert len(item) == 1
                    for txt_id, val in item.items():
                        embedding = get_sparse_embedding(is_law, val, enc_type='document')
                        if embedding is None:
                            continue
                        while embedding_queue.qsize() >= MAX_QUEUE_SIZE - n_consumers-1:
                            sleep(0.5)
                        embedding_queue.put((txt_id, embedding, 0))
        embedding_queue.put(None)

    def consumer(embedding_queue):
        db = SessionLocal()
        pbar = tqdm(total=None, unit='item', desc='Updating Pinecone')
        try:
            while True:
                item = embedding_queue.get()
                if item is None:
                    break
                txt_id, embedding, attempts = item
                try:
                    if is_law:
                        index.update(id=txt_id, sparse_values=embedding)
                    else:
                        record_ids = get_record_ids_by_txt_id_sql(txt_id, db)
                        for record_id in record_ids:
                            index.update(id=record_id, sparse_values=embedding)
                except Exception as e:
                    logger.error(f"Error updating {txt_id}: {e}")
                    if attempts == MAX_ATTEMPTS:
                        logger.warning(f"----> {txt_id} has reached max attempts so it will not be attempted again")
                    embedding_queue.put((txt_id, embedding, attempts+1))
                pbar.update(1)
        finally:
            db.close()
            pbar.close()
        embedding_queue.put(None)
        logger.info('Consumer is done!')

    producer_thread = threading.Thread(target=producer)
    producer_thread.start()
    consumer_threads = []
    for _ in range(n_consumers):
        t = threading.Thread(target=consumer, args=(embedding_queue,))
        t.start()
        consumer_threads.append(t)
    producer_thread.join()
    logger.info('Finished joining the producer')
    for t in consumer_threads:
        t.join()
        logger.info('Finished joining a consumer')
    logger.info('Done updating pinecone')

async def train_bm25_encoder(maagar_id, run_id, index_name, dump=False, create_corpus=False, fit_bm25=False, update_pinecone=False):
    from scripts.dump_from_sql_for_bm25 import verdict_dump_sql_to_json, law_dump_sql_to_json
    from utils.cache_db import get_redis
    from db_utils.sql_utils import insert_to_bm25_with_fifo

    redis_client = await get_redis()
    if dump:
        assert create_corpus, 'when creating a new dump, must create the corpus'
    if dump:
        print('You are about re-create the dump file, are you sure? ')
        verify_developers_choice()
        logger.info(f'Dumping data for the {index_name} index')
        # dump_index(index_name)
        verdict_dump_sql_to_json(index_name) if maagar_id == 2 else law_dump_sql_to_json(index_name)
        logger.info(f'Done dumping data for the {index_name} index')
    assert os.path.exists(get_dump_name(index_name)), 'Missing dump file, run again to create it'
    if create_corpus:
        logger.info(f'Creating corpus for the {index_name} index')
        create_corpus_from_index_dump(index_name)
        logger.info(f'Done creating corpus for the {index_name} index')
    assert os.path.exists(get_dump_name(index_name, ext='txt')), 'Missing corpus file, run again to create it'
    bm25_name = config.BM25_LAW_PARAMS if index_name == config.LAW_INDEX_NAME else config.BM25_VERDICT_PARAMS
    if fit_bm25:
        print(f'You are about to update bm25 params for {index_name}, please verify you are sure about it')
        verify_developers_choice()
        logger.info(f'Creating bm25 encoder for the {index_name} index')
        fit_bm25_encoder_from_corpus(index_name, bm25_name)
        logger.info(f'Done creating bm25 encoder for the {index_name} index')
    assert os.path.exists(bm25_name), 'Missing bm25 params file, run again to train it'
    if update_pinecone:
        print('You are about to update pinecone, please verify you are sure about it')
        verify_developers_choice()
        logger.info(f'Updating sparse vectors for the {index_name} index')
        update_pinecone_with_sparse_encodings(index_name)
        logger.info(f'Updating sparse vectors for the {index_name} index')
    os.remove(get_dump_name(index_name, ext='jsonl'))
    os.remove(get_dump_name(index_name, ext='txt'))
    
    with open(bm25_name, 'r') as file:
        json_data = json.load(file)        
    json_string = json.dumps(json_data)
    new_row_data =  {'runId': run_id, 'bm25': json_string, 'updatedAt' : datetime.date.today()}
    insert_to_bm25_with_fifo(maagar_id, new_row_data)

    message = {'run_id': run_id, 'maagarId' : maagar_id}
    await redis_client.publish('update_bm25', json.dumps(message))

if __name__ == '__main__':
    import os
    from dotenv import load_dotenv
    load_dotenv(os.path.join('configs', '.env'))
    train_bm25_encoder(config.VERDICTS_INDEX_NAME, dump=False, create_corpus=False, fit_bm25=False, update_pinecone=True)
    train_bm25_encoder(config.LAW_INDEX_NAME, dump=False, create_corpus=False, fit_bm25=False, update_pinecone=False)

    

