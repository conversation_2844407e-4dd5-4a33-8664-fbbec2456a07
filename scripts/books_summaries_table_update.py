# import sys
# sys.path.append('C:/Users/<USER>/code/AI-Development')
# import os
# from typing import List
# import pandas as pd
# from sqlalchemy.orm import sessionmaker
# from sqlalchemy import create_engine
# from app.models import AiBookSummaries
# import configs.app_config as config
#
# DATABASE_URL_TEMPLATE = config.DATABASE_URL_TEMPLATE
# DATABASE_DBNAME = config.AI_DATABASE_NAME
# DATABASE_HOSTNAME = config.DATABASE_HOSTNAME
# DATABASE_PORT = config.DATABASE_PORT
# DATABASE_USERNAME = config.DATABASE_USERNAME
# DEFAULT_DB_PASSWORD = config.DEFAULT_DB_PASSWORD
#
# import ast
#
# # Path to your CSV file
# # csv_file = BOOKS_SUMMARIES_FILE
#
# # Read data from the CSV file into a DataFrame with "," as the delimiter
# data = pd.read_csv(csv_file, delimiter=',')
# data = data.fillna('')
#
# def format_procedure_number(procedure):
#     if procedure:
#         try:
#             procedure_list = ast.literal_eval(procedure)
#         except (ValueError, SyntaxError):
#             procedure_list = [procedure]
#
#         formatted_procedure_list = []
#         for item in procedure_list:
#             # Assuming the format is always 'prefix code', split and reverse
#             parts = item.split(' ')
#             if len(parts) == 2:
#                 formatted_item = f'"{parts[1]} {parts[0]}"'  # Reverse the order
#             else:
#                 formatted_item = f'"{item}"'  # Keep original if not in 'prefix code' format
#             formatted_procedure_list.append(formatted_item)
#
#         return '[' + ', '.join(formatted_procedure_list) + ']'
#     else:
#         return '[]'
#
# # Check if 'procedures' column exists in the DataFrame
# if 'procedures' in data.columns:
#     # Apply the format_procedure_number function to the 'procedures' column
#     data['procedures'] = data['procedures'].apply(format_procedure_number)
#
# # Add a hyphen at the beginning of the 'cid' values to generate 'txtId' values
# data['txtId'] = '-' + data['cid'].astype(str).str.replace('-', '')
#
# # Establish a database connection
# sql_url = DATABASE_URL_TEMPLATE.format(username=DATABASE_USERNAME, password=DEFAULT_DB_PASSWORD, hostname=DATABASE_HOSTNAME, port=DATABASE_PORT, dbname=DATABASE_DBNAME)
# engine = create_engine(sql_url)
# SessionLocal = sessionmaker(bind=engine)
#
#
# # ...
#
# if __name__ == '__main__':
#     # Iterate through the rows of the DataFrame and insert each row into the SQL table
#     with SessionLocal() as session:
#         for index, row in data.iterrows():
#             try:
#                 # Modify txtId to remove the middle hyphen and add 5 zeros
#                 txtId = row.get('cid', None)
#                 if txtId:
#                     parts = txtId.split('-')
#                     if len(parts) == 2:
#                         txtId = '-' + parts[0] + '00000' + parts[1]  # Remove middle hyphen and add 5 zeros
#                     else:
#                         txtId = '-' + txtId + '00000'  # Add 5 zeros if there is no middle hyphen
#
#                 ai_book_summary = AiBookSummaries(
#                     txtId=txtId,
#                     legalField=row.get('legal_field', None),
#                     chapter=row.get('chapter', None),
#                     subject=row.get('sub_chapter', row.get('subject', None)),
#                     verdictTitle=row.get('verdictTitle', None),
#                     text=row.get('clean_content', None),
#                     nTokens=row.get('nTokens', None),
#                     procedures=row.get('procedures', None),
#                     source=row.get('source', None)
#                 )
#                 session.add(ai_book_summary)
#             except Exception as e:
#                 print(f"Error inserting row {index}: {e}")
#                 # Optionally, decide whether to continue or halt on error
#                 continue
#
#         session.commit()
