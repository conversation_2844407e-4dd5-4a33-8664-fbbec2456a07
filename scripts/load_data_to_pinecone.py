
from time import time

import models
from api.dependencies.relational_db import get_sql_session
from configs import app_config as config
import requests
import json


def get_all_txt_ids_from_maagar(maagar_id, sql_session=None, batch_size=1000, excluded_inserted_ids=True):
    close_session_at_end = sql_session is None
    if sql_session is None:
        sql_session = get_sql_session()

    if not excluded_inserted_ids:
        # Query to get the cIds based on maagarId = 1
        query = sql_session.query( models.ResourcesMain.txtId).filter( models.ResourcesMain.maagarId == maagar_id).order_by( models.ResourcesMain.txtId)
    else:
        # Query ResourcesMain with an outer join on AiEmbed
        query = (
            sql_session.query( models.ResourcesMain.txtId)
            .outerjoin( models.AiEmbed, models.ResourcesMain.txtId == models.AiEmbed.txtId)
            .filter( models.AiEmbed.txtId == None)
            .filter( models.ResourcesMain.maagarId == maagar_id)
            .order_by( models.ResourcesMain.txtId)
        )
    offset = 0

    while True:
        cids = query.offset(offset).limit(batch_size).all()
        if not cids:
            break
        yield [cid_tuple[0] for cid_tuple in cids]

        offset += batch_size

    if close_session_at_end:
        sql_session.close()


if __name__ == '__main__':
    import os
    from dotenv import load_dotenv
    load_dotenv(os.path.join('configs', '.env'))

    url = "http://127.0.0.1:8000/embed"

    total_inserted = 0
    # assuming the app is running on localhost:8000
    t = time()
    n_to_insert = 1000
    for txt_ids in get_all_txt_ids_from_maagar(config.LAW_DOC_TYPE, excluded_inserted_ids=True, batch_size=50):

        payload = json.dumps({"txtId": txt_ids})
        headers = {'Content-Type': 'application/json'}
        response = requests.request("POST", url, headers=headers, data=payload)

        assert response.status_code == 200
        total_inserted += len(txt_ids)  # note some have failed.
        print(f'Finished inserting {total_inserted} so far (last batch took {time()-t:.2f} seconds)')
        t = time()

        if total_inserted >= n_to_insert:
            break