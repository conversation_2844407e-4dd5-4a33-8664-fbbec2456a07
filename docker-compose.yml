version: "3.9"
services:
  service_8000:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    networks:
      - app-network
    environment:
      - SERVICE_NAME=service_8000
      - LOG_LEVEL=WARNING
      - WORKERS=1
      - RUNNING_IN_DOCKER=False

  service_8001:
    build:
      context: .
      dockerfile: Dockerfile_Lambda
    ports:
      - "8001:8001"
    networks:
      - app-network
    environment:
      - SERVICE_NAME=service_8001
      - LOG_LEVEL=INFO
      - WORKERS=1

networks:
  app-network:
    driver: bridge
