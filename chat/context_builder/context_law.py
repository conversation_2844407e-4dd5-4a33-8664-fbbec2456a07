from typing import Dict, Any

from chat.context_builder import Context<PERSON>uilder
from middlewares.logging_utils import app_logger as logger
from utils.pre_chat_dto import SearchTypeEnum, DataContext


def create_document_metadata(result: Dict[str, Any]) -> str:
    """
    Create metadata for a document.

    Args:
        result: A single search result

    Returns:
        Dict containing metadata
    """

    # Get metadata from the result
    section_numbers_str = ','.join(str(num) for num in result.get('section_numbers', []))

    return (
        f"<section_number>{section_numbers_str}</section_number>\n"
        f"<chapter_title>{result.get('chapter_title', '')}</chapter_title>\n"
        f"<section_title>{result.get('provision_title', '')}</section_title>\n"
        f"amendment_information>{result.get('amendment_information', '')}</amendment_information>\n"
    )


class LawContextBuilder(ContextBuilder):
    """
    Context builder for law domain.
    Processes law data and formats it for Anthropic with citation support.
    """

    def __init__(self, data_context: DataContext, domain: str
                 ):
        """
        Initialize the verdict context builder.

        Args:
            data_context: DataContext object containing the search results
            domain: The domain (should be "verdict")
        """
        super().__init__(data_context, domain)




    def build_context(self, search_type: SearchTypeEnum = None) -> tuple[list[Any], dict[Any, Any]]:
        """
        Build the context for law domain.

        Returns:
            Dict containing the built context and metadata
        """

        # Sort search results by txt_id and then by sequence

        # Process each search result

        for result in self.search_results:
            self._process_search_result(result)

        return self.formatted_documents, self.mapper_cid

    def _process_search_result(self, result: Dict[str, Any]) -> None:
        """
        Process a single search result and add it to the formatted documents.

        Args:
            result: A single search result
        """
        try:
            txt_id = result.get("txt_id", "")
            chunk_text = result.get("content", "")
            chunk_id = result.get("cid", "")
            title = result.get("title", "")
            self.doc_index += 1
            self.txt_id_to_doc_index[txt_id] = self.doc_index
            if title:
                if 'section_numbers' in result.keys():
                    try:
                        section_numbers = result['section_numbers']
                        if isinstance(section_numbers, list):
                            title=f"{' , '.join(map(str, section_numbers))} {title}"
                        elif isinstance(section_numbers, str):
                            title = f"{section_numbers} {title}"
                    except Exception as e:
                        logger.error(f"Error processing section numbers: {e}")

            else:
                title="חוקים ותקנות"
            # Create metadata for this document
            metadata = create_document_metadata(result)
            # Create a new document
            document = {
                "type": "document",
                "source": {
                    "type": "text",
                    "media_type": "text/plain",
                    "data": chunk_text
                },
                "title": title[:480],
                "context": metadata,
                "citations": {"enabled": True}
            }
            self.mapper_cid[self.doc_index] = {
                "txt_id": txt_id,
                "master_chunk": False,
                "cid": chunk_id,
                "cited": False

            }
            self.formatted_documents.append(document)

        except Exception as e:
            logger.error(f"Error processing search result: {e}")
