import json
from typing import Any

from chat.context_builder import <PERSON><PERSON><PERSON><PERSON><PERSON>
from utils.pre_chat_dto import SearchEnum, DataContext


class MachshavotContextBuilder(ContextBuilder):
    """
    Context builder for machshavot domain.
    Processes machshavot data and formats it for Anthropic with citation support.
    """

    def __init__(self, data_context: DataContext, domain: str
                 ):
        """
        Initialize the verdict context builder.

        Args:
            data_context: DataContext object containing the search results
            domain: The domain (should be "verdict")
        """
        super().__init__(data_context, domain)

    def build_trainer_context(self, content, prompt_template: str):
        pass
        # content_json=json.loads(content)
        # answer=content_json["answer"]
        # student_answer=content_json["student_answer"]
        # prompt=prompt_template.format(book_answer=answer,student_answer=student_answer)
        # return prompt

    def build_context(self, search_type: SearchEnum = None) -> tuple[list[Any], dict[Any, Any]]:
        """
        Build the context for machshavot domain.

        Returns:
            Dict containing the built context and metadata
        """

        # Process each sub_chapter

        for sub_chapter in self.search_results:
            self.doc_index += 1

            # Create metadata for this document
            book_name = f'{sub_chapter.get("legal_field", "")} - {sub_chapter.get("book_type", "")}'
            chapter_title = sub_chapter.get("chapter_title", "")
            sub_chapter_title = sub_chapter.get("sub_chapter_title", "")
            metadata = {
                "book_name": book_name,
                "chapter_title": chapter_title,
                "sub_chapter_title": sub_chapter_title,
            }
            title = sub_chapter_title or chapter_title or book_name
            title=title[:480]

            # Create a new document
            document = {
                "type": "document",
                "source": {
                    "type": "text",
                    "media_type": "text/plain",
                    "data": sub_chapter.get("content", ""),
                },
                "title":title,
                "context": str(metadata),
                "citations": {"enabled": True}
            }
            # Map the document ID to its index
            self.mapper_cid[self.doc_index] = {
                "txt_id": sub_chapter.get("sub_chapter_id", ""),
                "cid": sub_chapter.get("sub_chapter_id", ""),
                "master_chunk": False,
                "cited": False
            }

            self.formatted_documents.append(document)

        return self.formatted_documents, self.mapper_cid
