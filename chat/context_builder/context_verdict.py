from typing import Dict, Any

from chat.context_builder import ContextBuilder
from middlewares.logging_utils import app_logger as logger
from utils.pre_chat_dto import Chat<PERSON>num, DataContext


def format_document_context(metadata: Dict[str, Any]) -> str:
    """
    Format document context for Anthropic.

    Args:
        metadata: Document metadata

    Returns:
        Formatted context string
    """
    procedures = metadata.get("procedures", [])
    procedure_str = ", ".join(procedures[:7]) if procedures else ""

    judges = metadata.get("judges", [])
    judges_str = ", ".join(judges) if judges else ""

    prosecutors = metadata.get("prosecutors", [])
    prosecutors_str = ", ".join(prosecutors[:7]) if prosecutors else ""

    defenders = metadata.get("defenders", [])
    defenders_str = ", ".join(defenders[:7]) if defenders else ""

    representatives = metadata.get("representatives", [])
    representatives_str = ", ".join(representatives[:7]) if representatives else ""

    return (
        f"<case_number>{procedure_str}</case_number>\n"
        f"<court_level>{metadata.get('court_name', '')}</court_level>\n"
        f"<Judges>{judges_str}</Judges>\n"
        f"<decision_type>{metadata.get('decision_name', '')}</decision_type>\n"
        f"<plaintiff>{prosecutors_str}</plaintiff>\n"
        f"<defendant>{defenders_str}</defendant>\n"
        f"<court_location>{metadata.get('location', '')}</court_location>\n"
        f"<representatives>{representatives_str}</representatives>\n"
        f"<referrer_count>{metadata.get('ref_count', 0)}</referrer_count>\n"
    )


class VerdictContextBuilder(ContextBuilder):
    """
    Context builder for verdict domain.
    Processes verdict data and formats it for Anthropic with citation support.
    """

    def append_master_chunk(self, master_chunk: Dict[str, str], title: str, txt_id: str) -> None:
        decision = master_chunk.get("decsion", "")
        court_ruling = master_chunk.get("court_ruling", "")
        data_parts = {
            f"<final_decision> {decision}</final_decision>",
            f"<court_ruling> {court_ruling}</court_ruling>",
        }

        content_text = "\n".join([part for part in data_parts if part.strip()])
        if not content_text:
            return
        document = {
            "type": "document",
            "source": {
                "type": "text",
                "media_type": "text/plain",
                "data": content_text
            },
            "title": title,
            "context": "Summary of the verdict",
            "citations": {"enabled": True}
        }

        self.doc_index += 1
        self.mapper_cid[self.doc_index] = {
            "txt_id": txt_id,
            "cid": -1,
            "master_chunk": True,
            "cid_offsets": []  # אופציונלי, לשמירה על אחידות
        }

        self.formatted_documents.append(document)

    def __init__(self, data_context: DataContext, domain: str
                 ):
        """
        Initialize the verdict context builder.

        Args:
            data_context: DataContext object containing the search results
            domain: The domain (should be "verdict")
        """
        super().__init__(data_context, domain)

    def _specific_document_context(self):
        result = self.search_results[0]
        txt_id = result.get("txt_id", "")
        chunk_text = self.full_text_content
        chunk_id = result.get("cid", "")
        title = result.get("title", "כותרת פסק דין")
        title = title[:480]

        self.doc_index += 1

        # Create metadata for this document
        metadata = format_document_context(result)

        master_chunk = self.master_chunks.get(str(txt_id), "")
        if master_chunk:
            self.append_master_chunk(master_chunk, title, txt_id)

        document = {
            "type": "document",
            "source": {
                "type": "text",
                "media_type": "text/plain",
                "data": f"{chunk_text}"
            },
            "title": title,
            "context": metadata,
            "citations": {"enabled": True}
        }
        self.mapper_cid[self.doc_index] = {
            "txt_id": txt_id,
            "cid": chunk_id,
            "master_chunk": False,
            "cited": False,

        }

        self.txt_id_to_doc_index[txt_id] = [self.doc_index]
        self.formatted_documents.append(document)

    def union_txt_ids_context(self):
        """
        Process the search results and build the context for verdict domain.

        Returns:
            Dict containing the built context and metadata
        """
        txt_id_offsets = {}  # Offset based on printed data_string length

        for result in self.search_results:
            txt_id = result.get("txt_id", "")
            chunk_text = result.get("content", "")
            chunk_id = result.get("cid", "")
            title = result.get("title", "") or "כותרת פסק דין"
            title=title[:480]
            metadata = format_document_context(result)

            # Text that will actually be printed
            prefix_chunk_text = f"|| "
            suffix_chunk_text = "||"
            data_string = prefix_chunk_text + chunk_text + suffix_chunk_text

            # Initialize offset tracker if first time
            if txt_id not in txt_id_offsets:
                txt_id_offsets[txt_id] = 0

            start_offset = txt_id_offsets[txt_id]
            end_offset = start_offset + len(data_string)
            if txt_id not in self.txt_id_to_doc_index:
                self.doc_index += 1
                self.txt_id_to_doc_index[txt_id] = self.doc_index

                document = {
                    "type": "document",
                    "source": {
                        "type": "text",
                        "media_type": "text/plain",
                        "data": data_string
                    },
                    "title": title,
                    "context": metadata,
                    "citations": {"enabled": True}
                }

                self.formatted_documents.append(document)

                self.mapper_cid[self.doc_index] = {
                    "txt_id": txt_id,
                    "cid_offsets": [
                        {
                            "cid": chunk_id,
                            "start_offset": start_offset,
                            "end_offset": end_offset
                        }
                    ],
                    "master_chunk": False,
                    "cited": False

                }
                txt_id_offsets[txt_id] = end_offset
                master_chunk = self.master_chunks.get(str(txt_id), "")
                if master_chunk:
                    self.append_master_chunk(master_chunk, title, txt_id)

            else:
                doc_index = self.txt_id_to_doc_index[txt_id]
                self.formatted_documents[doc_index]["source"]["data"] += data_string

                self.mapper_cid[doc_index]["cid_offsets"].append({
                    "cid": chunk_id,
                    "start_offset": start_offset,
                    "end_offset": end_offset
                })

                txt_id_offsets[txt_id] = end_offset

    def build_context(self, chat_type: ChatEnum = None) -> tuple[list[Any], dict[Any, Any]]:
        """
        Build the context for verdict domain.

        Returns:
            Dict containing the built context and metadata
        """
        # Sort search results by txt_id and then by sequence

        # Get all unique txt_ids for master chunks

        # Process each search result
        if chat_type == ChatEnum.specific_document and self.data_type == "full_document":
            self._specific_document_context()
        else:
            self.search_results = sorted(self.search_results, key=lambda x: (x["txt_id"], x["chunk_index"]))

            self.union_txt_ids_context()

            # for result in self.search_results:
            #     self._process_search_result(result)

        return self.formatted_documents, self.mapper_cid

    # def _process_search_result(self, result: Dict[str, Any]) -> None:
    #     """
    #     Process a single search result and add it to the formatted documents.
    #
    #     Args:
    #         result: A single search result
    #     """
    #     try:
    #         txt_id = result.get("txt_id", "")
    #         chunk_text = result.get("content", "")
    #         chunk_id = result.get("cid", "")
    #         title = result.get("title", "") or "כותרת פסק דין"
    #
    #         self.doc_index += 1
    #
    #         # Create metadata for this document
    #         metadata = format_document_context(result)
    #
    #         master_chunk = self.master_chunks.get(str(txt_id), "")
    #         if master_chunk:
    #             self.append_master_chunk(master_chunk, title, txt_id)
    #         document = {
    #             "type": "document",
    #             "source": {
    #                 "type": "text",
    #                 "media_type": "text/plain",
    #                 "data": f"{chunk_text}"
    #             },
    #             "title": title[:480],
    #             "context": metadata,
    #             "citations": {"enabled": True}
    #         }
    #         self.mapper_cid[self.doc_index] = {
    #             "txt_id": txt_id,
    #             "cid": chunk_id,
    #             "master_chunk": False,
    #             "cited": False,
    #
    #         }
    #         if txt_id in self.txt_id_to_doc_index:
    #             self.txt_id_to_doc_index[txt_id].append(self.doc_index)
    #         else:
    #             self.txt_id_to_doc_index[txt_id] = [self.doc_index]
    #         self.formatted_documents.append(document)
    #
    #     except Exception as e:
    #         logger.error(f"Error processing search result: {e}")
