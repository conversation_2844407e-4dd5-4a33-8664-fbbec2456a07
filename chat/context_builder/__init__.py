import copy
from abc import ABC, abstractmethod

from ai_models import citation_prompts as citation_prompts
from redis_db.redis_schema import RedisSchema
from utils.pre_chat_dto import Chat<PERSON>num, DataContext


class ContextBuilder(ABC):
    """
    Base abstract class for all context builders.
    Each domain-specific context builder should inherit from this class.
    """

    @abstractmethod
    def __init__(self, data_context: DataContext, domain: str):
        """
        Initialize the context builder with search results and domain.

        Args:
            search_results: List of search results from the search engine
            domain: The domain (verdict, law, machshavot)
        """
        self.domain = domain
        self.doc_index = -1
        self.mapper_cid = {}
        self.formatted_documents = []
        self.txt_id_to_doc_index = {}

        self.search_results = data_context.data_raw
        self.data_type = data_context.type
        self.full_text_content = data_context.full_text_content
        self.master_chunks = data_context.master_chunks

    def build_trainer_context(self, content, prompt_template: str):
        raise NotImplementedError("This method should be overridden in subclasses")

    @abstractmethod
    def build_context(self, chat_type: ChatEnum = None):
        pass


class ContextBuilderFactory:
    """
    Factory class to create the appropriate context builder based on the domain.
    """

    @staticmethod
    def get_builder(data_context: DataContext, domain: str) -> ContextBuilder:

        """
        Get the appropriate context builder based on the domain.

        Args:
            data_context: DataContext object containing the search results
            domain: The domain (verdict, law, machshavot)


        Returns:
            An instance of the appropriate context builder
        """
        from chat.context_builder.context_verdict import VerdictContextBuilder
        from chat.context_builder.context_law import LawContextBuilder
        from chat.context_builder.context_machshavot import MachshavotContextBuilder

        if domain == "verdict" or domain == "ethics":
            return VerdictContextBuilder(data_context, domain)
        elif domain == "law":
            return LawContextBuilder(data_context, domain)
        elif domain == "machshavot":
            return MachshavotContextBuilder(data_context, domain)
        else:
            raise ValueError(f"Unknown domain: {domain}")


PROMPT_NAMES = [
    k for k in vars(citation_prompts).keys()
    if k.isupper() and not k.startswith("__")
]


def get_prompt(name: str):
    return getattr(citation_prompts, name, None)


def build_conversation(full_conversation, conversation, prompt, session_index, citations):
    if prompt:
        prompt_text = get_prompt(prompt)
        if prompt_text:
            full_conversation.append({"role": "user", "content": prompt_text})

    for conv in conversation:
        if conv.action.value > 0:
            new_conv = {"role": conv.role.value, "content": copy.deepcopy(conv.content.strip())}
            if conv.prompt:
                prompt_text = get_prompt(conv.prompt)
                if prompt_text:
                    new_conv["content"] += prompt_text
            start_index, end_index = conv.citations_index if conv.citations_index else (0, 0)
            if conv.role == "assistant" and conv.action.value == 2 and end_index > start_index:
                new_conv["content"] += "\n\n" + "מקורות: " + "\n" + "\n".join(
                    [f"{cid['document_title']} - {cid['cited_text']}" for cid in citations[start_index:end_index + 1]])

            full_conversation.append(new_conv)

    return full_conversation


def build_context_for_anthropic(chat_data: RedisSchema, chat_type: ChatEnum):
    # Use context builder to build context
    domain = chat_data.domain
    prompt_template = chat_data.model_settings.prompt_template
    conversation_raw = chat_data.conversation
    session_index = chat_data.chat_settings.session_index
    citations = chat_data.citations

    conversation_data = []
    mapper_cid_id = {}
    context_builder = ContextBuilderFactory.get_builder(chat_data.data, domain)

    if chat_type != ChatEnum.trainer:
        formatted_docs, mapper_cid_id = context_builder.build_context(chat_type)
        if not formatted_docs or not mapper_cid_id:
            raise Exception('NO_RESULTS_FOUND')
        conversation_data.append({"role": "user", "content": formatted_docs})

    full_conversation = build_conversation(conversation_data, conversation_raw, prompt_template, session_index,
                                           citations)

    return full_conversation, mapper_cid_id
